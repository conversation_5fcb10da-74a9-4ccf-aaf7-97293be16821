(()=>{var t={606:t=>{var e=-1,n=1,r=0;function i(t,g,b,m,v){if(t===g)return t?[[r,t]]:[];if(null!=b){var w=function(t,e,n){var r="number"==typeof n?{index:n,length:0}:n.oldRange,i="number"==typeof n?null:n.newRange,s=t.length,o=e.length;if(0===r.length&&(null===i||0===i.length)){var l=r.index,a=t.slice(0,l),c=t.slice(l),u=i?i.index:null,h=l+o-s;if((null===u||u===h)&&!(h<0||h>o)){var d=e.slice(0,h);if((g=e.slice(h))===c){var f=Math.min(l,h);if((m=a.slice(0,f))===(w=d.slice(0,f)))return y(m,a.slice(f),d.slice(f),c)}}if(null===u||u===l){var p=l,g=(d=e.slice(0,p),e.slice(p));if(d===a){var b=Math.min(s-p,o-p);if((v=c.slice(c.length-b))===(x=g.slice(g.length-b)))return y(a,c.slice(0,c.length-b),g.slice(0,g.length-b),v)}}}if(r.length>0&&i&&0===i.length){var m=t.slice(0,r.index),v=t.slice(r.index+r.length);if(!(o<(f=m.length)+(b=v.length))){var w=e.slice(0,f),x=e.slice(o-b);if(m===w&&v===x)return y(m,t.slice(f,s-b),e.slice(f,o-b),v)}}return null}(t,g,b);if(w)return w}var x=o(t,g),N=t.substring(0,x);x=a(t=t.substring(x),g=g.substring(x));var k=t.substring(t.length-x),A=function(t,l){var c;if(!t)return[[n,l]];if(!l)return[[e,t]];var u=t.length>l.length?t:l,h=t.length>l.length?l:t,d=u.indexOf(h);if(-1!==d)return c=[[n,u.substring(0,d)],[r,h],[n,u.substring(d+h.length)]],t.length>l.length&&(c[0][0]=c[2][0]=e),c;if(1===h.length)return[[e,t],[n,l]];var f=function(t,e){var n=t.length>e.length?t:e,r=t.length>e.length?e:t;if(n.length<4||2*r.length<n.length)return null;function i(t,e,n){for(var r,i,s,l,c=t.substring(n,n+Math.floor(t.length/4)),u=-1,h="";-1!==(u=e.indexOf(c,u+1));){var d=o(t.substring(n),e.substring(u)),f=a(t.substring(0,n),e.substring(0,u));h.length<f+d&&(h=e.substring(u-f,u)+e.substring(u,u+d),r=t.substring(0,n-f),i=t.substring(n+d),s=e.substring(0,u-f),l=e.substring(u+d))}return 2*h.length>=t.length?[r,i,s,l,h]:null}var s,l,c,u,h,d=i(n,r,Math.ceil(n.length/4)),f=i(n,r,Math.ceil(n.length/2));return d||f?(s=f?d&&d[4].length>f[4].length?d:f:d,t.length>e.length?(l=s[0],c=s[1],u=s[2],h=s[3]):(u=s[0],h=s[1],l=s[2],c=s[3]),[l,c,u,h,s[4]]):null}(t,l);if(f){var p=f[0],g=f[1],b=f[2],m=f[3],v=f[4],y=i(p,b),w=i(g,m);return y.concat([[r,v]],w)}return function(t,r){for(var i=t.length,o=r.length,l=Math.ceil((i+o)/2),a=l,c=2*l,u=new Array(c),h=new Array(c),d=0;d<c;d++)u[d]=-1,h[d]=-1;u[a+1]=0,h[a+1]=0;for(var f=i-o,p=f%2!=0,g=0,b=0,m=0,v=0,y=0;y<l;y++){for(var w=-y+g;w<=y-b;w+=2){for(var x=a+w,N=(C=w===-y||w!==y&&u[x-1]<u[x+1]?u[x+1]:u[x-1]+1)-w;C<i&&N<o&&t.charAt(C)===r.charAt(N);)C++,N++;if(u[x]=C,C>i)b+=2;else if(N>o)g+=2;else if(p&&(_=a+f-w)>=0&&_<c&&-1!==h[_]&&C>=(A=i-h[_]))return s(t,r,C,N)}for(var k=-y+m;k<=y-v;k+=2){for(var A,_=a+k,E=(A=k===-y||k!==y&&h[_-1]<h[_+1]?h[_+1]:h[_-1]+1)-k;A<i&&E<o&&t.charAt(i-A-1)===r.charAt(o-E-1);)A++,E++;if(h[_]=A,A>i)v+=2;else if(E>o)m+=2;else if(!p){var C;if((x=a+f-k)>=0&&x<c&&-1!==u[x])if(N=a+(C=u[x])-x,C>=(A=i-A))return s(t,r,C,N)}}}return[[e,t],[n,r]]}(t,l)}(t=t.substring(0,t.length-x),g=g.substring(0,g.length-x));return N&&A.unshift([r,N]),k&&A.push([r,k]),p(A,v),m&&function(t){for(var i=!1,s=[],o=0,g=null,b=0,m=0,v=0,y=0,w=0;b<t.length;)t[b][0]==r?(s[o++]=b,m=y,v=w,y=0,w=0,g=t[b][1]):(t[b][0]==n?y+=t[b][1].length:w+=t[b][1].length,g&&g.length<=Math.max(m,v)&&g.length<=Math.max(y,w)&&(t.splice(s[o-1],0,[e,g]),t[s[o-1]+1][0]=n,o--,b=--o>0?s[o-1]:-1,m=0,v=0,y=0,w=0,g=null,i=!0)),b++;for(i&&p(t),function(t){function e(t,e){if(!t||!e)return 6;var n=t.charAt(t.length-1),r=e.charAt(0),i=n.match(c),s=r.match(c),o=i&&n.match(u),l=s&&r.match(u),a=o&&n.match(h),p=l&&r.match(h),g=a&&t.match(d),b=p&&e.match(f);return g||b?5:a||p?4:i&&!o&&l?3:o||l?2:i||s?1:0}for(var n=1;n<t.length-1;){if(t[n-1][0]==r&&t[n+1][0]==r){var i=t[n-1][1],s=t[n][1],o=t[n+1][1],l=a(i,s);if(l){var p=s.substring(s.length-l);i=i.substring(0,i.length-l),s=p+s.substring(0,s.length-l),o=p+o}for(var g=i,b=s,m=o,v=e(i,s)+e(s,o);s.charAt(0)===o.charAt(0);){i+=s.charAt(0),s=s.substring(1)+o.charAt(0),o=o.substring(1);var y=e(i,s)+e(s,o);y>=v&&(v=y,g=i,b=s,m=o)}t[n-1][1]!=g&&(g?t[n-1][1]=g:(t.splice(n-1,1),n--),t[n][1]=b,m?t[n+1][1]=m:(t.splice(n+1,1),n--))}n++}}(t),b=1;b<t.length;){if(t[b-1][0]==e&&t[b][0]==n){var x=t[b-1][1],N=t[b][1],k=l(x,N),A=l(N,x);k>=A?(k>=x.length/2||k>=N.length/2)&&(t.splice(b,0,[r,N.substring(0,k)]),t[b-1][1]=x.substring(0,x.length-k),t[b+1][1]=N.substring(k),b++):(A>=x.length/2||A>=N.length/2)&&(t.splice(b,0,[r,x.substring(0,A)]),t[b-1][0]=n,t[b-1][1]=N.substring(0,N.length-A),t[b+1][0]=e,t[b+1][1]=x.substring(A),b++),b++}b++}}(A),A}function s(t,e,n,r){var s=t.substring(0,n),o=e.substring(0,r),l=t.substring(n),a=e.substring(r),c=i(s,o),u=i(l,a);return c.concat(u)}function o(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(s,i)==e.substring(s,i)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return g(t.charCodeAt(i-1))&&i--,i}function l(t,e){var n=t.length,r=e.length;if(0==n||0==r)return 0;n>r?t=t.substring(n-r):n<r&&(e=e.substring(0,n));var i=Math.min(n,r);if(t==e)return i;for(var s=0,o=1;;){var l=t.substring(i-o),a=e.indexOf(l);if(-1==a)return s;o+=a,0!=a&&t.substring(i-o)!=e.substring(0,o)||(s=o,o++)}}function a(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(t.length-i,t.length-s)==e.substring(e.length-i,e.length-s)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return b(t.charCodeAt(t.length-i))&&i--,i}var c=/[^a-zA-Z0-9]/,u=/\s/,h=/[\r\n]/,d=/\n\r?\n$/,f=/^\r?\n\r?\n/;function p(t,i){t.push([r,""]);for(var s,l=0,c=0,u=0,h="",d="";l<t.length;)if(l<t.length-1&&!t[l][1])t.splice(l,1);else switch(t[l][0]){case n:u++,d+=t[l][1],l++;break;case e:c++,h+=t[l][1],l++;break;case r:var f=l-u-c-1;if(i){if(f>=0&&v(t[f][1])){var g=t[f][1].slice(-1);if(t[f][1]=t[f][1].slice(0,-1),h=g+h,d=g+d,!t[f][1]){t.splice(f,1),l--;var b=f-1;t[b]&&t[b][0]===n&&(u++,d=t[b][1]+d,b--),t[b]&&t[b][0]===e&&(c++,h=t[b][1]+h,b--),f=b}}m(t[l][1])&&(g=t[l][1].charAt(0),t[l][1]=t[l][1].slice(1),h+=g,d+=g)}if(l<t.length-1&&!t[l][1]){t.splice(l,1);break}if(h.length>0||d.length>0){h.length>0&&d.length>0&&(0!==(s=o(d,h))&&(f>=0?t[f][1]+=d.substring(0,s):(t.splice(0,0,[r,d.substring(0,s)]),l++),d=d.substring(s),h=h.substring(s)),0!==(s=a(d,h))&&(t[l][1]=d.substring(d.length-s)+t[l][1],d=d.substring(0,d.length-s),h=h.substring(0,h.length-s)));var y=u+c;0===h.length&&0===d.length?(t.splice(l-y,y),l-=y):0===h.length?(t.splice(l-y,y,[n,d]),l=l-y+1):0===d.length?(t.splice(l-y,y,[e,h]),l=l-y+1):(t.splice(l-y,y,[e,h],[n,d]),l=l-y+2)}0!==l&&t[l-1][0]===r?(t[l-1][1]+=t[l][1],t.splice(l,1)):l++,u=0,c=0,h="",d=""}""===t[t.length-1][1]&&t.pop();var w=!1;for(l=1;l<t.length-1;)t[l-1][0]===r&&t[l+1][0]===r&&(t[l][1].substring(t[l][1].length-t[l-1][1].length)===t[l-1][1]?(t[l][1]=t[l-1][1]+t[l][1].substring(0,t[l][1].length-t[l-1][1].length),t[l+1][1]=t[l-1][1]+t[l+1][1],t.splice(l-1,1),w=!0):t[l][1].substring(0,t[l+1][1].length)==t[l+1][1]&&(t[l-1][1]+=t[l+1][1],t[l][1]=t[l][1].substring(t[l+1][1].length)+t[l+1][1],t.splice(l+1,1),w=!0)),l++;w&&p(t,i)}function g(t){return t>=55296&&t<=56319}function b(t){return t>=56320&&t<=57343}function m(t){return b(t.charCodeAt(0))}function v(t){return g(t.charCodeAt(t.length-1))}function y(t,i,s,o){return v(t)||m(o)?null:function(t){for(var e=[],n=0;n<t.length;n++)t[n][1].length>0&&e.push(t[n]);return e}([[r,t],[e,i],[n,s],[r,o]])}function w(t,e,n,r){return i(t,e,n,r,!0)}w.INSERT=n,w.DELETE=e,w.EQUAL=r,t.exports=w},193:(t,e,n)=>{t=n.nmd(t);var r="__lodash_hash_undefined__",i=9007199254740991,s="[object Arguments]",o="[object Boolean]",l="[object Date]",a="[object Function]",c="[object GeneratorFunction]",u="[object Map]",h="[object Number]",d="[object Object]",f="[object Promise]",p="[object RegExp]",g="[object Set]",b="[object String]",m="[object Symbol]",v="[object WeakMap]",y="[object ArrayBuffer]",w="[object DataView]",x="[object Float32Array]",N="[object Float64Array]",k="[object Int8Array]",A="[object Int16Array]",_="[object Int32Array]",E="[object Uint8Array]",C="[object Uint8ClampedArray]",T="[object Uint16Array]",q="[object Uint32Array]",L=/\w*$/,S=/^\[object .+?Constructor\]$/,O=/^(?:0|[1-9]\d*)$/,j={};j[s]=j["[object Array]"]=j[y]=j[w]=j[o]=j[l]=j[x]=j[N]=j[k]=j[A]=j[_]=j[u]=j[h]=j[d]=j[p]=j[g]=j[b]=j[m]=j[E]=j[C]=j[T]=j[q]=!0,j["[object Error]"]=j[a]=j[v]=!1;var M="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,B="object"==typeof self&&self&&self.Object===Object&&self,R=M||B||Function("return this")(),I=e&&!e.nodeType&&e,D=I&&t&&!t.nodeType&&t,P=D&&D.exports===I;function U(t,e){return t.set(e[0],e[1]),t}function z(t,e){return t.add(e),t}function H(t,e,n,r){var i=-1,s=t?t.length:0;for(r&&s&&(n=t[++i]);++i<s;)n=e(n,t[i],i,t);return n}function F(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function $(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function V(t,e){return function(n){return t(e(n))}}function W(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var K,G=Array.prototype,Z=Function.prototype,Y=Object.prototype,X=R["__core-js_shared__"],Q=(K=/[^.]+$/.exec(X&&X.keys&&X.keys.IE_PROTO||""))?"Symbol(src)_1."+K:"",J=Z.toString,tt=Y.hasOwnProperty,et=Y.toString,nt=RegExp("^"+J.call(tt).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),rt=P?R.Buffer:void 0,it=R.Symbol,st=R.Uint8Array,ot=V(Object.getPrototypeOf,Object),lt=Object.create,at=Y.propertyIsEnumerable,ct=G.splice,ut=Object.getOwnPropertySymbols,ht=rt?rt.isBuffer:void 0,dt=V(Object.keys,Object),ft=Rt(R,"DataView"),pt=Rt(R,"Map"),gt=Rt(R,"Promise"),bt=Rt(R,"Set"),mt=Rt(R,"WeakMap"),vt=Rt(Object,"create"),yt=zt(ft),wt=zt(pt),xt=zt(gt),Nt=zt(bt),kt=zt(mt),At=it?it.prototype:void 0,_t=At?At.valueOf:void 0;function Et(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Ct(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Tt(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function qt(t){this.__data__=new Ct(t)}function Lt(t,e,n){var r=t[e];tt.call(t,e)&&Ht(r,n)&&(void 0!==n||e in t)||(t[e]=n)}function St(t,e){for(var n=t.length;n--;)if(Ht(t[n][0],e))return n;return-1}function Ot(t,e,n,r,i,f,v){var S;if(r&&(S=f?r(t,i,f,v):r(t)),void 0!==S)return S;if(!Kt(t))return t;var O=Ft(t);if(O){if(S=function(t){var e=t.length,n=t.constructor(e);return e&&"string"==typeof t[0]&&tt.call(t,"index")&&(n.index=t.index,n.input=t.input),n}(t),!e)return function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}(t,S)}else{var M=Dt(t),B=M==a||M==c;if(Vt(t))return function(t,e){if(e)return t.slice();var n=new t.constructor(t.length);return t.copy(n),n}(t,e);if(M==d||M==s||B&&!f){if(F(t))return f?t:{};if(S=function(t){return"function"!=typeof t.constructor||Ut(t)?{}:Kt(e=ot(t))?lt(e):{};var e}(B?{}:t),!e)return function(t,e){return Mt(t,It(t),e)}(t,function(t,e){return t&&Mt(e,Gt(e),t)}(S,t))}else{if(!j[M])return f?t:{};S=function(t,e,n,r){var i,s=t.constructor;switch(e){case y:return jt(t);case o:case l:return new s(+t);case w:return function(t,e){var n=e?jt(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}(t,r);case x:case N:case k:case A:case _:case E:case C:case T:case q:return function(t,e){var n=e?jt(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}(t,r);case u:return function(t,e,n){return H(e?n($(t),!0):$(t),U,new t.constructor)}(t,r,n);case h:case b:return new s(t);case p:return function(t){var e=new t.constructor(t.source,L.exec(t));return e.lastIndex=t.lastIndex,e}(t);case g:return function(t,e,n){return H(e?n(W(t),!0):W(t),z,new t.constructor)}(t,r,n);case m:return i=t,_t?Object(_t.call(i)):{}}}(t,M,Ot,e)}}v||(v=new qt);var R=v.get(t);if(R)return R;if(v.set(t,S),!O)var I=n?function(t){return function(t,e,n){var r=e(t);return Ft(t)?r:function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}(r,n(t))}(t,Gt,It)}(t):Gt(t);return function(t,e){for(var n=-1,r=t?t.length:0;++n<r&&!1!==e(t[n],n););}(I||t,(function(i,s){I&&(i=t[s=i]),Lt(S,s,Ot(i,e,n,r,s,t,v))})),S}function jt(t){var e=new t.constructor(t.byteLength);return new st(e).set(new st(t)),e}function Mt(t,e,n,r){n||(n={});for(var i=-1,s=e.length;++i<s;){var o=e[i],l=r?r(n[o],t[o],o,n,t):void 0;Lt(n,o,void 0===l?t[o]:l)}return n}function Bt(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function Rt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!Kt(t)||(e=t,Q&&Q in e))&&(Wt(t)||F(t)?nt:S).test(zt(t));var e}(n)?n:void 0}Et.prototype.clear=function(){this.__data__=vt?vt(null):{}},Et.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},Et.prototype.get=function(t){var e=this.__data__;if(vt){var n=e[t];return n===r?void 0:n}return tt.call(e,t)?e[t]:void 0},Et.prototype.has=function(t){var e=this.__data__;return vt?void 0!==e[t]:tt.call(e,t)},Et.prototype.set=function(t,e){return this.__data__[t]=vt&&void 0===e?r:e,this},Ct.prototype.clear=function(){this.__data__=[]},Ct.prototype.delete=function(t){var e=this.__data__,n=St(e,t);return!(n<0||(n==e.length-1?e.pop():ct.call(e,n,1),0))},Ct.prototype.get=function(t){var e=this.__data__,n=St(e,t);return n<0?void 0:e[n][1]},Ct.prototype.has=function(t){return St(this.__data__,t)>-1},Ct.prototype.set=function(t,e){var n=this.__data__,r=St(n,t);return r<0?n.push([t,e]):n[r][1]=e,this},Tt.prototype.clear=function(){this.__data__={hash:new Et,map:new(pt||Ct),string:new Et}},Tt.prototype.delete=function(t){return Bt(this,t).delete(t)},Tt.prototype.get=function(t){return Bt(this,t).get(t)},Tt.prototype.has=function(t){return Bt(this,t).has(t)},Tt.prototype.set=function(t,e){return Bt(this,t).set(t,e),this},qt.prototype.clear=function(){this.__data__=new Ct},qt.prototype.delete=function(t){return this.__data__.delete(t)},qt.prototype.get=function(t){return this.__data__.get(t)},qt.prototype.has=function(t){return this.__data__.has(t)},qt.prototype.set=function(t,e){var n=this.__data__;if(n instanceof Ct){var r=n.__data__;if(!pt||r.length<199)return r.push([t,e]),this;n=this.__data__=new Tt(r)}return n.set(t,e),this};var It=ut?V(ut,Object):function(){return[]},Dt=function(t){return et.call(t)};function Pt(t,e){return!!(e=null==e?i:e)&&("number"==typeof t||O.test(t))&&t>-1&&t%1==0&&t<e}function Ut(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Y)}function zt(t){if(null!=t){try{return J.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function Ht(t,e){return t===e||t!=t&&e!=e}(ft&&Dt(new ft(new ArrayBuffer(1)))!=w||pt&&Dt(new pt)!=u||gt&&Dt(gt.resolve())!=f||bt&&Dt(new bt)!=g||mt&&Dt(new mt)!=v)&&(Dt=function(t){var e=et.call(t),n=e==d?t.constructor:void 0,r=n?zt(n):void 0;if(r)switch(r){case yt:return w;case wt:return u;case xt:return f;case Nt:return g;case kt:return v}return e});var Ft=Array.isArray;function $t(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=i}(t.length)&&!Wt(t)}var Vt=ht||function(){return!1};function Wt(t){var e=Kt(t)?et.call(t):"";return e==a||e==c}function Kt(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function Gt(t){return $t(t)?function(t,e){var n=Ft(t)||function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&$t(t)}(t)&&tt.call(t,"callee")&&(!at.call(t,"callee")||et.call(t)==s)}(t)?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],r=n.length,i=!!r;for(var o in t)!e&&!tt.call(t,o)||i&&("length"==o||Pt(o,r))||n.push(o);return n}(t):function(t){if(!Ut(t))return dt(t);var e=[];for(var n in Object(t))tt.call(t,n)&&"constructor"!=n&&e.push(n);return e}(t)}t.exports=function(t){return Ot(t,!0,!0)}},142:(t,e,n)=>{t=n.nmd(t);var r="__lodash_hash_undefined__",i=1,s=2,o=9007199254740991,l="[object Arguments]",a="[object Array]",c="[object AsyncFunction]",u="[object Boolean]",h="[object Date]",d="[object Error]",f="[object Function]",p="[object GeneratorFunction]",g="[object Map]",b="[object Number]",m="[object Null]",v="[object Object]",y="[object Promise]",w="[object Proxy]",x="[object RegExp]",N="[object Set]",k="[object String]",A="[object Undefined]",_="[object WeakMap]",E="[object ArrayBuffer]",C="[object DataView]",T=/^\[object .+?Constructor\]$/,q=/^(?:0|[1-9]\d*)$/,L={};L["[object Float32Array]"]=L["[object Float64Array]"]=L["[object Int8Array]"]=L["[object Int16Array]"]=L["[object Int32Array]"]=L["[object Uint8Array]"]=L["[object Uint8ClampedArray]"]=L["[object Uint16Array]"]=L["[object Uint32Array]"]=!0,L[l]=L[a]=L[E]=L[u]=L[C]=L[h]=L[d]=L[f]=L[g]=L[b]=L[v]=L[x]=L[N]=L[k]=L[_]=!1;var S="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,O="object"==typeof self&&self&&self.Object===Object&&self,j=S||O||Function("return this")(),M=e&&!e.nodeType&&e,B=M&&t&&!t.nodeType&&t,R=B&&B.exports===M,I=R&&S.process,D=function(){try{return I&&I.binding&&I.binding("util")}catch(t){}}(),P=D&&D.isTypedArray;function U(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}function z(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function H(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var F,$,V,W=Array.prototype,K=Function.prototype,G=Object.prototype,Z=j["__core-js_shared__"],Y=K.toString,X=G.hasOwnProperty,Q=(F=/[^.]+$/.exec(Z&&Z.keys&&Z.keys.IE_PROTO||""))?"Symbol(src)_1."+F:"",J=G.toString,tt=RegExp("^"+Y.call(X).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),et=R?j.Buffer:void 0,nt=j.Symbol,rt=j.Uint8Array,it=G.propertyIsEnumerable,st=W.splice,ot=nt?nt.toStringTag:void 0,lt=Object.getOwnPropertySymbols,at=et?et.isBuffer:void 0,ct=($=Object.keys,V=Object,function(t){return $(V(t))}),ut=Bt(j,"DataView"),ht=Bt(j,"Map"),dt=Bt(j,"Promise"),ft=Bt(j,"Set"),pt=Bt(j,"WeakMap"),gt=Bt(Object,"create"),bt=Pt(ut),mt=Pt(ht),vt=Pt(dt),yt=Pt(ft),wt=Pt(pt),xt=nt?nt.prototype:void 0,Nt=xt?xt.valueOf:void 0;function kt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function At(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function _t(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Et(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new _t;++e<n;)this.add(t[e])}function Ct(t){var e=this.__data__=new At(t);this.size=e.size}function Tt(t,e){for(var n=t.length;n--;)if(Ut(t[n][0],e))return n;return-1}function qt(t){return null==t?void 0===t?A:m:ot&&ot in Object(t)?function(t){var e=X.call(t,ot),n=t[ot];try{t[ot]=void 0;var r=!0}catch(t){}var i=J.call(t);return r&&(e?t[ot]=n:delete t[ot]),i}(t):function(t){return J.call(t)}(t)}function Lt(t){return Kt(t)&&qt(t)==l}function St(t,e,n,r,o){return t===e||(null==t||null==e||!Kt(t)&&!Kt(e)?t!=t&&e!=e:function(t,e,n,r,o,c){var f=Ht(t),p=Ht(e),m=f?a:It(t),y=p?a:It(e),w=(m=m==l?v:m)==v,A=(y=y==l?v:y)==v,_=m==y;if(_&&Ft(t)){if(!Ft(e))return!1;f=!0,w=!1}if(_&&!w)return c||(c=new Ct),f||Gt(t)?Ot(t,e,n,r,o,c):function(t,e,n,r,o,l,a){switch(n){case C:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case E:return!(t.byteLength!=e.byteLength||!l(new rt(t),new rt(e)));case u:case h:case b:return Ut(+t,+e);case d:return t.name==e.name&&t.message==e.message;case x:case k:return t==e+"";case g:var c=z;case N:var f=r&i;if(c||(c=H),t.size!=e.size&&!f)return!1;var p=a.get(t);if(p)return p==e;r|=s,a.set(t,e);var m=Ot(c(t),c(e),r,o,l,a);return a.delete(t),m;case"[object Symbol]":if(Nt)return Nt.call(t)==Nt.call(e)}return!1}(t,e,m,n,r,o,c);if(!(n&i)){var T=w&&X.call(t,"__wrapped__"),q=A&&X.call(e,"__wrapped__");if(T||q){var L=T?t.value():t,S=q?e.value():e;return c||(c=new Ct),o(L,S,n,r,c)}}return!!_&&(c||(c=new Ct),function(t,e,n,r,s,o){var l=n&i,a=jt(t),c=a.length;if(c!=jt(e).length&&!l)return!1;for(var u=c;u--;){var h=a[u];if(!(l?h in e:X.call(e,h)))return!1}var d=o.get(t);if(d&&o.get(e))return d==e;var f=!0;o.set(t,e),o.set(e,t);for(var p=l;++u<c;){var g=t[h=a[u]],b=e[h];if(r)var m=l?r(b,g,h,e,t,o):r(g,b,h,t,e,o);if(!(void 0===m?g===b||s(g,b,n,r,o):m)){f=!1;break}p||(p="constructor"==h)}if(f&&!p){var v=t.constructor,y=e.constructor;v==y||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof y&&y instanceof y||(f=!1)}return o.delete(t),o.delete(e),f}(t,e,n,r,o,c))}(t,e,n,r,St,o))}function Ot(t,e,n,r,o,l){var a=n&i,c=t.length,u=e.length;if(c!=u&&!(a&&u>c))return!1;var h=l.get(t);if(h&&l.get(e))return h==e;var d=-1,f=!0,p=n&s?new Et:void 0;for(l.set(t,e),l.set(e,t);++d<c;){var g=t[d],b=e[d];if(r)var m=a?r(b,g,d,e,t,l):r(g,b,d,t,e,l);if(void 0!==m){if(m)continue;f=!1;break}if(p){if(!U(e,(function(t,e){if(i=e,!p.has(i)&&(g===t||o(g,t,n,r,l)))return p.push(e);var i}))){f=!1;break}}else if(g!==b&&!o(g,b,n,r,l)){f=!1;break}}return l.delete(t),l.delete(e),f}function jt(t){return function(t,e,n){var r=e(t);return Ht(t)?r:function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}(r,n(t))}(t,Zt,Rt)}function Mt(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function Bt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!Wt(t)||function(t){return!!Q&&Q in t}(t))&&($t(t)?tt:T).test(Pt(t))}(n)?n:void 0}kt.prototype.clear=function(){this.__data__=gt?gt(null):{},this.size=0},kt.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},kt.prototype.get=function(t){var e=this.__data__;if(gt){var n=e[t];return n===r?void 0:n}return X.call(e,t)?e[t]:void 0},kt.prototype.has=function(t){var e=this.__data__;return gt?void 0!==e[t]:X.call(e,t)},kt.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=gt&&void 0===e?r:e,this},At.prototype.clear=function(){this.__data__=[],this.size=0},At.prototype.delete=function(t){var e=this.__data__,n=Tt(e,t);return!(n<0||(n==e.length-1?e.pop():st.call(e,n,1),--this.size,0))},At.prototype.get=function(t){var e=this.__data__,n=Tt(e,t);return n<0?void 0:e[n][1]},At.prototype.has=function(t){return Tt(this.__data__,t)>-1},At.prototype.set=function(t,e){var n=this.__data__,r=Tt(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},_t.prototype.clear=function(){this.size=0,this.__data__={hash:new kt,map:new(ht||At),string:new kt}},_t.prototype.delete=function(t){var e=Mt(this,t).delete(t);return this.size-=e?1:0,e},_t.prototype.get=function(t){return Mt(this,t).get(t)},_t.prototype.has=function(t){return Mt(this,t).has(t)},_t.prototype.set=function(t,e){var n=Mt(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},Et.prototype.add=Et.prototype.push=function(t){return this.__data__.set(t,r),this},Et.prototype.has=function(t){return this.__data__.has(t)},Ct.prototype.clear=function(){this.__data__=new At,this.size=0},Ct.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},Ct.prototype.get=function(t){return this.__data__.get(t)},Ct.prototype.has=function(t){return this.__data__.has(t)},Ct.prototype.set=function(t,e){var n=this.__data__;if(n instanceof At){var r=n.__data__;if(!ht||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new _t(r)}return n.set(t,e),this.size=n.size,this};var Rt=lt?function(t){return null==t?[]:(t=Object(t),function(e,n){for(var r=-1,i=null==e?0:e.length,s=0,o=[];++r<i;){var l=e[r];a=l,it.call(t,a)&&(o[s++]=l)}var a;return o}(lt(t)))}:function(){return[]},It=qt;function Dt(t,e){return!!(e=null==e?o:e)&&("number"==typeof t||q.test(t))&&t>-1&&t%1==0&&t<e}function Pt(t){if(null!=t){try{return Y.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function Ut(t,e){return t===e||t!=t&&e!=e}(ut&&It(new ut(new ArrayBuffer(1)))!=C||ht&&It(new ht)!=g||dt&&It(dt.resolve())!=y||ft&&It(new ft)!=N||pt&&It(new pt)!=_)&&(It=function(t){var e=qt(t),n=e==v?t.constructor:void 0,r=n?Pt(n):"";if(r)switch(r){case bt:return C;case mt:return g;case vt:return y;case yt:return N;case wt:return _}return e});var zt=Lt(function(){return arguments}())?Lt:function(t){return Kt(t)&&X.call(t,"callee")&&!it.call(t,"callee")},Ht=Array.isArray,Ft=at||function(){return!1};function $t(t){if(!Wt(t))return!1;var e=qt(t);return e==f||e==p||e==c||e==w}function Vt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=o}function Wt(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Kt(t){return null!=t&&"object"==typeof t}var Gt=P?function(t){return function(e){return t(e)}}(P):function(t){return Kt(t)&&Vt(t.length)&&!!L[qt(t)]};function Zt(t){return null!=(e=t)&&Vt(e.length)&&!$t(e)?function(t,e){var n=Ht(t),r=!n&&zt(t),i=!n&&!r&&Ft(t),s=!n&&!r&&!i&&Gt(t),o=n||r||i||s,l=o?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],a=l.length;for(var c in t)!e&&!X.call(t,c)||o&&("length"==c||i&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Dt(c,a))||l.push(c);return l}(t):function(t){if(n=(e=t)&&e.constructor,e!==("function"==typeof n&&n.prototype||G))return ct(t);var e,n,r=[];for(var i in Object(t))X.call(t,i)&&"constructor"!=i&&r.push(i);return r}(t);var e}t.exports=function(t,e){return St(t,e)}},74:()=>{},625:()=>{},976:()=>{},106:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(193),i=n(142);var s;!function(t){t.compose=function(t={},e={},n=!1){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});let i=r(e);n||(i=Object.keys(i).reduce(((t,e)=>(null!=i[e]&&(t[e]=i[e]),t)),{}));for(const n in t)void 0!==t[n]&&void 0===e[n]&&(i[n]=t[n]);return Object.keys(i).length>0?i:void 0},t.diff=function(t={},e={}){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});const n=Object.keys(t).concat(Object.keys(e)).reduce(((n,r)=>(i(t[r],e[r])||(n[r]=void 0===e[r]?null:e[r]),n)),{});return Object.keys(n).length>0?n:void 0},t.invert=function(t={},e={}){t=t||{};const n=Object.keys(e).reduce(((n,r)=>(e[r]!==t[r]&&void 0!==t[r]&&(n[r]=e[r]),n)),{});return Object.keys(t).reduce(((n,r)=>(t[r]!==e[r]&&void 0===e[r]&&(n[r]=null),n)),n)},t.transform=function(t,e,n=!1){if("object"!=typeof t)return e;if("object"!=typeof e)return;if(!n)return e;const r=Object.keys(e).reduce(((n,r)=>(void 0===t[r]&&(n[r]=e[r]),n)),{});return Object.keys(r).length>0?r:void 0}}(s||(s={})),e.default=s},660:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributeMap=e.OpIterator=e.Op=void 0;const r=n(606),i=n(193),s=n(142),o=n(106);e.AttributeMap=o.default;const l=n(759);e.Op=l.default;const a=n(317);e.OpIterator=a.default;const c=String.fromCharCode(0),u=(t,e)=>{if("object"!=typeof t||null===t)throw new Error("cannot retain a "+typeof t);if("object"!=typeof e||null===e)throw new Error("cannot retain a "+typeof e);const n=Object.keys(t)[0];if(!n||n!==Object.keys(e)[0])throw new Error(`embed types not matched: ${n} != ${Object.keys(e)[0]}`);return[n,t[n],e[n]]};class h{constructor(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}static registerEmbed(t,e){this.handlers[t]=e}static unregisterEmbed(t){delete this.handlers[t]}static getHandler(t){const e=this.handlers[t];if(!e)throw new Error(`no handlers for embed type "${t}"`);return e}insert(t,e){const n={};return"string"==typeof t&&0===t.length?this:(n.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n))}delete(t){return t<=0?this:this.push({delete:t})}retain(t,e){if("number"==typeof t&&t<=0)return this;const n={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n)}push(t){let e=this.ops.length,n=this.ops[e-1];if(t=i(t),"object"==typeof n){if("number"==typeof t.delete&&"number"==typeof n.delete)return this.ops[e-1]={delete:n.delete+t.delete},this;if("number"==typeof n.delete&&null!=t.insert&&(e-=1,n=this.ops[e-1],"object"!=typeof n))return this.ops.unshift(t),this;if(s(t.attributes,n.attributes)){if("string"==typeof t.insert&&"string"==typeof n.insert)return this.ops[e-1]={insert:n.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof n.retain)return this.ops[e-1]={retain:n.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this}chop(){const t=this.ops[this.ops.length-1];return t&&"number"==typeof t.retain&&!t.attributes&&this.ops.pop(),this}filter(t){return this.ops.filter(t)}forEach(t){this.ops.forEach(t)}map(t){return this.ops.map(t)}partition(t){const e=[],n=[];return this.forEach((r=>{(t(r)?e:n).push(r)})),[e,n]}reduce(t,e){return this.ops.reduce(t,e)}changeLength(){return this.reduce(((t,e)=>e.insert?t+l.default.length(e):e.delete?t-e.delete:t),0)}length(){return this.reduce(((t,e)=>t+l.default.length(e)),0)}slice(t=0,e=1/0){const n=[],r=new a.default(this.ops);let i=0;for(;i<e&&r.hasNext();){let s;i<t?s=r.next(t-i):(s=r.next(e-i),n.push(s)),i+=l.default.length(s)}return new h(n)}compose(t){const e=new a.default(this.ops),n=new a.default(t.ops),r=[],i=n.peek();if(null!=i&&"number"==typeof i.retain&&null==i.attributes){let t=i.retain;for(;"insert"===e.peekType()&&e.peekLength()<=t;)t-=e.peekLength(),r.push(e.next());i.retain-t>0&&n.next(i.retain-t)}const l=new h(r);for(;e.hasNext()||n.hasNext();)if("insert"===n.peekType())l.push(n.next());else if("delete"===e.peekType())l.push(e.next());else{const t=Math.min(e.peekLength(),n.peekLength()),r=e.next(t),i=n.next(t);if(i.retain){const a={};if("number"==typeof r.retain)a.retain="number"==typeof i.retain?t:i.retain;else if("number"==typeof i.retain)null==r.retain?a.insert=r.insert:a.retain=r.retain;else{const t=null==r.retain?"insert":"retain",[e,n,s]=u(r[t],i.retain),o=h.getHandler(e);a[t]={[e]:o.compose(n,s,"retain"===t)}}const c=o.default.compose(r.attributes,i.attributes,"number"==typeof r.retain);if(c&&(a.attributes=c),l.push(a),!n.hasNext()&&s(l.ops[l.ops.length-1],a)){const t=new h(e.rest());return l.concat(t).chop()}}else"number"==typeof i.delete&&("number"==typeof r.retain||"object"==typeof r.retain&&null!==r.retain)&&l.push(i)}return l.chop()}concat(t){const e=new h(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e}diff(t,e){if(this.ops===t.ops)return new h;const n=[this,t].map((e=>e.map((n=>{if(null!=n.insert)return"string"==typeof n.insert?n.insert:c;throw new Error("diff() called "+(e===t?"on":"with")+" non-document")})).join(""))),i=new h,l=r(n[0],n[1],e,!0),u=new a.default(this.ops),d=new a.default(t.ops);return l.forEach((t=>{let e=t[1].length;for(;e>0;){let n=0;switch(t[0]){case r.INSERT:n=Math.min(d.peekLength(),e),i.push(d.next(n));break;case r.DELETE:n=Math.min(e,u.peekLength()),u.next(n),i.delete(n);break;case r.EQUAL:n=Math.min(u.peekLength(),d.peekLength(),e);const t=u.next(n),l=d.next(n);s(t.insert,l.insert)?i.retain(n,o.default.diff(t.attributes,l.attributes)):i.push(l).delete(n)}e-=n}})),i.chop()}eachLine(t,e="\n"){const n=new a.default(this.ops);let r=new h,i=0;for(;n.hasNext();){if("insert"!==n.peekType())return;const s=n.peek(),o=l.default.length(s)-n.peekLength(),a="string"==typeof s.insert?s.insert.indexOf(e,o)-o:-1;if(a<0)r.push(n.next());else if(a>0)r.push(n.next(a));else{if(!1===t(r,n.next(1).attributes||{},i))return;i+=1,r=new h}}r.length()>0&&t(r,{},i)}invert(t){const e=new h;return this.reduce(((n,r)=>{if(r.insert)e.delete(l.default.length(r));else{if("number"==typeof r.retain&&null==r.attributes)return e.retain(r.retain),n+r.retain;if(r.delete||"number"==typeof r.retain){const i=r.delete||r.retain;return t.slice(n,n+i).forEach((t=>{r.delete?e.push(t):r.retain&&r.attributes&&e.retain(l.default.length(t),o.default.invert(r.attributes,t.attributes))})),n+i}if("object"==typeof r.retain&&null!==r.retain){const i=t.slice(n,n+1),s=new a.default(i.ops).next(),[l,c,d]=u(r.retain,s.insert),f=h.getHandler(l);return e.retain({[l]:f.invert(c,d)},o.default.invert(r.attributes,s.attributes)),n+1}}return n}),0),e.chop()}transform(t,e=!1){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);const n=t,r=new a.default(this.ops),i=new a.default(n.ops),s=new h;for(;r.hasNext()||i.hasNext();)if("insert"!==r.peekType()||!e&&"insert"===i.peekType())if("insert"===i.peekType())s.push(i.next());else{const t=Math.min(r.peekLength(),i.peekLength()),n=r.next(t),l=i.next(t);if(n.delete)continue;if(l.delete)s.push(l);else{const r=n.retain,i=l.retain;let a="object"==typeof i&&null!==i?i:t;if("object"==typeof r&&null!==r&&"object"==typeof i&&null!==i){const t=Object.keys(r)[0];if(t===Object.keys(i)[0]){const n=h.getHandler(t);n&&(a={[t]:n.transform(r[t],i[t],e)})}}s.retain(a,o.default.transform(n.attributes,l.attributes,e))}}else s.retain(l.default.length(r.next()));return s.chop()}transformPosition(t,e=!1){e=!!e;const n=new a.default(this.ops);let r=0;for(;n.hasNext()&&r<=t;){const i=n.peekLength(),s=n.peekType();n.next(),"delete"!==s?("insert"===s&&(r<t||!e)&&(t+=i),r+=i):t-=Math.min(i,t-r)}return t}}h.Op=l.default,h.OpIterator=a.default,h.AttributeMap=o.default,h.handlers={},e.default=h,t.exports=h,t.exports.default=h},759:(t,e)=>{"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t.length=function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"object"==typeof t.retain&&null!==t.retain?1:"string"==typeof t.insert?t.insert.length:1}}(n||(n={})),e.default=n},317:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(759);e.default=class{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);const e=this.ops[this.index];if(e){const n=this.offset,i=r.default.length(e);if(t>=i-n?(t=i-n,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};{const r={};return e.attributes&&(r.attributes=e.attributes),"number"==typeof e.retain?r.retain=t:"object"==typeof e.retain&&null!==e.retain?r.retain=e.retain:"string"==typeof e.insert?r.insert=e.insert.substr(n,t):r.insert=e.insert,r}}return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?r.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const t=this.ops[this.index];return t?"number"==typeof t.delete?"delete":"number"==typeof t.retain||"object"==typeof t.retain&&null!==t.retain?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);{const t=this.offset,e=this.index,n=this.next(),r=this.ops.slice(this.index);return this.offset=t,this.index=e,[n].concat(r)}}return[]}}},568:(t,e,n)=>{var r;self,t.exports=(r=n(690),function(){var t={386:function(t){var e=-1,n=1,r=0;function i(t,g,b,m,v){if(t===g)return t?[[r,t]]:[];if(null!=b){var w=function(t,e,n){var r="number"==typeof n?{index:n,length:0}:n.oldRange,i="number"==typeof n?null:n.newRange,s=t.length,o=e.length;if(0===r.length&&(null===i||0===i.length)){var l=r.index,a=t.slice(0,l),c=t.slice(l),u=i?i.index:null,h=l+o-s;if((null===u||u===h)&&!(h<0||h>o)){var d=e.slice(0,h);if((g=e.slice(h))===c){var f=Math.min(l,h);if((m=a.slice(0,f))===(w=d.slice(0,f)))return y(m,a.slice(f),d.slice(f),c)}}if(null===u||u===l){var p=l,g=(d=e.slice(0,p),e.slice(p));if(d===a){var b=Math.min(s-p,o-p);if((v=c.slice(c.length-b))===(x=g.slice(g.length-b)))return y(a,c.slice(0,c.length-b),g.slice(0,g.length-b),v)}}}if(r.length>0&&i&&0===i.length){var m=t.slice(0,r.index),v=t.slice(r.index+r.length);if(!(o<(f=m.length)+(b=v.length))){var w=e.slice(0,f),x=e.slice(o-b);if(m===w&&v===x)return y(m,t.slice(f,s-b),e.slice(f,o-b),v)}}return null}(t,g,b);if(w)return w}var x=o(t,g),N=t.substring(0,x);x=a(t=t.substring(x),g=g.substring(x));var k=t.substring(t.length-x),A=function(t,l){var c;if(!t)return[[n,l]];if(!l)return[[e,t]];var u=t.length>l.length?t:l,h=t.length>l.length?l:t,d=u.indexOf(h);if(-1!==d)return c=[[n,u.substring(0,d)],[r,h],[n,u.substring(d+h.length)]],t.length>l.length&&(c[0][0]=c[2][0]=e),c;if(1===h.length)return[[e,t],[n,l]];var f=function(t,e){var n=t.length>e.length?t:e,r=t.length>e.length?e:t;if(n.length<4||2*r.length<n.length)return null;function i(t,e,n){for(var r,i,s,l,c=t.substring(n,n+Math.floor(t.length/4)),u=-1,h="";-1!==(u=e.indexOf(c,u+1));){var d=o(t.substring(n),e.substring(u)),f=a(t.substring(0,n),e.substring(0,u));h.length<f+d&&(h=e.substring(u-f,u)+e.substring(u,u+d),r=t.substring(0,n-f),i=t.substring(n+d),s=e.substring(0,u-f),l=e.substring(u+d))}return 2*h.length>=t.length?[r,i,s,l,h]:null}var s,l,c,u,h,d=i(n,r,Math.ceil(n.length/4)),f=i(n,r,Math.ceil(n.length/2));return d||f?(s=f?d&&d[4].length>f[4].length?d:f:d,t.length>e.length?(l=s[0],c=s[1],u=s[2],h=s[3]):(u=s[0],h=s[1],l=s[2],c=s[3]),[l,c,u,h,s[4]]):null}(t,l);if(f){var p=f[0],g=f[1],b=f[2],m=f[3],v=f[4],y=i(p,b),w=i(g,m);return y.concat([[r,v]],w)}return function(t,r){for(var i=t.length,o=r.length,l=Math.ceil((i+o)/2),a=l,c=2*l,u=new Array(c),h=new Array(c),d=0;d<c;d++)u[d]=-1,h[d]=-1;u[a+1]=0,h[a+1]=0;for(var f=i-o,p=f%2!=0,g=0,b=0,m=0,v=0,y=0;y<l;y++){for(var w=-y+g;w<=y-b;w+=2){for(var x=a+w,N=(C=w===-y||w!==y&&u[x-1]<u[x+1]?u[x+1]:u[x-1]+1)-w;C<i&&N<o&&t.charAt(C)===r.charAt(N);)C++,N++;if(u[x]=C,C>i)b+=2;else if(N>o)g+=2;else if(p&&(_=a+f-w)>=0&&_<c&&-1!==h[_]&&C>=(A=i-h[_]))return s(t,r,C,N)}for(var k=-y+m;k<=y-v;k+=2){for(var A,_=a+k,E=(A=k===-y||k!==y&&h[_-1]<h[_+1]?h[_+1]:h[_-1]+1)-k;A<i&&E<o&&t.charAt(i-A-1)===r.charAt(o-E-1);)A++,E++;if(h[_]=A,A>i)v+=2;else if(E>o)m+=2;else if(!p){var C;if((x=a+f-k)>=0&&x<c&&-1!==u[x]&&(N=a+(C=u[x])-x,C>=(A=i-A)))return s(t,r,C,N)}}}return[[e,t],[n,r]]}(t,l)}(t=t.substring(0,t.length-x),g=g.substring(0,g.length-x));return N&&A.unshift([r,N]),k&&A.push([r,k]),p(A,v),m&&function(t){for(var i=!1,s=[],o=0,g=null,b=0,m=0,v=0,y=0,w=0;b<t.length;)t[b][0]==r?(s[o++]=b,m=y,v=w,y=0,w=0,g=t[b][1]):(t[b][0]==n?y+=t[b][1].length:w+=t[b][1].length,g&&g.length<=Math.max(m,v)&&g.length<=Math.max(y,w)&&(t.splice(s[o-1],0,[e,g]),t[s[o-1]+1][0]=n,o--,b=--o>0?s[o-1]:-1,m=0,v=0,y=0,w=0,g=null,i=!0)),b++;for(i&&p(t),function(t){function e(t,e){if(!t||!e)return 6;var n=t.charAt(t.length-1),r=e.charAt(0),i=n.match(c),s=r.match(c),o=i&&n.match(u),l=s&&r.match(u),a=o&&n.match(h),p=l&&r.match(h),g=a&&t.match(d),b=p&&e.match(f);return g||b?5:a||p?4:i&&!o&&l?3:o||l?2:i||s?1:0}for(var n=1;n<t.length-1;){if(t[n-1][0]==r&&t[n+1][0]==r){var i=t[n-1][1],s=t[n][1],o=t[n+1][1],l=a(i,s);if(l){var p=s.substring(s.length-l);i=i.substring(0,i.length-l),s=p+s.substring(0,s.length-l),o=p+o}for(var g=i,b=s,m=o,v=e(i,s)+e(s,o);s.charAt(0)===o.charAt(0);){i+=s.charAt(0),s=s.substring(1)+o.charAt(0),o=o.substring(1);var y=e(i,s)+e(s,o);y>=v&&(v=y,g=i,b=s,m=o)}t[n-1][1]!=g&&(g?t[n-1][1]=g:(t.splice(n-1,1),n--),t[n][1]=b,m?t[n+1][1]=m:(t.splice(n+1,1),n--))}n++}}(t),b=1;b<t.length;){if(t[b-1][0]==e&&t[b][0]==n){var x=t[b-1][1],N=t[b][1],k=l(x,N),A=l(N,x);k>=A?(k>=x.length/2||k>=N.length/2)&&(t.splice(b,0,[r,N.substring(0,k)]),t[b-1][1]=x.substring(0,x.length-k),t[b+1][1]=N.substring(k),b++):(A>=x.length/2||A>=N.length/2)&&(t.splice(b,0,[r,x.substring(0,A)]),t[b-1][0]=n,t[b-1][1]=N.substring(0,N.length-A),t[b+1][0]=e,t[b+1][1]=x.substring(A),b++),b++}b++}}(A),A}function s(t,e,n,r){var s=t.substring(0,n),o=e.substring(0,r),l=t.substring(n),a=e.substring(r),c=i(s,o),u=i(l,a);return c.concat(u)}function o(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(s,i)==e.substring(s,i)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return g(t.charCodeAt(i-1))&&i--,i}function l(t,e){var n=t.length,r=e.length;if(0==n||0==r)return 0;n>r?t=t.substring(n-r):n<r&&(e=e.substring(0,n));var i=Math.min(n,r);if(t==e)return i;for(var s=0,o=1;;){var l=t.substring(i-o),a=e.indexOf(l);if(-1==a)return s;o+=a,0!=a&&t.substring(i-o)!=e.substring(0,o)||(s=o,o++)}}function a(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,s=0;n<i;)t.substring(t.length-i,t.length-s)==e.substring(e.length-i,e.length-s)?s=n=i:r=i,i=Math.floor((r-n)/2+n);return b(t.charCodeAt(t.length-i))&&i--,i}var c=/[^a-zA-Z0-9]/,u=/\s/,h=/[\r\n]/,d=/\n\r?\n$/,f=/^\r?\n\r?\n/;function p(t,i){t.push([r,""]);for(var s,l=0,c=0,u=0,h="",d="";l<t.length;)if(l<t.length-1&&!t[l][1])t.splice(l,1);else switch(t[l][0]){case n:u++,d+=t[l][1],l++;break;case e:c++,h+=t[l][1],l++;break;case r:var f=l-u-c-1;if(i){if(f>=0&&v(t[f][1])){var g=t[f][1].slice(-1);if(t[f][1]=t[f][1].slice(0,-1),h=g+h,d=g+d,!t[f][1]){t.splice(f,1),l--;var b=f-1;t[b]&&t[b][0]===n&&(u++,d=t[b][1]+d,b--),t[b]&&t[b][0]===e&&(c++,h=t[b][1]+h,b--),f=b}}m(t[l][1])&&(g=t[l][1].charAt(0),t[l][1]=t[l][1].slice(1),h+=g,d+=g)}if(l<t.length-1&&!t[l][1]){t.splice(l,1);break}if(h.length>0||d.length>0){h.length>0&&d.length>0&&(0!==(s=o(d,h))&&(f>=0?t[f][1]+=d.substring(0,s):(t.splice(0,0,[r,d.substring(0,s)]),l++),d=d.substring(s),h=h.substring(s)),0!==(s=a(d,h))&&(t[l][1]=d.substring(d.length-s)+t[l][1],d=d.substring(0,d.length-s),h=h.substring(0,h.length-s)));var y=u+c;0===h.length&&0===d.length?(t.splice(l-y,y),l-=y):0===h.length?(t.splice(l-y,y,[n,d]),l=l-y+1):0===d.length?(t.splice(l-y,y,[e,h]),l=l-y+1):(t.splice(l-y,y,[e,h],[n,d]),l=l-y+2)}0!==l&&t[l-1][0]===r?(t[l-1][1]+=t[l][1],t.splice(l,1)):l++,u=0,c=0,h="",d=""}""===t[t.length-1][1]&&t.pop();var w=!1;for(l=1;l<t.length-1;)t[l-1][0]===r&&t[l+1][0]===r&&(t[l][1].substring(t[l][1].length-t[l-1][1].length)===t[l-1][1]?(t[l][1]=t[l-1][1]+t[l][1].substring(0,t[l][1].length-t[l-1][1].length),t[l+1][1]=t[l-1][1]+t[l+1][1],t.splice(l-1,1),w=!0):t[l][1].substring(0,t[l+1][1].length)==t[l+1][1]&&(t[l-1][1]+=t[l+1][1],t[l][1]=t[l][1].substring(t[l+1][1].length)+t[l+1][1],t.splice(l+1,1),w=!0)),l++;w&&p(t,i)}function g(t){return t>=55296&&t<=56319}function b(t){return t>=56320&&t<=57343}function m(t){return b(t.charCodeAt(0))}function v(t){return g(t.charCodeAt(t.length-1))}function y(t,i,s,o){return v(t)||m(o)?null:function(t){for(var e=[],n=0;n<t.length;n++)t[n][1].length>0&&e.push(t[n]);return e}([[r,t],[e,i],[n,s],[r,o]])}function w(t,e,n,r){return i(t,e,n,r,!0)}w.INSERT=n,w.DELETE=e,w.EQUAL=r,t.exports=w},861:function(t,e,n){t=n.nmd(t);var r="__lodash_hash_undefined__",i=9007199254740991,s="[object Arguments]",o="[object Boolean]",l="[object Date]",a="[object Function]",c="[object GeneratorFunction]",u="[object Map]",h="[object Number]",d="[object Object]",f="[object Promise]",p="[object RegExp]",g="[object Set]",b="[object String]",m="[object Symbol]",v="[object WeakMap]",y="[object ArrayBuffer]",w="[object DataView]",x="[object Float32Array]",N="[object Float64Array]",k="[object Int8Array]",A="[object Int16Array]",_="[object Int32Array]",E="[object Uint8Array]",C="[object Uint8ClampedArray]",T="[object Uint16Array]",q="[object Uint32Array]",L=/\w*$/,S=/^\[object .+?Constructor\]$/,O=/^(?:0|[1-9]\d*)$/,j={};j[s]=j["[object Array]"]=j[y]=j[w]=j[o]=j[l]=j[x]=j[N]=j[k]=j[A]=j[_]=j[u]=j[h]=j[d]=j[p]=j[g]=j[b]=j[m]=j[E]=j[C]=j[T]=j[q]=!0,j["[object Error]"]=j[a]=j[v]=!1;var M="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,B="object"==typeof self&&self&&self.Object===Object&&self,R=M||B||Function("return this")(),I=e&&!e.nodeType&&e,D=I&&t&&!t.nodeType&&t,P=D&&D.exports===I;function U(t,e){return t.set(e[0],e[1]),t}function z(t,e){return t.add(e),t}function H(t,e,n,r){var i=-1,s=t?t.length:0;for(r&&s&&(n=t[++i]);++i<s;)n=e(n,t[i],i,t);return n}function F(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function $(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function V(t,e){return function(n){return t(e(n))}}function W(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var K,G=Array.prototype,Z=Function.prototype,Y=Object.prototype,X=R["__core-js_shared__"],Q=(K=/[^.]+$/.exec(X&&X.keys&&X.keys.IE_PROTO||""))?"Symbol(src)_1."+K:"",J=Z.toString,tt=Y.hasOwnProperty,et=Y.toString,nt=RegExp("^"+J.call(tt).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),rt=P?R.Buffer:void 0,it=R.Symbol,st=R.Uint8Array,ot=V(Object.getPrototypeOf,Object),lt=Object.create,at=Y.propertyIsEnumerable,ct=G.splice,ut=Object.getOwnPropertySymbols,ht=rt?rt.isBuffer:void 0,dt=V(Object.keys,Object),ft=Rt(R,"DataView"),pt=Rt(R,"Map"),gt=Rt(R,"Promise"),bt=Rt(R,"Set"),mt=Rt(R,"WeakMap"),vt=Rt(Object,"create"),yt=zt(ft),wt=zt(pt),xt=zt(gt),Nt=zt(bt),kt=zt(mt),At=it?it.prototype:void 0,_t=At?At.valueOf:void 0;function Et(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Ct(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Tt(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function qt(t){this.__data__=new Ct(t)}function Lt(t,e,n){var r=t[e];tt.call(t,e)&&Ht(r,n)&&(void 0!==n||e in t)||(t[e]=n)}function St(t,e){for(var n=t.length;n--;)if(Ht(t[n][0],e))return n;return-1}function Ot(t,e,n,r,i,f,v){var S;if(r&&(S=f?r(t,i,f,v):r(t)),void 0!==S)return S;if(!Kt(t))return t;var O=Ft(t);if(O){if(S=function(t){var e=t.length,n=t.constructor(e);return e&&"string"==typeof t[0]&&tt.call(t,"index")&&(n.index=t.index,n.input=t.input),n}(t),!e)return function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}(t,S)}else{var M=Dt(t),B=M==a||M==c;if(Vt(t))return function(t,e){if(e)return t.slice();var n=new t.constructor(t.length);return t.copy(n),n}(t,e);if(M==d||M==s||B&&!f){if(F(t))return f?t:{};if(S=function(t){return"function"!=typeof t.constructor||Ut(t)?{}:Kt(e=ot(t))?lt(e):{};var e}(B?{}:t),!e)return function(t,e){return Mt(t,It(t),e)}(t,function(t,e){return t&&Mt(e,Gt(e),t)}(S,t))}else{if(!j[M])return f?t:{};S=function(t,e,n,r){var i,s=t.constructor;switch(e){case y:return jt(t);case o:case l:return new s(+t);case w:return function(t,e){var n=e?jt(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}(t,r);case x:case N:case k:case A:case _:case E:case C:case T:case q:return function(t,e){var n=e?jt(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)}(t,r);case u:return function(t,e,n){return H(e?n($(t),!0):$(t),U,new t.constructor)}(t,r,n);case h:case b:return new s(t);case p:return function(t){var e=new t.constructor(t.source,L.exec(t));return e.lastIndex=t.lastIndex,e}(t);case g:return function(t,e,n){return H(e?n(W(t),!0):W(t),z,new t.constructor)}(t,r,n);case m:return i=t,_t?Object(_t.call(i)):{}}}(t,M,Ot,e)}}v||(v=new qt);var R=v.get(t);if(R)return R;if(v.set(t,S),!O)var I=n?function(t){return function(t,e,n){var r=e(t);return Ft(t)?r:function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}(r,n(t))}(t,Gt,It)}(t):Gt(t);return function(t,e){for(var n=-1,r=t?t.length:0;++n<r&&!1!==e(t[n],n););}(I||t,(function(i,s){I&&(i=t[s=i]),Lt(S,s,Ot(i,e,n,r,s,t,v))})),S}function jt(t){var e=new t.constructor(t.byteLength);return new st(e).set(new st(t)),e}function Mt(t,e,n,r){n||(n={});for(var i=-1,s=e.length;++i<s;){var o=e[i],l=r?r(n[o],t[o],o,n,t):void 0;Lt(n,o,void 0===l?t[o]:l)}return n}function Bt(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function Rt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!Kt(t)||(e=t,Q&&Q in e))&&(Wt(t)||F(t)?nt:S).test(zt(t));var e}(n)?n:void 0}Et.prototype.clear=function(){this.__data__=vt?vt(null):{}},Et.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},Et.prototype.get=function(t){var e=this.__data__;if(vt){var n=e[t];return n===r?void 0:n}return tt.call(e,t)?e[t]:void 0},Et.prototype.has=function(t){var e=this.__data__;return vt?void 0!==e[t]:tt.call(e,t)},Et.prototype.set=function(t,e){return this.__data__[t]=vt&&void 0===e?r:e,this},Ct.prototype.clear=function(){this.__data__=[]},Ct.prototype.delete=function(t){var e=this.__data__,n=St(e,t);return!(n<0||(n==e.length-1?e.pop():ct.call(e,n,1),0))},Ct.prototype.get=function(t){var e=this.__data__,n=St(e,t);return n<0?void 0:e[n][1]},Ct.prototype.has=function(t){return St(this.__data__,t)>-1},Ct.prototype.set=function(t,e){var n=this.__data__,r=St(n,t);return r<0?n.push([t,e]):n[r][1]=e,this},Tt.prototype.clear=function(){this.__data__={hash:new Et,map:new(pt||Ct),string:new Et}},Tt.prototype.delete=function(t){return Bt(this,t).delete(t)},Tt.prototype.get=function(t){return Bt(this,t).get(t)},Tt.prototype.has=function(t){return Bt(this,t).has(t)},Tt.prototype.set=function(t,e){return Bt(this,t).set(t,e),this},qt.prototype.clear=function(){this.__data__=new Ct},qt.prototype.delete=function(t){return this.__data__.delete(t)},qt.prototype.get=function(t){return this.__data__.get(t)},qt.prototype.has=function(t){return this.__data__.has(t)},qt.prototype.set=function(t,e){var n=this.__data__;if(n instanceof Ct){var r=n.__data__;if(!pt||r.length<199)return r.push([t,e]),this;n=this.__data__=new Tt(r)}return n.set(t,e),this};var It=ut?V(ut,Object):function(){return[]},Dt=function(t){return et.call(t)};function Pt(t,e){return!!(e=null==e?i:e)&&("number"==typeof t||O.test(t))&&t>-1&&t%1==0&&t<e}function Ut(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||Y)}function zt(t){if(null!=t){try{return J.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function Ht(t,e){return t===e||t!=t&&e!=e}(ft&&Dt(new ft(new ArrayBuffer(1)))!=w||pt&&Dt(new pt)!=u||gt&&Dt(gt.resolve())!=f||bt&&Dt(new bt)!=g||mt&&Dt(new mt)!=v)&&(Dt=function(t){var e=et.call(t),n=e==d?t.constructor:void 0,r=n?zt(n):void 0;if(r)switch(r){case yt:return w;case wt:return u;case xt:return f;case Nt:return g;case kt:return v}return e});var Ft=Array.isArray;function $t(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=i}(t.length)&&!Wt(t)}var Vt=ht||function(){return!1};function Wt(t){var e=Kt(t)?et.call(t):"";return e==a||e==c}function Kt(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function Gt(t){return $t(t)?function(t,e){var n=Ft(t)||function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&$t(t)}(t)&&tt.call(t,"callee")&&(!at.call(t,"callee")||et.call(t)==s)}(t)?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],r=n.length,i=!!r;for(var o in t)!tt.call(t,o)||i&&("length"==o||Pt(o,r))||n.push(o);return n}(t):function(t){if(!Ut(t))return dt(t);var e=[];for(var n in Object(t))tt.call(t,n)&&"constructor"!=n&&e.push(n);return e}(t)}t.exports=function(t){return Ot(t,!0,!0)}},842:function(t,e,n){t=n.nmd(t);var r="__lodash_hash_undefined__",i=1,s=2,o=9007199254740991,l="[object Arguments]",a="[object Array]",c="[object AsyncFunction]",u="[object Boolean]",h="[object Date]",d="[object Error]",f="[object Function]",p="[object GeneratorFunction]",g="[object Map]",b="[object Number]",m="[object Null]",v="[object Object]",y="[object Promise]",w="[object Proxy]",x="[object RegExp]",N="[object Set]",k="[object String]",A="[object Undefined]",_="[object WeakMap]",E="[object ArrayBuffer]",C="[object DataView]",T=/^\[object .+?Constructor\]$/,q=/^(?:0|[1-9]\d*)$/,L={};L["[object Float32Array]"]=L["[object Float64Array]"]=L["[object Int8Array]"]=L["[object Int16Array]"]=L["[object Int32Array]"]=L["[object Uint8Array]"]=L["[object Uint8ClampedArray]"]=L["[object Uint16Array]"]=L["[object Uint32Array]"]=!0,L[l]=L[a]=L[E]=L[u]=L[C]=L[h]=L[d]=L[f]=L[g]=L[b]=L[v]=L[x]=L[N]=L[k]=L[_]=!1;var S="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,O="object"==typeof self&&self&&self.Object===Object&&self,j=S||O||Function("return this")(),M=e&&!e.nodeType&&e,B=M&&t&&!t.nodeType&&t,R=B&&B.exports===M,I=R&&S.process,D=function(){try{return I&&I.binding&&I.binding("util")}catch(t){}}(),P=D&&D.isTypedArray;function U(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}function z(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function H(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}var F,$,V,W=Array.prototype,K=Function.prototype,G=Object.prototype,Z=j["__core-js_shared__"],Y=K.toString,X=G.hasOwnProperty,Q=(F=/[^.]+$/.exec(Z&&Z.keys&&Z.keys.IE_PROTO||""))?"Symbol(src)_1."+F:"",J=G.toString,tt=RegExp("^"+Y.call(X).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),et=R?j.Buffer:void 0,nt=j.Symbol,rt=j.Uint8Array,it=G.propertyIsEnumerable,st=W.splice,ot=nt?nt.toStringTag:void 0,lt=Object.getOwnPropertySymbols,at=et?et.isBuffer:void 0,ct=($=Object.keys,V=Object,function(t){return $(V(t))}),ut=Bt(j,"DataView"),ht=Bt(j,"Map"),dt=Bt(j,"Promise"),ft=Bt(j,"Set"),pt=Bt(j,"WeakMap"),gt=Bt(Object,"create"),bt=Pt(ut),mt=Pt(ht),vt=Pt(dt),yt=Pt(ft),wt=Pt(pt),xt=nt?nt.prototype:void 0,Nt=xt?xt.valueOf:void 0;function kt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function At(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function _t(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function Et(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new _t;++e<n;)this.add(t[e])}function Ct(t){var e=this.__data__=new At(t);this.size=e.size}function Tt(t,e){for(var n=t.length;n--;)if(Ut(t[n][0],e))return n;return-1}function qt(t){return null==t?void 0===t?A:m:ot&&ot in Object(t)?function(t){var e=X.call(t,ot),n=t[ot];try{t[ot]=void 0;var r=!0}catch(t){}var i=J.call(t);return r&&(e?t[ot]=n:delete t[ot]),i}(t):function(t){return J.call(t)}(t)}function Lt(t){return Kt(t)&&qt(t)==l}function St(t,e,n,r,o){return t===e||(null==t||null==e||!Kt(t)&&!Kt(e)?t!=t&&e!=e:function(t,e,n,r,o,c){var f=Ht(t),p=Ht(e),m=f?a:It(t),y=p?a:It(e),w=(m=m==l?v:m)==v,A=(y=y==l?v:y)==v,_=m==y;if(_&&Ft(t)){if(!Ft(e))return!1;f=!0,w=!1}if(_&&!w)return c||(c=new Ct),f||Gt(t)?Ot(t,e,n,r,o,c):function(t,e,n,r,o,l,a){switch(n){case C:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case E:return!(t.byteLength!=e.byteLength||!l(new rt(t),new rt(e)));case u:case h:case b:return Ut(+t,+e);case d:return t.name==e.name&&t.message==e.message;case x:case k:return t==e+"";case g:var c=z;case N:var f=r&i;if(c||(c=H),t.size!=e.size&&!f)return!1;var p=a.get(t);if(p)return p==e;r|=s,a.set(t,e);var m=Ot(c(t),c(e),r,o,l,a);return a.delete(t),m;case"[object Symbol]":if(Nt)return Nt.call(t)==Nt.call(e)}return!1}(t,e,m,n,r,o,c);if(!(n&i)){var T=w&&X.call(t,"__wrapped__"),q=A&&X.call(e,"__wrapped__");if(T||q){var L=T?t.value():t,S=q?e.value():e;return c||(c=new Ct),o(L,S,n,r,c)}}return!!_&&(c||(c=new Ct),function(t,e,n,r,s,o){var l=n&i,a=jt(t),c=a.length;if(c!=jt(e).length&&!l)return!1;for(var u=c;u--;){var h=a[u];if(!(l?h in e:X.call(e,h)))return!1}var d=o.get(t);if(d&&o.get(e))return d==e;var f=!0;o.set(t,e),o.set(e,t);for(var p=l;++u<c;){var g=t[h=a[u]],b=e[h];if(r)var m=l?r(b,g,h,e,t,o):r(g,b,h,t,e,o);if(!(void 0===m?g===b||s(g,b,n,r,o):m)){f=!1;break}p||(p="constructor"==h)}if(f&&!p){var v=t.constructor,y=e.constructor;v==y||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof y&&y instanceof y||(f=!1)}return o.delete(t),o.delete(e),f}(t,e,n,r,o,c))}(t,e,n,r,St,o))}function Ot(t,e,n,r,o,l){var a=n&i,c=t.length,u=e.length;if(c!=u&&!(a&&u>c))return!1;var h=l.get(t);if(h&&l.get(e))return h==e;var d=-1,f=!0,p=n&s?new Et:void 0;for(l.set(t,e),l.set(e,t);++d<c;){var g=t[d],b=e[d];if(r)var m=a?r(b,g,d,e,t,l):r(g,b,d,t,e,l);if(void 0!==m){if(m)continue;f=!1;break}if(p){if(!U(e,(function(t,e){if(i=e,!p.has(i)&&(g===t||o(g,t,n,r,l)))return p.push(e);var i}))){f=!1;break}}else if(g!==b&&!o(g,b,n,r,l)){f=!1;break}}return l.delete(t),l.delete(e),f}function jt(t){return function(t,e,n){var r=e(t);return Ht(t)?r:function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}(r,n(t))}(t,Zt,Rt)}function Mt(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function Bt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!Wt(t)||function(t){return!!Q&&Q in t}(t))&&($t(t)?tt:T).test(Pt(t))}(n)?n:void 0}kt.prototype.clear=function(){this.__data__=gt?gt(null):{},this.size=0},kt.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},kt.prototype.get=function(t){var e=this.__data__;if(gt){var n=e[t];return n===r?void 0:n}return X.call(e,t)?e[t]:void 0},kt.prototype.has=function(t){var e=this.__data__;return gt?void 0!==e[t]:X.call(e,t)},kt.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=gt&&void 0===e?r:e,this},At.prototype.clear=function(){this.__data__=[],this.size=0},At.prototype.delete=function(t){var e=this.__data__,n=Tt(e,t);return!(n<0||(n==e.length-1?e.pop():st.call(e,n,1),--this.size,0))},At.prototype.get=function(t){var e=this.__data__,n=Tt(e,t);return n<0?void 0:e[n][1]},At.prototype.has=function(t){return Tt(this.__data__,t)>-1},At.prototype.set=function(t,e){var n=this.__data__,r=Tt(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},_t.prototype.clear=function(){this.size=0,this.__data__={hash:new kt,map:new(ht||At),string:new kt}},_t.prototype.delete=function(t){var e=Mt(this,t).delete(t);return this.size-=e?1:0,e},_t.prototype.get=function(t){return Mt(this,t).get(t)},_t.prototype.has=function(t){return Mt(this,t).has(t)},_t.prototype.set=function(t,e){var n=Mt(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},Et.prototype.add=Et.prototype.push=function(t){return this.__data__.set(t,r),this},Et.prototype.has=function(t){return this.__data__.has(t)},Ct.prototype.clear=function(){this.__data__=new At,this.size=0},Ct.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},Ct.prototype.get=function(t){return this.__data__.get(t)},Ct.prototype.has=function(t){return this.__data__.has(t)},Ct.prototype.set=function(t,e){var n=this.__data__;if(n instanceof At){var r=n.__data__;if(!ht||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new _t(r)}return n.set(t,e),this.size=n.size,this};var Rt=lt?function(t){return null==t?[]:(t=Object(t),function(e,n){for(var r=-1,i=null==e?0:e.length,s=0,o=[];++r<i;){var l=e[r];a=l,it.call(t,a)&&(o[s++]=l)}var a;return o}(lt(t)))}:function(){return[]},It=qt;function Dt(t,e){return!!(e=null==e?o:e)&&("number"==typeof t||q.test(t))&&t>-1&&t%1==0&&t<e}function Pt(t){if(null!=t){try{return Y.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function Ut(t,e){return t===e||t!=t&&e!=e}(ut&&It(new ut(new ArrayBuffer(1)))!=C||ht&&It(new ht)!=g||dt&&It(dt.resolve())!=y||ft&&It(new ft)!=N||pt&&It(new pt)!=_)&&(It=function(t){var e=qt(t),n=e==v?t.constructor:void 0,r=n?Pt(n):"";if(r)switch(r){case bt:return C;case mt:return g;case vt:return y;case yt:return N;case wt:return _}return e});var zt=Lt(function(){return arguments}())?Lt:function(t){return Kt(t)&&X.call(t,"callee")&&!it.call(t,"callee")},Ht=Array.isArray,Ft=at||function(){return!1};function $t(t){if(!Wt(t))return!1;var e=qt(t);return e==f||e==p||e==c||e==w}function Vt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=o}function Wt(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Kt(t){return null!=t&&"object"==typeof t}var Gt=P?function(t){return function(e){return t(e)}}(P):function(t){return Kt(t)&&Vt(t.length)&&!!L[qt(t)]};function Zt(t){return null!=(e=t)&&Vt(e.length)&&!$t(e)?function(t,e){var n=Ht(t),r=!n&&zt(t),i=!n&&!r&&Ft(t),s=!n&&!r&&!i&&Gt(t),o=n||r||i||s,l=o?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],a=l.length;for(var c in t)!X.call(t,c)||o&&("length"==c||i&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Dt(c,a))||l.push(c);return l}(t):function(t){if(n=(e=t)&&e.constructor,e!==("function"==typeof n&&n.prototype||G))return ct(t);var e,n,r=[];for(var i in Object(t))X.call(t,i)&&"constructor"!=i&&r.push(i);return r}(t);var e}t.exports=function(t,e){return St(t,e)}},930:function(t,e,n){t=n.nmd(t);var r="__lodash_hash_undefined__",i=9007199254740991,s="[object Arguments]",o="[object AsyncFunction]",l="[object Function]",a="[object GeneratorFunction]",c="[object Null]",u="[object Object]",h="[object Proxy]",d="[object Undefined]",f=/^\[object .+?Constructor\]$/,p=/^(?:0|[1-9]\d*)$/,g={};g["[object Float32Array]"]=g["[object Float64Array]"]=g["[object Int8Array]"]=g["[object Int16Array]"]=g["[object Int32Array]"]=g["[object Uint8Array]"]=g["[object Uint8ClampedArray]"]=g["[object Uint16Array]"]=g["[object Uint32Array]"]=!0,g[s]=g["[object Array]"]=g["[object ArrayBuffer]"]=g["[object Boolean]"]=g["[object DataView]"]=g["[object Date]"]=g["[object Error]"]=g[l]=g["[object Map]"]=g["[object Number]"]=g[u]=g["[object RegExp]"]=g["[object Set]"]=g["[object String]"]=g["[object WeakMap]"]=!1;var b,m,v,y="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,w="object"==typeof self&&self&&self.Object===Object&&self,x=y||w||Function("return this")(),N=e&&!e.nodeType&&e,k=N&&t&&!t.nodeType&&t,A=k&&k.exports===N,_=A&&y.process,E=function(){try{return k&&k.require&&k.require("util").types||_&&_.binding&&_.binding("util")}catch(t){}}(),C=E&&E.isTypedArray,T=Array.prototype,q=Function.prototype,L=Object.prototype,S=x["__core-js_shared__"],O=q.toString,j=L.hasOwnProperty,M=(b=/[^.]+$/.exec(S&&S.keys&&S.keys.IE_PROTO||""))?"Symbol(src)_1."+b:"",B=L.toString,R=O.call(Object),I=RegExp("^"+O.call(j).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),D=A?x.Buffer:void 0,P=x.Symbol,U=x.Uint8Array,z=(D&&D.allocUnsafe,m=Object.getPrototypeOf,v=Object,function(t){return m(v(t))}),H=Object.create,F=L.propertyIsEnumerable,$=T.splice,V=P?P.toStringTag:void 0,W=function(){try{var t=dt(Object,"defineProperty");return t({},"",{}),t}catch(t){}}(),K=D?D.isBuffer:void 0,G=Math.max,Z=Date.now,Y=dt(x,"Map"),X=dt(Object,"create"),Q=function(){function t(){}return function(e){if(!At(e))return{};if(H)return H(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}();function J(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function tt(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function et(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function nt(t){var e=this.__data__=new tt(t);this.size=e.size}function rt(t,e,n){(void 0!==n&&!mt(t[e],n)||void 0===n&&!(e in t))&&ot(t,e,n)}function it(t,e,n){var r=t[e];j.call(t,e)&&mt(r,n)&&(void 0!==n||e in t)||ot(t,e,n)}function st(t,e){for(var n=t.length;n--;)if(mt(t[n][0],e))return n;return-1}function ot(t,e,n){"__proto__"==e&&W?W(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function lt(t){return null==t?void 0===t?d:c:V&&V in Object(t)?function(t){var e=j.call(t,V),n=t[V];try{t[V]=void 0;var r=!0}catch(t){}var i=B.call(t);return r&&(e?t[V]=n:delete t[V]),i}(t):function(t){return B.call(t)}(t)}function at(t){return _t(t)&&lt(t)==s}function ct(t,e,n,r,i){t!==e&&function(t,e,n){for(var r=-1,i=Object(t),s=n(t),o=s.length;o--;){var l=s[++r];if(!1===e(i[l],l))break}}(e,(function(s,o){if(i||(i=new nt),At(s))!function(t,e,n,r,i,s,o){var l=gt(t,n),a=gt(e,n),c=o.get(a);if(c)rt(t,n,c);else{var h,d,f,p,g,b=s?s(l,a,n+"",t,e,o):void 0,m=void 0===b;if(m){var v=yt(a),y=!v&&xt(a),w=!v&&!y&&Et(a);b=a,v||y||w?yt(l)?b=l:_t(g=l)&&wt(g)?b=function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e}(l):y?(m=!1,b=function(t,e){return t.slice()}(a)):w?(m=!1,p=new(f=(h=a).buffer).constructor(f.byteLength),new U(p).set(new U(f)),d=p,b=new h.constructor(d,h.byteOffset,h.length)):b=[]:function(t){if(!_t(t)||lt(t)!=u)return!1;var e=z(t);if(null===e)return!0;var n=j.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&O.call(n)==R}(a)||vt(a)?(b=l,vt(l)?b=function(t){return function(t,e,n,r){var i=!n;n||(n={});for(var s=-1,o=e.length;++s<o;){var l=e[s],a=void 0;void 0===a&&(a=t[l]),i?ot(n,l,a):it(n,l,a)}return n}(t,Ct(t))}(l):At(l)&&!Nt(l)||(b=function(t){return"function"!=typeof t.constructor||pt(t)?{}:Q(z(t))}(a))):m=!1}m&&(o.set(a,b),i(b,a,r,s,o),o.delete(a)),rt(t,n,b)}}(t,e,o,n,ct,r,i);else{var l=r?r(gt(t,o),s,o+"",t,e,i):void 0;void 0===l&&(l=s),rt(t,o,l)}}),Ct)}J.prototype.clear=function(){this.__data__=X?X(null):{},this.size=0},J.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},J.prototype.get=function(t){var e=this.__data__;if(X){var n=e[t];return n===r?void 0:n}return j.call(e,t)?e[t]:void 0},J.prototype.has=function(t){var e=this.__data__;return X?void 0!==e[t]:j.call(e,t)},J.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=X&&void 0===e?r:e,this},tt.prototype.clear=function(){this.__data__=[],this.size=0},tt.prototype.delete=function(t){var e=this.__data__,n=st(e,t);return!(n<0||(n==e.length-1?e.pop():$.call(e,n,1),--this.size,0))},tt.prototype.get=function(t){var e=this.__data__,n=st(e,t);return n<0?void 0:e[n][1]},tt.prototype.has=function(t){return st(this.__data__,t)>-1},tt.prototype.set=function(t,e){var n=this.__data__,r=st(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},et.prototype.clear=function(){this.size=0,this.__data__={hash:new J,map:new(Y||tt),string:new J}},et.prototype.delete=function(t){var e=ht(this,t).delete(t);return this.size-=e?1:0,e},et.prototype.get=function(t){return ht(this,t).get(t)},et.prototype.has=function(t){return ht(this,t).has(t)},et.prototype.set=function(t,e){var n=ht(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},nt.prototype.clear=function(){this.__data__=new tt,this.size=0},nt.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},nt.prototype.get=function(t){return this.__data__.get(t)},nt.prototype.has=function(t){return this.__data__.has(t)},nt.prototype.set=function(t,e){var n=this.__data__;if(n instanceof tt){var r=n.__data__;if(!Y||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new et(r)}return n.set(t,e),this.size=n.size,this};var ut=W?function(t,e){return W(t,"toString",{configurable:!0,enumerable:!1,value:(n=e,function(){return n}),writable:!0});var n}:Lt;function ht(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function dt(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){return!(!At(t)||function(t){return!!M&&M in t}(t))&&(Nt(t)?I:f).test(function(t){if(null!=t){try{return O.call(t)}catch(t){}try{return t+""}catch(t){}}return""}(t))}(n)?n:void 0}function ft(t,e){var n=typeof t;return!!(e=null==e?i:e)&&("number"==n||"symbol"!=n&&p.test(t))&&t>-1&&t%1==0&&t<e}function pt(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||L)}function gt(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}var bt=function(t){var e=0,n=0;return function(){var r=Z(),i=16-(r-n);if(n=r,i>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(ut);function mt(t,e){return t===e||t!=t&&e!=e}var vt=at(function(){return arguments}())?at:function(t){return _t(t)&&j.call(t,"callee")&&!F.call(t,"callee")},yt=Array.isArray;function wt(t){return null!=t&&kt(t.length)&&!Nt(t)}var xt=K||function(){return!1};function Nt(t){if(!At(t))return!1;var e=lt(t);return e==l||e==a||e==o||e==h}function kt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=i}function At(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function _t(t){return null!=t&&"object"==typeof t}var Et=C?function(t){return function(e){return t(e)}}(C):function(t){return _t(t)&&kt(t.length)&&!!g[lt(t)]};function Ct(t){return wt(t)?function(t,e){var n=yt(t),r=!n&&vt(t),i=!n&&!r&&xt(t),s=!n&&!r&&!i&&Et(t),o=n||r||i||s,l=o?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],a=l.length;for(var c in t)o&&("length"==c||i&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||ft(c,a))||l.push(c);return l}(t):function(t){if(!At(t))return function(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}(t);var e=pt(t),n=[];for(var r in t)("constructor"!=r||!e&&j.call(t,r))&&n.push(r);return n}(t)}var Tt,qt=(Tt=function(t,e,n){ct(t,e,n)},function(t,e){return bt(function(t,e,n){return e=G(void 0===e?t.length-1:e,0),function(){for(var r=arguments,i=-1,s=G(r.length-e,0),o=Array(s);++i<s;)o[i]=r[e+i];i=-1;for(var l=Array(e+1);++i<e;)l[i]=r[i];return l[e]=n(o),function(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}(t,this,l)}}(t,void 0,Lt),t+"")}((function(t,e){var n=-1,r=e.length,i=r>1?e[r-1]:void 0,s=r>2?e[2]:void 0;for(i=Tt.length>3&&"function"==typeof i?(r--,i):void 0,s&&function(t,e,n){if(!At(n))return!1;var r=typeof e;return!!("number"==r?wt(n)&&ft(e,n.length):"string"==r&&e in n)&&mt(n[e],t)}(e[0],e[1],s)&&(i=r<3?void 0:i,r=1),t=Object(t);++n<r;){var o=e[n];o&&Tt(t,o,n)}return t})));function Lt(t){return t}t.exports=qt},382:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(861),i=n(842);var s;!function(t){t.compose=function(t={},e={},n=!1){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});let i=r(e);n||(i=Object.keys(i).reduce(((t,e)=>(null!=i[e]&&(t[e]=i[e]),t)),{}));for(const n in t)void 0!==t[n]&&void 0===e[n]&&(i[n]=t[n]);return Object.keys(i).length>0?i:void 0},t.diff=function(t={},e={}){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});const n=Object.keys(t).concat(Object.keys(e)).reduce(((n,r)=>(i(t[r],e[r])||(n[r]=void 0===e[r]?null:e[r]),n)),{});return Object.keys(n).length>0?n:void 0},t.invert=function(t={},e={}){t=t||{};const n=Object.keys(e).reduce(((n,r)=>(e[r]!==t[r]&&void 0!==t[r]&&(n[r]=e[r]),n)),{});return Object.keys(t).reduce(((n,r)=>(t[r]!==e[r]&&void 0===e[r]&&(n[r]=null),n)),n)},t.transform=function(t,e,n=!1){if("object"!=typeof t)return e;if("object"!=typeof e)return;if(!n)return e;const r=Object.keys(e).reduce(((n,r)=>(void 0===t[r]&&(n[r]=e[r]),n)),{});return Object.keys(r).length>0?r:void 0}}(s||(s={})),e.default=s},32:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributeMap=e.OpIterator=e.Op=void 0;const r=n(386),i=n(861),s=n(842),o=n(382);e.AttributeMap=o.default;const l=n(427);e.Op=l.default;const a=n(505);e.OpIterator=a.default;const c=String.fromCharCode(0),u=(t,e)=>{if("object"!=typeof t||null===t)throw new Error("cannot retain a "+typeof t);if("object"!=typeof e||null===e)throw new Error("cannot retain a "+typeof e);const n=Object.keys(t)[0];if(!n||n!==Object.keys(e)[0])throw new Error(`embed types not matched: ${n} != ${Object.keys(e)[0]}`);return[n,t[n],e[n]]};class h{constructor(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]}static registerEmbed(t,e){this.handlers[t]=e}static unregisterEmbed(t){delete this.handlers[t]}static getHandler(t){const e=this.handlers[t];if(!e)throw new Error(`no handlers for embed type "${t}"`);return e}insert(t,e){const n={};return"string"==typeof t&&0===t.length?this:(n.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n))}delete(t){return t<=0?this:this.push({delete:t})}retain(t,e){if("number"==typeof t&&t<=0)return this;const n={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n)}push(t){let e=this.ops.length,n=this.ops[e-1];if(t=i(t),"object"==typeof n){if("number"==typeof t.delete&&"number"==typeof n.delete)return this.ops[e-1]={delete:n.delete+t.delete},this;if("number"==typeof n.delete&&null!=t.insert&&(e-=1,n=this.ops[e-1],"object"!=typeof n))return this.ops.unshift(t),this;if(s(t.attributes,n.attributes)){if("string"==typeof t.insert&&"string"==typeof n.insert)return this.ops[e-1]={insert:n.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof n.retain)return this.ops[e-1]={retain:n.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this}chop(){const t=this.ops[this.ops.length-1];return t&&"number"==typeof t.retain&&!t.attributes&&this.ops.pop(),this}filter(t){return this.ops.filter(t)}forEach(t){this.ops.forEach(t)}map(t){return this.ops.map(t)}partition(t){const e=[],n=[];return this.forEach((r=>{(t(r)?e:n).push(r)})),[e,n]}reduce(t,e){return this.ops.reduce(t,e)}changeLength(){return this.reduce(((t,e)=>e.insert?t+l.default.length(e):e.delete?t-e.delete:t),0)}length(){return this.reduce(((t,e)=>t+l.default.length(e)),0)}slice(t=0,e=1/0){const n=[],r=new a.default(this.ops);let i=0;for(;i<e&&r.hasNext();){let s;i<t?s=r.next(t-i):(s=r.next(e-i),n.push(s)),i+=l.default.length(s)}return new h(n)}compose(t){const e=new a.default(this.ops),n=new a.default(t.ops),r=[],i=n.peek();if(null!=i&&"number"==typeof i.retain&&null==i.attributes){let t=i.retain;for(;"insert"===e.peekType()&&e.peekLength()<=t;)t-=e.peekLength(),r.push(e.next());i.retain-t>0&&n.next(i.retain-t)}const l=new h(r);for(;e.hasNext()||n.hasNext();)if("insert"===n.peekType())l.push(n.next());else if("delete"===e.peekType())l.push(e.next());else{const t=Math.min(e.peekLength(),n.peekLength()),r=e.next(t),i=n.next(t);if(i.retain){const a={};if("number"==typeof r.retain)a.retain="number"==typeof i.retain?t:i.retain;else if("number"==typeof i.retain)null==r.retain?a.insert=r.insert:a.retain=r.retain;else{const t=null==r.retain?"insert":"retain",[e,n,s]=u(r[t],i.retain),o=h.getHandler(e);a[t]={[e]:o.compose(n,s,"retain"===t)}}const c=o.default.compose(r.attributes,i.attributes,"number"==typeof r.retain);if(c&&(a.attributes=c),l.push(a),!n.hasNext()&&s(l.ops[l.ops.length-1],a)){const t=new h(e.rest());return l.concat(t).chop()}}else"number"==typeof i.delete&&("number"==typeof r.retain||"object"==typeof r.retain&&null!==r.retain)&&l.push(i)}return l.chop()}concat(t){const e=new h(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e}diff(t,e){if(this.ops===t.ops)return new h;const n=[this,t].map((e=>e.map((n=>{if(null!=n.insert)return"string"==typeof n.insert?n.insert:c;throw new Error("diff() called "+(e===t?"on":"with")+" non-document")})).join(""))),i=new h,l=r(n[0],n[1],e,!0),u=new a.default(this.ops),d=new a.default(t.ops);return l.forEach((t=>{let e=t[1].length;for(;e>0;){let n=0;switch(t[0]){case r.INSERT:n=Math.min(d.peekLength(),e),i.push(d.next(n));break;case r.DELETE:n=Math.min(e,u.peekLength()),u.next(n),i.delete(n);break;case r.EQUAL:n=Math.min(u.peekLength(),d.peekLength(),e);const t=u.next(n),l=d.next(n);s(t.insert,l.insert)?i.retain(n,o.default.diff(t.attributes,l.attributes)):i.push(l).delete(n)}e-=n}})),i.chop()}eachLine(t,e="\n"){const n=new a.default(this.ops);let r=new h,i=0;for(;n.hasNext();){if("insert"!==n.peekType())return;const s=n.peek(),o=l.default.length(s)-n.peekLength(),a="string"==typeof s.insert?s.insert.indexOf(e,o)-o:-1;if(a<0)r.push(n.next());else if(a>0)r.push(n.next(a));else{if(!1===t(r,n.next(1).attributes||{},i))return;i+=1,r=new h}}r.length()>0&&t(r,{},i)}invert(t){const e=new h;return this.reduce(((n,r)=>{if(r.insert)e.delete(l.default.length(r));else{if("number"==typeof r.retain&&null==r.attributes)return e.retain(r.retain),n+r.retain;if(r.delete||"number"==typeof r.retain){const i=r.delete||r.retain;return t.slice(n,n+i).forEach((t=>{r.delete?e.push(t):r.retain&&r.attributes&&e.retain(l.default.length(t),o.default.invert(r.attributes,t.attributes))})),n+i}if("object"==typeof r.retain&&null!==r.retain){const i=t.slice(n,n+1),s=new a.default(i.ops).next(),[l,c,d]=u(r.retain,s.insert),f=h.getHandler(l);return e.retain({[l]:f.invert(c,d)},o.default.invert(r.attributes,s.attributes)),n+1}}return n}),0),e.chop()}transform(t,e=!1){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);const n=t,r=new a.default(this.ops),i=new a.default(n.ops),s=new h;for(;r.hasNext()||i.hasNext();)if("insert"!==r.peekType()||!e&&"insert"===i.peekType())if("insert"===i.peekType())s.push(i.next());else{const t=Math.min(r.peekLength(),i.peekLength()),n=r.next(t),l=i.next(t);if(n.delete)continue;if(l.delete)s.push(l);else{const r=n.retain,i=l.retain;let a="object"==typeof i&&null!==i?i:t;if("object"==typeof r&&null!==r&&"object"==typeof i&&null!==i){const t=Object.keys(r)[0];if(t===Object.keys(i)[0]){const n=h.getHandler(t);n&&(a={[t]:n.transform(r[t],i[t],e)})}}s.retain(a,o.default.transform(n.attributes,l.attributes,e))}}else s.retain(l.default.length(r.next()));return s.chop()}transformPosition(t,e=!1){e=!!e;const n=new a.default(this.ops);let r=0;for(;n.hasNext()&&r<=t;){const i=n.peekLength(),s=n.peekType();n.next(),"delete"!==s?("insert"===s&&(r<t||!e)&&(t+=i),r+=i):t-=Math.min(i,t-r)}return t}}h.Op=l.default,h.OpIterator=a.default,h.AttributeMap=o.default,h.handlers={},e.default=h,t.exports=h,t.exports.default=h},427:function(t,e){"use strict";var n;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t.length=function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"object"==typeof t.retain&&null!==t.retain?1:"string"==typeof t.insert?t.insert.length:1}}(n||(n={})),e.default=n},505:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const r=n(427);e.default=class{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);const e=this.ops[this.index];if(e){const n=this.offset,i=r.default.length(e);if(t>=i-n?(t=i-n,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};{const r={};return e.attributes&&(r.attributes=e.attributes),"number"==typeof e.retain?r.retain=t:"object"==typeof e.retain&&null!==e.retain?r.retain=e.retain:"string"==typeof e.insert?r.insert=e.insert.substr(n,t):r.insert=e.insert,r}}return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?r.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const t=this.ops[this.index];return t?"number"==typeof t.delete?"delete":"number"==typeof t.retain||"object"==typeof t.retain&&null!==t.retain?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);{const t=this.offset,e=this.index,n=this.next(),r=this.ops.slice(this.index);return this.offset=t,this.index=e,[n].concat(r)}}return[]}}},912:function(t){"use strict";t.exports=r}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var s=e[r]={id:r,loaded:!1,exports:{}};return t[r](s,s.exports,n),s.loaded=!0,s.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t};var i={};return function(){"use strict";n.d(i,{default:function(){return tr}});var t=n(912),e=n.n(t),r=n(32),s=n.n(r),o='<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M36 19H12" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 9H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 29H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M36 39H12" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',l='<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M42 9H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M34 19H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 29H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M34 39H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',a='<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M42 9H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 19H14" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 29H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 39H14" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>';const c=["data-row","width","height","colspan","rowspan","style"],u={"border-style":"none","border-color":"","border-width":"","background-color":"",width:"",height:"",padding:"","text-align":"left","vertical-align":"middle"},h=["border-style","border-color","border-width","background-color","width","height","padding","text-align","vertical-align"],d=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","currentcolor","currentcolor","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","transparent","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],f=["border-style","border-color","border-width","background-color","width","height","align"],p=e().import("formats/list"),g=e().import("blots/container"),b=["colspan","rowspan"];class m extends g{static create(t){const e=super.create();for(const e of b)"1"==t[e]&&delete t[e];const n=Object.keys(t);for(const r of n)"data-row"===r?e.setAttribute(r,t[r]):"cellId"===r?e.setAttribute("data-cell",t[r]):e.setAttribute(`data-${r}`,t[r]);return e}format(t,e){return this.wrap(t,e)}static formats(t){const e=c.reduce(((e,n)=>{const r=n.includes("data")?n:`data-${n}`;return t.hasAttribute(r)&&(e[n]=t.getAttribute(r)),e}),{});e.cellId=t.getAttribute("data-cell");for(const t of b)e[t]||(e[t]="1");return e}formats(){const t=this.statics.formats(this.domNode,this.scroll);return{[this.statics.blotName]:t}}}m.blotName="table-list-container",m.className="table-list-container",m.tagName="OL";class v extends p{format(t,e,n){const r=this.formats()[this.statics.blotName];if("list"===t){const[t,i]=this.getCellFormats(this.parent);if(!e||e===r)return this.setReplace(n,t),this.replaceWith(V.blotName,i);if(e!==r)return this.replaceWith(this.statics.blotName,e)}else if(t===m.blotName){"string"==typeof e&&(e={cellId:e});const[n,r]=this.getCorrectCellFormats(e);this.wrap(W.blotName,n),this.wrap(t,Object.assign(Object.assign({},n),{cellId:r}))}else{if("header"===t){const[t,r]=this.getCellFormats(this.parent);return this.setReplace(n,t),this.replaceWith("table-header",{cellId:r,value:e})}if(t===W.blotName){const n=this.getListContainer(this.parent);if(!n)return;const r=n.formats()[n.statics.blotName];this.wrap(t,e),this.wrap(m.blotName,Object.assign(Object.assign({},r),e))}else if(t!==this.statics.blotName||e)super.format(t,e);else{const[,t]=this.getCellFormats(this.parent);this.replaceWith(V.blotName,t)}}}getCellFormats(t){return E(M(t))}getCorrectCellFormats(t){const e=M(this.parent);if(e){const[n,r]=E(e),i=Object.assign(Object.assign({},n),t),s=i.cellId||r;return delete i.cellId,[i,s]}{const e=t.cellId,n=Object.assign({},t);return delete n.cellId,[n,e]}}getListContainer(t){for(;t;){if(t.statics.blotName===m.blotName)return t;t=t.parent}return null}static register(){e().register(m)}setReplace(t,e){t?this.parent.replaceWith(W.blotName,e):this.wrap(W.blotName,e)}}v.blotName="table-list",v.className="table-list",e().register({"formats/table-list":v},!0),m.allowedChildren=[v],v.requiredContainer=m;const y=e().import("formats/header");class w extends y{static create(t){const{cellId:e,value:n}=t,r=super.create(n);return r.setAttribute("data-cell",e),r}format(t,e,n){if("header"===t){const t=this.statics.formats(this.domNode).value,n=this.domNode.getAttribute("data-cell");t!=e&&e?super.format("table-header",{cellId:n,value:e}):this.replaceWith(V.blotName,n)}else{if("list"===t){const[t,r]=this.getCellFormats(this.parent);return n?this.wrap(m.blotName,Object.assign(Object.assign({},t),{cellId:r})):this.wrap(W.blotName,t),this.replaceWith("table-list",e)}if(t===W.blotName)return this.wrap(t,e);if(t!==this.statics.blotName||e)super.format(t,e);else{const t=this.domNode.getAttribute("data-cell");this.replaceWith(V.blotName,t)}}}static formats(t){return{cellId:t.getAttribute("data-cell"),value:this.tagName.indexOf(t.tagName)+1}}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}getCellFormats(t){return E(M(t))}}w.blotName="table-header",w.className="ql-table-header",e().register({"formats/table-header":w},!0);var x=w;function N(t){if("string"!=typeof t||!t)return t;const e=t.slice(-2),n=t.slice(0,-2);return`${Math.round(parseFloat(n))}${e}`}function k(t){const e=document.createElement("div");return e.innerText=t,e.classList.add("ql-table-tooltip","ql-hidden"),e}function A(t){return t.replace(/mso.*?;/g,"")}function _(t){const[e]=t.descendant(V),[n]=t.descendant(m),[r]=t.descendant(x);return e||n||r}function E(t){const e=W.formats(t.domNode),n=_(t);if(n)return[e,C(n.formats()[n.statics.blotName])];{const t=e["data-row"].split("-")[1];return[e,`cell-${t}`]}}function C(t){return t instanceof Object?t.cellId:t}function T(t,e){return t.closest(e)}function q(t,e){return{left:Math.min(t.left,e.left),right:Math.max(t.right,e.right),top:Math.min(t.top,e.top),bottom:Math.max(t.bottom,e.bottom)}}function L(t,n,r){const i=e().find(n).descendants(Y);let s=0;return i.reduce(((e,n)=>{const{left:i,width:o}=j(n.domNode,r);return s=s||i,s+2>=t.left&&s-2+o<=t.right&&e.push(n.domNode),s+=o,e}),[])}function S(t,n,r,i){return e().find(n).descendants(W).reduce(((e,n)=>{const{left:s,top:o,width:l,height:a}=j(n.domNode,r);switch(i){case"column":(s+2>=t.left&&s-2+l<=t.right||s+2<t.right&&t.right<s-2+l||t.left>s+2&&t.left<s-2+l)&&e.push(n.domNode);break;case"row":break;default:s+2>=t.left&&s-2+l<=t.right&&o+2>=t.top&&o-2+a<=t.bottom&&e.push(n.domNode)}return e}),[])}function O(t){return t.replace(/data-[a-z]+="[^"]*"/g,"").replace(/class="[^"]*"/g,(t=>t.replace("ql-cell-selected","").replace("ql-cell-focused","").replace("ql-table-block",""))).replace(/class="\s*"/g,"")}function j(t,e){const n=t.getBoundingClientRect(),r=e.getBoundingClientRect(),i=n.left-r.left-e.scrollLeft,s=n.top-r.top-e.scrollTop,o=n.width,l=n.height;return{left:i,top:s,width:o,height:l,right:i+o,bottom:s+l}}function M(t){for(;t;){if(t.statics.blotName===W.blotName)return t;t=t.parent}return null}function B(t,e){const n=getComputedStyle(t),r=t.style;return e.reduce(((t,e)=>{return t[e]=(i=r.getPropertyValue(e)||n.getPropertyValue(e)).startsWith("rgba(")?function(t){t=t.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"");const e=Math.round(+t[0]),n=Math.round(+t[1]),r=Math.round(+t[2]),i=Math.round(255*+t[3]).toString(16).toUpperCase().padStart(2,"0");return"#"+((1<<24)+(e<<16)+(n<<8)+r).toString(16).slice(1)+i}(i):i.startsWith("rgb(")?`#${(i=i.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"")).split(",").map((t=>`00${parseInt(t,10).toString(16)}`.slice(-2))).join("")}`:i,t;var i}),{})}function R(t){return!t||!!/^#([A-Fa-f0-9]{3,6})$/.test(t)||!!/^rgb\((\d{1,3}), (\d{1,3}), (\d{1,3})\)$/.test(t)||function(t){for(const e of d)if(e===t)return!0;return!1}(t)}function I(t){if(!t)return!0;const e=t.slice(-2);return"px"===e||"em"===e||!/[a-z]/.test(e)&&!isNaN(parseFloat(e))}function D(t,e){for(const n in e)t.setAttribute(n,e[n])}function P(t,e){const n=t.style;if(n)for(const t in e)n.setProperty(t,e[t]);else t.setAttribute("style",e.toString())}function U(t,n,r){const i=e().find(t);if(!i)return;const s=i.colgroup(),o=i.temporary();if(s){let t=0;const e=s.domNode.querySelectorAll("col");for(const n of e)t+=~~n.getAttribute("width");P(o.domNode,{width:`${t}px`})}else P(o.domNode,{width:~~(n.width+r)+"px"})}const z=e().import("blots/block"),H=e().import("blots/container"),F=["border","cellspacing","style","data-class"],$=["width"];class V extends z{static create(t){const e=super.create();return t?e.setAttribute("data-cell",t):e.setAttribute("data-cell",J()),e}format(t,e){const n=this.formats()[this.statics.blotName];if(t===W.blotName&&e)return this.wrap(K.blotName),this.wrap(t,e);if(t===Q.blotName)this.wrap(t,e);else{if("header"===t)return this.replaceWith("table-header",{cellId:n,value:e});if("table-header"===t&&e)return this.wrapTableCell(this.parent),this.replaceWith(t,e);if("list"===t||"table-list"===t&&e){const t=this.getCellFormats(this.parent);return this.wrap(m.blotName,Object.assign(Object.assign({},t),{cellId:n})),this.replaceWith("table-list",e)}super.format(t,e)}}formats(){const t=this.attributes.values(),e=this.domNode.getAttribute("data-cell");return null!=e&&(t[this.statics.blotName]=e),t}getCellFormats(t){const e=M(t);if(!e)return{};const[n]=E(e);return n}wrapTableCell(t){const e=M(t);if(!e)return;const[n]=E(e);this.wrap(W.blotName,n)}}V.blotName="table-cell-block",V.className="ql-table-block",V.tagName="P";class W extends H{checkMerge(){if(super.checkMerge()&&null!=this.next.children.head&&this.next.children.head.formats){const t=this.children.head.formats()[this.children.head.statics.blotName],e=this.children.tail.formats()[this.children.tail.statics.blotName],n=this.next.children.head.formats()[this.next.children.head.statics.blotName],r=this.next.children.tail.formats()[this.next.children.tail.statics.blotName],i=C(t),s=C(e),o=C(n),l=C(r);return i===s&&i===o&&i===l}return!1}static create(t){const e=super.create(),n=Object.keys(t);for(const r of n)t[r]&&e.setAttribute(r,t[r]);return e}static formats(t){const e=this.getEmptyRowspan(t),n=c.reduce(((n,r)=>(t.hasAttribute(r)&&(n[r]="rowspan"===r&&e?""+(~~t.getAttribute(r)-e):A(t.getAttribute(r))),n)),{});return this.hasColgroup(t)&&(delete n.width,n.style&&(n.style=n.style.replace(/width.*?;/g,""))),n}formats(){const t=this.statics.formats(this.domNode,this.scroll);return{[this.statics.blotName]:t}}static getEmptyRowspan(t){let e=t.parentElement.nextElementSibling,n=0;for(;e&&"TR"===e.tagName&&!e.innerHTML.replace(/\s/g,"");)n++,e=e.nextElementSibling;return n}static hasColgroup(t){for(;t&&"TBODY"!==t.tagName;)t=t.parentElement;for(;t;){if("COLGROUP"===t.tagName)return!0;t=t.previousElementSibling}return!1}html(){return this.domNode.outerHTML.replace(/<(ol)[^>]*><li[^>]* data-list="bullet">(?:.*?)<\/li><\/(ol)>/gi,((t,e,n)=>t.replace(e,"ul").replace(n,"ul")))}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}setChildrenId(t){this.children.forEach((e=>{e.domNode.setAttribute("data-cell",t)}))}table(){let t=this.parent;for(;null!=t&&"table-container"!==t.statics.blotName;)t=t.parent;return t}optimize(t){super.optimize(t),this.children.forEach((t=>{if(null!=t.next&&C(t.formats()[t.statics.blotName])!==C(t.next.formats()[t.next.statics.blotName])){const e=this.splitAfter(t);e&&e.optimize(),this.prev&&this.prev.optimize()}}))}}W.blotName="table-cell",W.tagName="TD";class K extends H{checkMerge(){if(super.checkMerge()&&null!=this.next.children.head&&this.next.children.head.formats){const t=this.children.head.formats()[this.children.head.statics.blotName],e=this.children.tail.formats()[this.children.tail.statics.blotName],n=this.next.children.head.formats()[this.next.children.head.statics.blotName],r=this.next.children.tail.formats()[this.next.children.tail.statics.blotName];return t["data-row"]===e["data-row"]&&t["data-row"]===n["data-row"]&&t["data-row"]===r["data-row"]}return!1}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}}K.blotName="table-row",K.tagName="TR";class G extends H{}G.blotName="table-body",G.tagName="TBODY";class Z extends z{static create(t){const e=super.create(),n=Object.keys(t),r=Q.defaultClassName;for(const i of n)"data-class"!==i||~t[i].indexOf(r)?e.setAttribute(i,t[i]):e.setAttribute(i,`${r} ${t[i]}`);return e}static formats(t){return F.reduce(((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e)),{})}formats(){const t=this.statics.formats(this.domNode,this.scroll);return{[this.statics.blotName]:t}}optimize(...t){if(this.statics.requiredContainer&&this.parent instanceof this.statics.requiredContainer){const t=this.formats()[this.statics.blotName];for(const e of F)t[e]?"data-class"===e?this.parent.domNode.setAttribute("class",t[e]):this.parent.domNode.setAttribute(e,t[e]):this.parent.domNode.removeAttribute(e)}super.optimize(...t)}}Z.blotName="table-temporary",Z.className="ql-table-temporary",Z.tagName="temporary";class Y extends z{static create(t){const e=super.create(),n=Object.keys(t);for(const r of n)e.setAttribute(r,t[r]);return e}static formats(t){return $.reduce(((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e)),{})}formats(){const t=this.statics.formats(this.domNode,this.scroll);return{[this.statics.blotName]:t}}html(){return this.domNode.outerHTML}}Y.blotName="table-col",Y.tagName="COL";class X extends H{}X.blotName="table-colgroup",X.tagName="COLGROUP";class Q extends H{colgroup(){const[t]=this.descendant(X);return t||this.findChild("table-colgroup")}deleteColumn(t,n,r,i=[]){const s=this.tbody(),o=this.descendants(W);if(null!=s&&null!=s.children.head)if(n.length===o.length)r();else{for(const[n,r]of t)this.setCellColspan(e().find(n),r);for(const t of[...n,...i])1===t.parentElement.children.length&&this.setCellRowspan(t.parentElement.previousElementSibling),t.remove()}}deleteRow(t,e){const n=this.tbody();if(null!=n&&null!=n.children.head)if(t.length===n.children.length)e();else{const e=new WeakMap,r=[],i=[],s=this.getMaxColumns(n.children.head.children);for(const n of t){const r=this.getCorrectRow(n,s);r&&r.children.forEach((n=>{var r;const s=~~n.domNode.getAttribute("rowspan")||1;if(s>1){const o=n.statics.blotName,[l]=E(n);if(t.includes(n.parent)){const t=null===(r=n.parent)||void 0===r?void 0:r.next;if(e.has(n)){const{rowspan:r}=e.get(n);e.set(n,{next:t,rowspan:r-1})}else e.set(n,{next:t,rowspan:s-1}),i.push(n)}else n.replaceWith(o,Object.assign(Object.assign({},l),{rowspan:s-1}))}}))}for(const t of i){const[n]=E(t),{right:i,width:s}=t.domNode.getBoundingClientRect(),{next:o,rowspan:l}=e.get(t);this.setColumnCells(o,r,{position:i,width:s},n,l,t)}for(const[t,e,n,i]of r){const r=this.scroll.create(W.blotName,e);i.moveChildren(r);const s=J();r.setChildrenId(s),t.insertBefore(r,n),i.remove()}for(const e of t)e.remove()}}deleteTable(){this.remove()}findChild(t){let e=this.children.head;for(;e;){if(e.statics.blotName===t)return e;e=e.next}return null}getCopyTable(){return this.domNode.outerHTML.replace(/<temporary[^>]*>(.*?)<\/temporary>/gi,"").replace(/<td[^>]*>(.*?)<\/td>/gi,(t=>O(t)))}getCorrectRow(t,e){let n=!1;for(;t&&!n;){if(e===this.getMaxColumns(t.children))return n=!0,t;t=t.prev}return t}getInsertRow(t,e,n){const r=this.tbody();if(null==r||null==r.children.head)return;const i=tt(),s=this.scroll.create(K.blotName),o=this.getMaxColumns(r.children.head.children);return this.getMaxColumns(t.children)===o?(t.children.forEach((t=>{const e={height:"24","data-row":i},n=~~t.domNode.getAttribute("colspan")||1;this.insertTableCell(n,e,s)})),s):(this.getCorrectRow(t.prev,o).children.forEach((t=>{const r={height:"24","data-row":i},o=~~t.domNode.getAttribute("colspan")||1,l=~~t.domNode.getAttribute("rowspan")||1;if(l>1)if(n>0&&!e)this.insertTableCell(o,r,s);else{const[e]=E(t);t.replaceWith(t.statics.blotName,Object.assign(Object.assign({},e),{rowspan:l+1}))}else this.insertTableCell(o,r,s)})),s)}getMaxColumns(t){return t.reduce(((t,e)=>t+(~~e.domNode.getAttribute("colspan")||1)),0)}insertColumn(t,e,n,r){const i=this.colgroup(),s=this.tbody();if(null==s||null==s.children.head)return;const o=[],l=[];let a=s.children.head;for(;a;){if(e&&r>0){const t=a.children.tail.domNode.getAttribute("data-row");o.push([a,t,null,null])}else this.setColumnCells(a,o,{position:t,width:n});a=a.next}if(i)if(e)l.push([i,null]);else{let e=0,n=0,r=i.children.head;for(;r;){const{left:s,width:o}=r.domNode.getBoundingClientRect();if(e=e||s,n=e+o,Math.abs(e-t)<=2){l.push([i,r]);break}if(Math.abs(n-t)<=2&&!r.next){l.push([i,null]);break}e+=o,r=r.next}}for(const[t,e,n]of o)t?this.insertColumnCell(t,e,n):this.setCellColspan(n,1);for(const[t,e]of l)this.insertCol(t,e)}insertCol(t,e){const n=this.scroll.create(Y.blotName,{width:"72"});t.insertBefore(n,e)}insertColumnCell(t,e,n){const r=this.colgroup()?{"data-row":e}:{"data-row":e,width:"72"},i=this.scroll.create(W.blotName,r),s=this.scroll.create(V.blotName,J());if(i.appendChild(s),!t){const e=this.tbody();t=this.scroll.create(K.blotName),e.insertBefore(t,null)}return t.insertBefore(i,n),s.optimize(),i}insertRow(t,e){const n=this.tbody();if(null==n||null==n.children.head)return;const r=n.children.at(t),i=r||n.children.at(t-1),s=this.getInsertRow(i,r,e);n.insertBefore(s,r)}insertTableCell(t,e,n){t>1?Object.assign(e,{colspan:t}):delete e.colspan;const r=this.scroll.create(W.blotName,e),i=this.scroll.create(V.blotName,J());r.appendChild(i),n.appendChild(r),i.optimize()}optimize(t){super.optimize(t);const e=this.descendants(Z);if(this.setClassName(e),e.length>1){e.shift();for(const t of e)t.remove()}}setCellColspan(t,e){const n=t.statics.blotName,r=t.formats()[n],i=(~~r.colspan||1)+e;i>1?Object.assign(r,{colspan:i}):delete r.colspan,t.replaceWith(n,r)}setCellRowspan(t){for(;t;){const n=t.querySelectorAll("td[rowspan]");if(n.length){for(const t of n){const n=e().find(t),r=n.statics.blotName,i=n.formats()[r],s=(~~i.rowspan||1)-1,o=_(n);s>1?Object.assign(i,{rowspan:s}):delete i.rowspan,o.format(r,i)}break}t=t.previousElementSibling}}setClassName(t){const e=this.statics.defaultClassName,n=t[0],r=this.domNode.getAttribute("class"),i=(t,n)=>{const r=t.domNode.getAttribute("data-class");r!==n&&null!=n&&t.domNode.setAttribute("data-class",(t=>{const n=(t||"").split(/\s+/);return n.find((t=>t===e))||n.unshift(e),n.join(" ").trim()})(n)),n||r||t.domNode.setAttribute("data-class",e)};if(n)i(n,r);else{const t=this.prev;if(!t)return;const[e]=t.descendant(W),[n]=t.descendant(Z);!e&&n&&i(n,r)}}setColumnCells(t,e,n,r,i,s){if(!t)return;const{position:o,width:l}=n;let a=t.children.head;for(;a;){const{left:n,right:c}=a.domNode.getBoundingClientRect(),u=a.domNode.getAttribute("data-row");"object"==typeof r&&Object.assign(r,{rowspan:i,"data-row":u});const h=r||u;if(Math.abs(n-o)<=2){e.push([t,h,a,s]);break}if(Math.abs(c-o)<=2&&!a.next){e.push([t,h,null,s]);break}if(Math.abs(n-o-l)<=2){e.push([t,h,a,s]);break}if(o>n&&o<c){e.push([null,h,a,s]);break}a=a.next}}tbody(){const[t]=this.descendant(G);return t||this.findChild("table-body")}temporary(){const[t]=this.descendant(Z);return t}}function J(){return`cell-${Math.random().toString(36).slice(2,6)}`}function tt(){return`row-${Math.random().toString(36).slice(2,6)}`}Q.blotName="table-container",Q.defaultClassName="ql-table-better",Q.tagName="TABLE",Q.allowedChildren=[G,Z,X],G.requiredContainer=Q,Z.requiredContainer=Q,X.requiredContainer=Q,G.allowedChildren=[K],K.requiredContainer=G,X.allowedChildren=[Y],Y.requiredContainer=X,K.allowedChildren=[W],W.requiredContainer=K,W.allowedChildren=[V,x,m],V.requiredContainer=W,x.requiredContainer=W,m.requiredContainer=W;var et=n(930),nt=n.n(et);const rt=["border","cellspacing","style","class"];function it(t,e,n){return"object"==typeof e?Object.keys(e).reduce(((t,n)=>it(t,n,e[n])),t):t.reduce(((t,r)=>r.attributes&&r.attributes[e]?t.push(r):t.insert(r.insert,nt()({},{[e]:n},r.attributes))),new(s()))}function st(t,e){const n="TABLE"===t.parentNode.tagName?t.parentNode:t.parentNode.parentNode,r=Array.from(n.querySelectorAll("tr")).indexOf(t)+1;return t.innerHTML.replace(/\s/g,"")?it(e,"table-cell",r):new(s())}function ot(t,e){var n;const r="TABLE"===t.parentNode.parentNode.tagName?t.parentNode.parentNode:t.parentNode.parentNode.parentNode,i=Array.from(r.querySelectorAll("tr")),s=t.tagName,o=Array.from(t.parentNode.querySelectorAll(s)),l=t.getAttribute("data-row")||i.indexOf(t.parentNode)+1,a=(null===(n=null==t?void 0:t.firstElementChild)||void 0===n?void 0:n.getAttribute("data-cell"))||o.indexOf(t)+1;return e.length()||e.insert("\n",{"table-cell":{"data-row":l}}),e.ops.forEach((t=>{t.attributes&&t.attributes["table-cell"]&&(t.attributes["table-cell"]=Object.assign(Object.assign({},t.attributes["table-cell"]),{"data-row":l}))})),it(function(t,e,n){const r=W.formats(t);return"TH"===t.tagName?(e.ops.forEach((t=>{"string"!=typeof t.insert||t.insert.endsWith("\n")||(t.insert+="\n")})),it(e,"table-cell",Object.assign(Object.assign({},r),{"data-row":n}))):e}(t,e,l),"table-cell-block",a)}function lt(t,e){let n=~~t.getAttribute("span")||1;const r=t.getAttribute("width"),i=new(s());for(;n>1;)i.insert("\n",{"table-col":{width:r}}),n--;return i.concat(e)}function at(t,e){const n=rt.reduce(((e,n)=>(t.hasAttribute(n)&&("class"===n?e["data-class"]=t.getAttribute(n):e[n]=A(t.getAttribute(n))),e)),{});return(new(s())).insert("\n",{"table-temporary":n}).concat(e)}var ct={col:"Column",insColL:"Insert column left",insColR:"Insert column right",delCol:"Delete column",row:"Row",insRowAbv:"Insert row above",insRowBlw:"Insert row below",delRow:"Delete row",mCells:"Merge cells",sCell:"Split cell",tblProps:"Table properties",cellProps:"Cell properties",insParaOTbl:"Insert paragraph outside the table",insB4:"Insert before",insAft:"Insert after",copyTable:"Copy table",delTable:"Delete table",border:"Border",color:"Color",width:"Width",background:"Background",dims:"Dimensions",height:"Height",padding:"Padding",tblCellTxtAlm:"Table cell text alignment",alCellTxtL:"Align cell text to the left",alCellTxtC:"Align cell text to the center",alCellTxtR:"Align cell text to the right",jusfCellTxt:"Justify cell text",alCellTxtT:"Align cell text to the top",alCellTxtM:"Align cell text to the middle",alCellTxtB:"Align cell text to the bottom",dimsAlm:"Dimensions and alignment",alTblL:"Align table to the left",tblC:"Center table",alTblR:"Align table to the right",save:"Save",cancel:"Cancel",colorMsg:'The color is invalid. Try "#FF0000" or "rgb(255,0,0)" or "red".',dimsMsg:'The value is invalid. Try "10px" or "2em" or simply "2".',colorPicker:"Color picker",removeColor:"Remove color",black:"Black",dimGrey:"Dim grey",grey:"Grey",lightGrey:"Light grey",white:"White",red:"Red",orange:"Orange",yellow:"Yellow",lightGreen:"Light green",green:"Green",aquamarine:"Aquamarine",turquoise:"Turquoise",lightBlue:"Light blue",blue:"Blue",purple:"Purple"},ut={col:"列",insColL:"向左插入列",insColR:"向右插入列",delCol:"删除列",row:"行",insRowAbv:"在上面插入行",insRowBlw:"在下面插入行",delRow:"删除行",mCells:"合并单元格",sCell:"拆分单元格",tblProps:"表格属性",cellProps:"单元格属性",insParaOTbl:"在表格外插入段落",insB4:"在表格前面插入",insAft:"在表格后面插入",copyTable:"复制表格",delTable:"删除表格",border:"边框",color:"颜色",width:"宽度",background:"背景",dims:"尺寸",height:"高度",padding:"内边距",tblCellTxtAlm:"单元格文本对齐方式",alCellTxtL:"左对齐",alCellTxtC:"水平居中对齐",alCellTxtR:"右对齐",jusfCellTxt:"两边对齐",alCellTxtT:"顶端对齐",alCellTxtM:"垂直居中对齐",alCellTxtB:"底部对齐",dimsAlm:"尺寸和对齐方式",alTblL:"表格左对齐",tblC:"表格居中",alTblR:"表格右对齐",save:"保存",cancel:"取消",colorMsg:'无效颜色，请使用 "#FF0000" 或者 "rgb(255,0,0)" 或者 "red"',dimsMsg:'无效值， 请使用 "10px" 或者 "2em" 或者 "2"',colorPicker:"颜色选择器",removeColor:"删除颜色",black:"黑色",dimGrey:"暗灰色",grey:"灰色",lightGrey:"浅灰色",white:"白色",red:"红色",orange:"橙色",yellow:"黄色",lightGreen:"浅绿色",green:"绿色",aquamarine:"海蓝色",turquoise:"青绿色",lightBlue:"浅蓝色",blue:"蓝色",purple:"紫色"},ht={col:"Colonne",insColL:"Insérer colonne à gauche",insColR:"Insérer colonne à droite",delCol:"Supprimer la colonne",row:"Ligne",insRowAbv:"Insérer ligne au-dessus",insRowBlw:"Insérer ligne en dessous",delRow:"Supprimer la ligne",mCells:"Fusionner les cellules",sCell:"Diviser la cellule",tblProps:"Propriétés du tableau",cellProps:"Propriétés de la cellule",insParaOTbl:"Insérer paragraphe en dehors du tableau",insB4:"Insérer avant",insAft:"Insérer après",copyTable:"Copier le tableau",delTable:"Supprimer le tableau",border:"Bordure",color:"Couleur",width:"Largeur",background:"Arrière-plan",dims:"Dimensions",height:"Hauteur",padding:"Marge intérieure",tblCellTxtAlm:"Alignement du texte de la cellule du tableau",alCellTxtL:"Aligner le texte de la cellule à gauche",alCellTxtC:"Aligner le texte de la cellule au centre",alCellTxtR:"Aligner le texte de la cellule à droite",jusfCellTxt:"Justifier le texte de la cellule",alCellTxtT:"Aligner le texte de la cellule en haut",alCellTxtM:"Aligner le texte de la cellule au milieu",alCellTxtB:"Aligner le texte de la cellule en bas",dimsAlm:"Dimensions et alignement",alTblL:"Aligner le tableau à gauche",tblC:"Centrer le tableau",alTblR:"Aligner le tableau à droite",save:"Enregistrer",cancel:"Annuler",colorMsg:'La couleur est invalide. Essayez "#FF0000" ou "rgb(255,0,0)" ou "rouge".',dimsMsg:'La valeur est invalide. Essayez "10px" ou "2em" ou simplement "2".',colorPicker:"Sélecteur de couleur",removeColor:"Supprimer la couleur",black:"Noir",dimGrey:"Gris foncé",grey:"Gris",lightGrey:"Gris clair",white:"Blanc",red:"Rouge",orange:"Orange",yellow:"Jaune",lightGreen:"Vert clair",green:"Vert",aquamarine:"Aigue-marine",turquoise:"Turquoise",lightBlue:"Bleu clair",blue:"Bleu",purple:"Violet"},dt={col:"Kolumna",insColL:"Wstaw kolumnę z lewej",insColR:"Wstaw kolumnę z prawej",delCol:"Usuń kolumnę",row:"Wiersz",insRowAbv:"Wstaw wiersz powyżej",insRowBlw:"Wstaw wiersz poniżej",delRow:"Usuń wiersz",mCells:"Scal komórki",sCell:"Podziel komórkę",tblProps:"Właściwości tabeli",cellProps:"Właściwości komórki",insParaOTbl:"Wstaw akapit poza tabelą",insB4:"Wstaw przed",insAft:"Wstaw po",copyTable:"Kopiuj tabelę",delTable:"Usuń tabelę",border:"Obramowanie",color:"Kolor",width:"Szerokość",background:"Tło",dims:"Wymiary",height:"Wysokość",padding:"Margines wewnętrzny",tblCellTxtAlm:"Wyrównanie tekstu w komórce tabeli",alCellTxtL:"Wyrównaj tekst w komórce do lewej",alCellTxtC:"Wyrównaj tekst w komórce do środka",alCellTxtR:"Wyrównaj tekst w komórce do prawej",jusfCellTxt:"Wyjustuj tekst w komórce",alCellTxtT:"Wyrównaj tekst w komórce do góry",alCellTxtM:"Wyrównaj tekst w komórce do środka",alCellTxtB:"Wyrównaj tekst w komórce do dołu",dimsAlm:"Wymiary i wyrównanie",alTblL:"Wyrównaj tabelę do lewej",tblC:"Wyśrodkuj tabelę",alTblR:"Wyrównaj tabelę do prawej",save:"Zapisz",cancel:"Anuluj",colorMsg:'Kolor jest nieprawidłowy. Spróbuj "#FF0000" lub "rgb(255,0,0)" lub "red".',dimsMsg:'Wartość jest nieprawidłowa. Spróbuj "10px" lub "2em" lub po prostu "2".',colorPicker:"Wybór koloru",removeColor:"Usuń kolor",black:"Czarny",dimGrey:"Przyciemniony szary",grey:"Szary",lightGrey:"Jasnoszary",white:"Biały",red:"Czerwony",orange:"Pomarańczowy",yellow:"Żółty",lightGreen:"Jasnozielony",green:"Zielony",aquamarine:"Akwamaryna",turquoise:"Turkusowy",lightBlue:"Jasnoniebieski",blue:"Niebieski",purple:"Fioletowy"},ft={col:"Spalte",insColL:"Spalte links einfügen",insColR:"Spalte rechts einfügen",delCol:"Spalte löschen",row:"Zeile",insRowAbv:"Zeile oberhalb einfügen",insRowBlw:"Zeile unterhalb einfügen",delRow:"Zeile löschen",mCells:"Zellen verbinden",sCell:"Zelle teilen",tblProps:"Tabelleneingenschaften",cellProps:"Zelleneigenschaften",insParaOTbl:"Absatz außerhalb der Tabelle einfügen",insB4:"Davor einfügen",insAft:"Danach einfügen",copyTable:"Tabelle kopieren",delTable:"Tabelle löschen",border:"Rahmen",color:"Farbe",width:"Breite",background:"Schattierung",dims:"Maße",height:"Höhe",padding:"Abstand",tblCellTxtAlm:"Ausrichtung",alCellTxtL:"Zellentext links ausrichten",alCellTxtC:"Zellentext mittig ausrichten",alCellTxtR:"Zellentext rechts ausrichten",jusfCellTxt:"Zellentext Blocksatz",alCellTxtT:"Zellentext oben ausrichten",alCellTxtM:"Zellentext mittig ausrichten",alCellTxtB:"Zellentext unten ausrichten",dimsAlm:"Maße und Ausrichtung",alTblL:"Tabelle links ausrichten",tblC:"Tabelle mittig ausrichten",alTblR:"Tabelle rechts ausrichten",save:"Speichern",cancel:"Abbrechen",colorMsg:'Die Farbe ist ungültig. Probiere "#FF0000", "rgb(255,0,0)" oder "red".',dimsMsg:'Der Wert ist ungültig. Probiere "10px", "2em" oder einfach "2".',colorPicker:"Farbwähler",removeColor:"Farbe entfernen",black:"Schwarz",dimGrey:"Dunkelgrau",grey:"Grau",lightGrey:"Hellgrau",white:"Weiß",red:"Rot",orange:"Orange",yellow:"Gelb",lightGreen:"Hellgrün",green:"Grün",aquamarine:"Aquamarin",turquoise:"Türkis",lightBlue:"Hellblau",blue:"Blau",purple:"Lila"},pt={col:"Столбец",insColL:"Вставить столбец слева",insColR:"Вставить столбец справа",delCol:"Удалить столбец",row:"Строка",insRowAbv:"Вставить строку сверху",insRowBlw:"Вставить строку снизу",delRow:"Удалить строку",mCells:"Объединить ячейки",sCell:"Разбить ячейку",tblProps:"Свойства таблицы",cellProps:"Свойства ячейки",insParaOTbl:"Вставить абзац за пределами таблицы",insB4:"Вставить абзац перед",insAft:"Вставить абзац после",copyTable:"Копировать таблицу",delTable:"Удалить таблицу",border:"Обводка",color:"Цвет",width:"Ширина",background:"Фон",dims:"Размеры",height:"Высота",padding:"Отступ",tblCellTxtAlm:"Выравнивание текста в ячейке таблицы",alCellTxtL:"Выровнять текст в ячейке по левому краю",alCellTxtC:"Выровнять текст в ячейке по центру",alCellTxtR:"Выровнять текст в ячейке по правому краю",jusfCellTxt:"Выровнять текст в ячейке по ширине",alCellTxtT:"Выровнять текст в ячейке по верху",alCellTxtM:"Выровнять текст в ячейке по середине",alCellTxtB:"Выровнять текст в ячейке по низу",dimsAlm:"Размеры и выравнивание",alTblL:"Выровнять таблицу по левому краю",tblC:"Центрировать таблицу",alTblR:"Выровнять таблицу по правому краю",save:"Сохранить",cancel:"Отменить",colorMsg:'Неверный цвет. Попробуйте "#FF0000", "rgb(255,0,0)" или "red".',dimsMsg:'Недопустимое значение. Попробуйте "10px", "2em" или просто "2".',colorPicker:"Выбор цвета",removeColor:"Удалить цвет",black:"Черный",dimGrey:"Темно-серый",grey:"Серый",lightGrey:"Светло-серый",white:"Белый",red:"Красный",orange:"Оранжевый",yellow:"Желтый",lightGreen:"Светло-зеленый",green:"Зеленый",aquamarine:"Аквамарин",turquoise:"Бирюзовый",lightBlue:"Светло-голубой",blue:"Синий",purple:"Фиолетовый"},gt={col:"Sütun",insColL:"Sola sütun ekle",insColR:"Sağa sütun ekle",delCol:"Sütunu sil",row:"Satır",insRowAbv:"Üstüne satır ekle",insRowBlw:"Altına satır ekle",delRow:"Satırı sil",mCells:"Hücreleri birleştir",sCell:"Hücreyi böl",tblProps:"Tablo özellikleri",cellProps:"Hücre özellikleri",insParaOTbl:"Tablo dışında paragraf ekle",insB4:"Öncesine ekle",insAft:"Sonrasına ekle",copyTable:"Tabloyu kopyala",delTable:"Tabloyu sil",border:"Kenarlık",color:"Renk",width:"Genişlik",background:"Arka plan",dims:"Boyutlar",height:"Yükseklik",padding:"Dolgu",tblCellTxtAlm:"Tablo hücresi metin hizalaması",alCellTxtL:"Hücre metnini sola hizala",alCellTxtC:"Hücre metnini ortaya hizala",alCellTxtR:"Hücre metnini sağa hizala",jusfCellTxt:"Hücre metnini yasla",alCellTxtT:"Hücre metnini üste hizala",alCellTxtM:"Hücre metnini ortaya hizala",alCellTxtB:"Hücre metnini alta hizala",dimsAlm:"Boyutlar ve hizalama",alTblL:"Tabloyu sola hizala",tblC:"Tabloyu ortala",alTblR:"Tabloyu sağa hizala",save:"Kaydet",cancel:"İptal",colorMsg:'Renk geçersiz. "#FF0000", "rgb(255,0,0)" veya "red" deneyin.',dimsMsg:'Değer geçersiz. "10px", "2em" veya sadece "2" deneyin.',colorPicker:"Renk seçici",removeColor:"Rengi kaldır",black:"Siyah",dimGrey:"Koyu gri",grey:"Gri",lightGrey:"Açık gri",white:"Beyaz",red:"Kırmızı",orange:"Turuncu",yellow:"Sarı",lightGreen:"Açık yeşil",green:"Yeşil",aquamarine:"Akuamarin",turquoise:"Turkuaz",lightBlue:"Açık mavi",blue:"Mavi",purple:"Mor"},bt=class{constructor(t){this.config={en_US:ct,zh_CN:ut,fr_FR:ht,pl_PL:dt,de_DE:ft,ru_RU:pt,tr_TR:gt},this.init(t)}changeLanguage(t){this.name=t}init(t){if(void 0===t||"string"==typeof t)this.changeLanguage(t||"en_US");else{const{name:e,content:n}=t;n&&this.registry(e,n),e&&this.changeLanguage(e)}}registry(t,e){this.config=Object.assign(Object.assign({},this.config),{[t]:e})}useLanguage(t){return this.config[this.name][t]}},mt=(t=>(t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY",t))(mt||{});class vt{constructor(t,e,n={}){this.attrName=t,this.keyName=e;const r=mt.TYPE&mt.ATTRIBUTE;this.scope=null!=n.scope?n.scope&mt.LEVEL|r:mt.ATTRIBUTE,null!=n.whitelist&&(this.whitelist=n.whitelist)}static keys(t){return Array.from(t.attributes).map((t=>t.name))}add(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)}canAdd(t,e){return null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1)}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class yt extends Error{constructor(t){super(t="[Parchment] "+t),this.message=t,this.name=this.constructor.name}}const wt=class t{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(null==t)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let n=null;try{n=t.parentNode}catch{return null}return this.find(n,e)}return null}create(e,n,r){const i=this.query(n);if(null==i)throw new yt(`Unable to create ${n} blot`);const s=i,o=n instanceof Node||n.nodeType===Node.TEXT_NODE?n:s.create(r),l=new s(e,o,r);return t.blots.set(l.domNode,l),l}find(e,n=!1){return t.find(e,n)}query(t,e=mt.ANY){let n;return"string"==typeof t?n=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?n=this.types.text:"number"==typeof t?t&mt.LEVEL&mt.BLOCK?n=this.types.block:t&mt.LEVEL&mt.INLINE&&(n=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some((t=>(n=this.classes[t],!!n))),n=n||this.tags[t.tagName]),null==n?null:"scope"in n&&e&mt.LEVEL&n.scope&&e&mt.TYPE&n.scope?n:null}register(...t){return t.map((t=>{const e="blotName"in t,n="attrName"in t;if(!e&&!n)throw new yt("Invalid definition");if(e&&"abstract"===t.blotName)throw new yt("Cannot register abstract class");const r=e?t.blotName:n?t.attrName:void 0;return this.types[r]=t,n?"string"==typeof t.keyName&&(this.attributes[t.keyName]=t):e&&(t.className&&(this.classes[t.className]=t),t.tagName&&(Array.isArray(t.tagName)?t.tagName=t.tagName.map((t=>t.toUpperCase())):t.tagName=t.tagName.toUpperCase(),(Array.isArray(t.tagName)?t.tagName:[t.tagName]).forEach((e=>{(null==this.tags[e]||null==t.className)&&(this.tags[e]=t)})))),t}))}};wt.blots=new WeakMap;let xt=wt;function Nt(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter((t=>0===t.indexOf(`${e}-`)))}const kt=class extends vt{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map((t=>t.split("-").slice(0,-1).join("-")))}add(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0)}remove(t){Nt(t,this.keyName).forEach((e=>{t.classList.remove(e)})),0===t.classList.length&&t.removeAttribute("class")}value(t){const e=(Nt(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}};function At(t){const e=t.split("-"),n=e.slice(1).map((t=>t[0].toUpperCase()+t.slice(1))).join("");return e[0]+n}const _t=class extends vt{static keys(t){return(t.getAttribute("style")||"").split(";").map((t=>t.split(":")[0].trim()))}add(t,e){return!!this.canAdd(t,e)&&(t.style[At(this.keyName)]=e,!0)}remove(t){t.style[At(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){const e=t.style[At(this.keyName)];return this.canAdd(t,e)?e:""}},Et=class{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=xt.find(this.domNode);if(null==t)return;const e=vt.keys(this.domNode),n=kt.keys(this.domNode),r=_t.keys(this.domNode);e.concat(n).concat(r).forEach((e=>{const n=t.scroll.query(e,mt.ATTRIBUTE);n instanceof vt&&(this.attributes[n.attrName]=n)}))}copy(t){Object.keys(this.attributes).forEach((e=>{const n=this.attributes[e].value(this.domNode);t.format(e,n)}))}move(t){this.copy(t),Object.keys(this.attributes).forEach((t=>{this.attributes[t].remove(this.domNode)})),this.attributes={}}values(){return Object.keys(this.attributes).reduce(((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t)),{})}},Ct=class{constructor(t,e){this.scroll=t,this.domNode=e,xt.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(null==this.tagName)throw new yt("Blot definition missing tagName");let e,n;return Array.isArray(this.tagName)?("string"==typeof t?(n=t.toUpperCase(),parseInt(n,10).toString()===n&&(n=parseInt(n,10))):"number"==typeof t&&(n=t),e="number"==typeof n?document.createElement(this.tagName[n-1]):n&&this.tagName.indexOf(n)>-1?document.createElement(n):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){null!=this.parent&&this.parent.removeChild(this),xt.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,n,r){const i=this.isolate(t,e);if(null!=this.scroll.query(n,mt.BLOT)&&r)i.wrap(n,r);else if(null!=this.scroll.query(n,mt.ATTRIBUTE)){const t=this.scroll.create(this.statics.scope);i.wrap(t),t.format(n,r)}}insertAt(t,e,n){const r=null==n?this.scroll.create("text",e):this.scroll.create(e,n),i=this.split(t);this.parent.insertBefore(r,i||void 0)}isolate(t,e){const n=this.split(t);if(null==n)throw new Error("Attempt to isolate at end");return n.split(e),n}length(){return 1}offset(t=this.parent){return null==this.parent||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const n="string"==typeof t?this.scroll.create(t,e):t;return null!=this.parent&&(this.parent.insertBefore(n,this.next||void 0),this.remove()),n}split(t,e){return 0===t?this:this.next}update(t,e){}wrap(t,e){const n="string"==typeof t?this.scroll.create(t,e):t;if(null!=this.parent&&this.parent.insertBefore(n,this.next||void 0),"function"!=typeof n.appendChild)throw new yt(`Cannot wrap ${t}`);return n.appendChild(this),n}};Ct.blotName="abstract";let Tt=Ct;const qt=class extends Tt{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let n=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(n+=1),[this.parent.domNode,n]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};qt.scope=mt.INLINE_BLOT;const Lt=qt;class St{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let n=e();for(;n&&t>0;)t-=1,n=e();return n}contains(t){const e=this.iterator();let n=e();for(;n;){if(n===t)return!0;n=e()}return!1}indexOf(t){const e=this.iterator();let n=e(),r=0;for(;n;){if(n===t)return r;r+=1,n=e()}return-1}insertBefore(t,e){null!=t&&(this.remove(t),t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,n=this.head;for(;null!=n;){if(n===t)return e;e+=n.length(),n=n.next}return-1}remove(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return null!=t&&(t=t.next),e}}find(t,e=!1){const n=this.iterator();let r=n();for(;r;){const i=r.length();if(t<i||e&&t===i&&(null==r.next||0!==r.next.length()))return[r,t];t-=i,r=n()}return[null,0]}forEach(t){const e=this.iterator();let n=e();for(;n;)t(n),n=e()}forEachAt(t,e,n){if(e<=0)return;const[r,i]=this.find(t);let s=t-i;const o=this.iterator(r);let l=o();for(;l&&s<t+e;){const r=l.length();t>s?n(l,t-s,Math.min(e,s+r-t)):n(l,0,Math.min(r,t+e-s)),s+=r,l=o()}}map(t){return this.reduce(((e,n)=>(e.push(t(n)),e)),[])}reduce(t,e){const n=this.iterator();let r=n();for(;r;)e=t(e,r),r=n();return e}}function Ot(t,e){const n=e.find(t);if(n)return n;try{return e.create(t)}catch{const n=e.create(mt.INLINE);return Array.from(t.childNodes).forEach((t=>{n.domNode.appendChild(t)})),t.parentNode&&t.parentNode.replaceChild(n.domNode,t),n.attach(),n}}const jt=class t extends Tt{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach((t=>{t.attach()}))}attachUI(e){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=e,t.uiClass&&this.uiNode.classList.add(t.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new St,Array.from(this.domNode.childNodes).filter((t=>t!==this.uiNode)).reverse().forEach((t=>{try{const e=Ot(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(t){if(t instanceof yt)return;throw t}}))}deleteAt(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,((t,e,n)=>{t.deleteAt(e,n)}))}descendant(e,n=0){const[r,i]=this.children.find(n);return null==e.blotName&&e(r)||null!=e.blotName&&r instanceof e?[r,i]:r instanceof t?r.descendant(e,i):[null,-1]}descendants(e,n=0,r=Number.MAX_VALUE){let i=[],s=r;return this.children.forEachAt(n,r,((n,r,o)=>{(null==e.blotName&&e(n)||null!=e.blotName&&n instanceof e)&&i.push(n),n instanceof t&&(i=i.concat(n.descendants(e,r,s))),s-=o})),i}detach(){this.children.forEach((t=>{t.detach()})),super.detach()}enforceAllowedChildren(){let e=!1;this.children.forEach((n=>{e||this.statics.allowedChildren.some((t=>n instanceof t))||(n.statics.scope===mt.BLOCK_BLOT?(null!=n.next&&this.splitAfter(n),null!=n.prev&&this.splitAfter(n.prev),n.parent.unwrap(),e=!0):n instanceof t?n.unwrap():n.remove())}))}formatAt(t,e,n,r){this.children.forEachAt(t,e,((t,e,i)=>{t.formatAt(e,i,n,r)}))}insertAt(t,e,n){const[r,i]=this.children.find(t);if(r)r.insertAt(i,e,n);else{const t=null==n?this.scroll.create("text",e):this.scroll.create(e,n);this.appendChild(t)}}insertBefore(t,e){null!=t.parent&&t.parent.children.remove(t);let n=null;this.children.insertBefore(t,e||null),t.parent=this,null!=e&&(n=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==n)&&this.domNode.insertBefore(t.domNode,n),t.attach()}length(){return this.children.reduce(((t,e)=>t+e.length()),0)}moveChildren(t,e){this.children.forEach((n=>{t.insertBefore(n,e)}))}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){const t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t)}else this.remove()}path(e,n=!1){const[r,i]=this.children.find(e,n),s=[[this,e]];return r instanceof t?s.concat(r.path(i,n)):(null!=r&&s.push([r,i]),s)}removeChild(t){this.children.remove(t)}replaceWith(e,n){const r="string"==typeof e?this.scroll.create(e,n):e;return r instanceof t&&this.moveChildren(r),super.replaceWith(r)}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}const n=this.clone();return this.parent&&this.parent.insertBefore(n,this.next||void 0),this.children.forEachAt(t,this.length(),((t,r,i)=>{const s=t.split(r,e);null!=s&&n.appendChild(s)})),n}splitAfter(t){const e=this.clone();for(;null!=t.next;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const n=[],r=[];t.forEach((t=>{t.target===this.domNode&&"childList"===t.type&&(n.push(...t.addedNodes),r.push(...t.removedNodes))})),r.forEach((t=>{if(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const e=this.scroll.find(t);null!=e&&(null==e.domNode.parentNode||e.domNode.parentNode===this.domNode)&&e.detach()})),n.filter((t=>t.parentNode===this.domNode&&t!==this.uiNode)).sort(((t,e)=>t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1)).forEach((t=>{let e=null;null!=t.nextSibling&&(e=this.scroll.find(t.nextSibling));const n=Ot(t,this.scroll);(n.next!==e||null==n.next)&&(null!=n.parent&&n.parent.removeChild(this),this.insertBefore(n,e||void 0))})),this.enforceAllowedChildren()}};jt.uiClass="";const Mt=jt,Bt=class t extends Mt{static create(t){return super.create(t)}static formats(e,n){const r=n.query(t.blotName);if(null==r||e.tagName!==r.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Et(this.domNode)}format(e,n){if(e!==this.statics.blotName||n){const t=this.scroll.query(e,mt.INLINE);if(null==t)return;t instanceof vt?this.attributes.attribute(t,n):n&&(e!==this.statics.blotName||this.formats()[e]!==n)&&this.replaceWith(e,n)}else this.children.forEach((e=>{e instanceof t||(e=e.wrap(t.blotName,!0)),this.attributes.copy(e)})),this.unwrap()}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,r){null!=this.formats()[n]||this.scroll.query(n,mt.ATTRIBUTE)?this.isolate(t,e).format(n,r):super.formatAt(t,e,n,r)}optimize(e){super.optimize(e);const n=this.formats();if(0===Object.keys(n).length)return this.unwrap();const r=this.next;r instanceof t&&r.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const n in t)if(t[n]!==e[n])return!1;return!0}(n,r.formats())&&(r.moveChildren(this),r.remove())}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some((t=>t.target===this.domNode&&"attributes"===t.type))&&this.attributes.build()}wrap(e,n){const r=super.wrap(e,n);return r instanceof t&&this.attributes.move(r),r}};Bt.allowedChildren=[Bt,Lt],Bt.blotName="inline",Bt.scope=mt.INLINE_BLOT,Bt.tagName="SPAN";const Rt=Bt,It=class t extends Mt{static create(t){return super.create(t)}static formats(e,n){const r=n.query(t.blotName);if(null==r||e.tagName!==r.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Et(this.domNode)}format(e,n){const r=this.scroll.query(e,mt.BLOCK);null!=r&&(r instanceof vt?this.attributes.attribute(r,n):e!==this.statics.blotName||n?n&&(e!==this.statics.blotName||this.formats()[e]!==n)&&this.replaceWith(e,n):this.replaceWith(t.blotName))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,r){null!=this.scroll.query(n,mt.BLOCK)?this.format(n,r):super.formatAt(t,e,n,r)}insertAt(t,e,n){if(null==n||null!=this.scroll.query(e,mt.INLINE))super.insertAt(t,e,n);else{const r=this.split(t);if(null==r)throw new Error("Attempt to insertAt after block boundaries");{const t=this.scroll.create(e,n);r.parent.insertBefore(t,r)}}}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some((t=>t.target===this.domNode&&"attributes"===t.type))&&this.attributes.build()}};It.blotName="block",It.scope=mt.BLOCK_BLOT,It.tagName="P",It.allowedChildren=[Rt,It,Lt];const Dt=It,Pt=class extends Mt{checkMerge(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,n,r){super.formatAt(t,e,n,r),this.enforceAllowedChildren()}insertAt(t,e,n){super.insertAt(t,e,n),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};Pt.blotName="container",Pt.scope=mt.BLOCK_BLOT;const Ut=Pt,zt=class extends Lt{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,n,r){0===t&&e===this.length()?this.format(n,r):super.formatAt(t,e,n,r)}formats(){return this.statics.formats(this.domNode,this.scroll)}},Ht={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Ft=class extends Mt{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver((t=>{this.update(t)})),this.observer.observe(this.domNode,Ht),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const n=this.registry.find(t,e);return n?n.scroll===this?n:e?this.find(n.scroll.domNode.parentNode,!0):null:null}query(t,e=mt.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){null!=this.scroll&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),0===t&&e===this.length()?this.children.forEach((t=>{t.remove()})):super.deleteAt(t,e)}formatAt(t,e,n,r){this.update(),super.formatAt(t,e,n,r)}insertAt(t,e,n){this.update(),super.insertAt(t,e,n)}optimize(t=[],e={}){super.optimize(e);const n=e.mutationsMap||new WeakMap;let r=Array.from(this.observer.takeRecords());for(;r.length>0;)t.push(r.pop());const i=(t,e=!0)=>{null==t||t===this||null!=t.domNode.parentNode&&(n.has(t.domNode)||n.set(t.domNode,[]),e&&i(t.parent))},s=t=>{n.has(t.domNode)&&(t instanceof Mt&&t.children.forEach(s),n.delete(t.domNode),t.optimize(e))};let o=t;for(let e=0;o.length>0;e+=1){if(e>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(o.forEach((t=>{const e=this.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(i(this.find(t.previousSibling,!1)),Array.from(t.addedNodes).forEach((t=>{const e=this.find(t,!1);i(e,!1),e instanceof Mt&&e.children.forEach((t=>{i(t,!1)}))}))):"attributes"===t.type&&i(e.prev)),i(e))})),this.children.forEach(s),o=Array.from(this.observer.takeRecords()),r=o.slice();r.length>0;)t.push(r.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const n=new WeakMap;t.map((t=>{const e=this.find(t.target,!0);return null==e?null:n.has(e.domNode)?(n.get(e.domNode).push(t),null):(n.set(e.domNode,[t]),e)})).forEach((t=>{null!=t&&t!==this&&n.has(t.domNode)&&t.update(n.get(t.domNode)||[],e)})),e.mutationsMap=n,n.has(this.domNode)&&super.update(n.get(this.domNode),e),this.optimize(t,e)}};Ft.blotName="scroll",Ft.defaultChild=Dt,Ft.allowedChildren=[Dt,Ut],Ft.scope=mt.BLOCK_BLOT,Ft.tagName="DIV";const $t=class t extends Lt{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,n){null==n?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,n)}length(){return this.text.length}optimize(e){super.optimize(e),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof t&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}const n=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(n,this.next||void 0),this.text=this.statics.value(this.domNode),n}update(t,e){t.some((t=>"characterData"===t.type&&t.target===this.domNode))&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};$t.blotName="text",$t.scope=mt.INLINE_BLOT;const Vt=["bold","italic","underline","strike","size","color","background","font","list","header","align","link","image"],Wt=["link","image"];var Kt,Gt,Zt,Yt,Xt,Qt=class{constructor(t,e){this.quill=t,this.selectedTds=[],this.startTd=null,this.endTd=null,this.disabledList=[],this.singleList=[],this.tableBetter=e,this.quill.root.addEventListener("click",this.handleClick.bind(this)),this.initDocumentListener(),this.initWhiteList()}attach(t){let e=Array.from(t.classList).find((t=>0===t.indexOf("ql-")));if(!e)return;const[n,r]=this.getButtonsWhiteList(),i=this.getCorrectDisabled(t,e);e=e.slice(3),n.includes(e)||this.disabledList.push(...i),r.includes(e)&&this.singleList.push(...i)}clearSelected(){for(const t of this.selectedTds)t.classList&&t.classList.remove("ql-cell-focused","ql-cell-selected");this.selectedTds=[],this.startTd=null,this.endTd=null}exitTableFocus(t,n){const r=M(t).table(),i=n?-1:r.length(),s=r.offset(this.quill.scroll)+i;this.tableBetter.hideTools(),this.quill.setSelection(s,0,e().sources.USER)}getButtonsWhiteList(){const{options:t={}}=this.tableBetter,{toolbarButtons:e={}}=t,{whiteList:n=Vt,singleWhiteList:r=Wt}=e;return[n,r]}getCopyColumns(t){const e=t.querySelector("tr");return Array.from(e.querySelectorAll("td")).reduce(((t,e)=>t+(~~e.getAttribute("colspan")||1)),0)}getCopyData(){const t=e().find(this.selectedTds[0]).table();if(t.descendants(W).length===this.selectedTds.length){const e=t.getCopyTable();return{html:e,text:this.getText(e)}}let n="";const r={};for(const t of this.selectedTds){const e=t.getAttribute("data-row");r[e]||(r[e]=[]),r[e].push(t)}for(const t of Object.values(r)){let e="";for(const n of t)e+=O(n.outerHTML);e=`<tr>${e}</tr>`,n+=e}return n=`<table><tbody>${n}</tbody></table>`,{html:n,text:this.getText(n)}}getCorrectDisabled(t,e){if("SELECT"!==t.tagName)return[t];const n=t.closest("span.ql-formats");return n?[...n.querySelectorAll(`span.${e}.ql-picker`),t]:[t]}getCorrectRow(t,n){const r="next"===n?0:-1;let i=(~~t.getAttribute("rowspan")||1)+r||1,s=e().find(t).parent;for(;s&&i;)s=s[n],i--;return null==s?void 0:s.domNode}getCorrectValue(t,n){for(const r of this.selectedTds){const i=e().find(r).html()||r.outerHTML,s=this.quill.clipboard.convert({html:i,text:"\n"});for(const e of s.ops)if(!this.isContinue(e)&&(n=this.getListCorrectValue(t,n,null==e?void 0:e.attributes))!=((null==e?void 0:e.attributes)&&(null==e?void 0:e.attributes[t])||!1))return n}return!n}getListCorrectValue(t,e,n={}){return"list"!==t?e:"check"===e?"checked"!==n[t]&&"unchecked"!==n[t]&&"unchecked":e}getPasteComputeBounds(t,e,n){const r=t.getBoundingClientRect(),i=e.getBoundingClientRect(),s=n.domNode.getBoundingClientRect(),o=this.quill.container.getBoundingClientRect(),l=this.quill.container.scrollLeft,a=this.quill.container.scrollTop;return{left:r.left-o.left-l,right:i.right-o.left-l,top:r.top-o.top-a,bottom:s.bottom-o.top-a}}getPasteInfo(t,e,n){let r=0,i=null,s=null,o=t.parentElement;for(;t;){if(r+=~~t.getAttribute("colspan")||1,r>=e){r=e,i=t;break}t=t.nextElementSibling}for(;--n;){if(!o.nextElementSibling){s=o.firstElementChild;break}o=o.nextElementSibling}return[{clospan:Math.abs(e-r),cloTd:i},{rowspan:n,rowTd:s}]}getPasteLastRow(t,e){for(;--e&&t;)t=t.next;return t}getPasteTds(t){const e={};for(const n of t){const t=n.getAttribute("data-row");e[t]||(e[t]=[]),e[t].push(n)}return Object.values(e)}getText(t){return this.quill.clipboard.convert({html:t}).filter((t=>"string"==typeof t.insert)).map((t=>t.insert)).join("")}handleClick(t){if(t.detail<3||!this.selectedTds.length)return;const{index:n,length:r}=this.quill.getSelection(!0);this.quill.setSelection(n,r-1,e().sources.SILENT),this.quill.scrollSelectionIntoView()}handleDeleteKeyup(t){var e;(null===(e=this.selectedTds)||void 0===e?void 0:e.length)<2||"Backspace"!==t.key&&"Delete"!==t.key||(t.ctrlKey?(this.tableBetter.tableMenus.deleteColumn(!0),this.tableBetter.tableMenus.deleteRow(!0)):this.removeSelectedTdsContent())}handleKeyup(t){switch(t.key){case"ArrowLeft":case"ArrowRight":this.makeTableArrowLevelHandler(t.key);break;case"ArrowUp":case"ArrowDown":this.makeTableArrowVerticalHandler(t.key)}}handleMousedown(t){this.clearSelected();const e=t.target.closest("table");if(!e)return;this.tableBetter.tableMenus.destroyTablePropertiesForm();const n=t.target.closest("td");this.startTd=n,this.endTd=n,this.selectedTds=[n],n.classList.add("ql-cell-focused");const r=t=>{const r=t.target.closest("td");if(!r)return;const i=n.isEqualNode(r);if(i)return;this.clearSelected(),this.startTd=n,this.endTd=r;const s=q(j(n,this.quill.container),j(r,this.quill.container));this.selectedTds=S(s,e,this.quill.container);for(const t of this.selectedTds)t.classList&&t.classList.add("ql-cell-selected");i||this.quill.blur()},i=t=>{this.setSingleDisabled(),this.setCorrectPositionTds(this.startTd,this.endTd,this.selectedTds),this.quill.root.removeEventListener("mousemove",r),this.quill.root.removeEventListener("mouseup",i)};this.quill.root.addEventListener("mousemove",r),this.quill.root.addEventListener("mouseup",i)}initDocumentListener(){document.addEventListener("copy",(t=>this.onCaptureCopy(t,!1))),document.addEventListener("cut",(t=>this.onCaptureCopy(t,!0))),document.addEventListener("keyup",this.handleDeleteKeyup.bind(this)),document.addEventListener("paste",this.onCapturePaste.bind(this))}initWhiteList(){const t=this.quill.getModule("toolbar");Array.from(t.container.querySelectorAll("button, select")).forEach((t=>{this.attach(t)}))}insertColumnCell(t,e){const n=t.tbody();n&&n.children.forEach((n=>{const r=n.children.tail.domNode.getAttribute("data-row");for(let i=0;i<e;i++)t.insertColumnCell(n,r,null)}))}insertRow(t,n,r){const i=e().find(r).rowOffset();for(;n--;)t.insertRow(i+1,1)}insertWith(t){return"string"==typeof t&&t.startsWith("\n")&&t.endsWith("\n")}isContinue(t){return!(!this.insertWith(t.insert)||t.attributes&&!t.attributes["table-list"]&&!t.attributes["table-header"])}lines(t){const e=t=>{let n=[];return t.children.forEach((t=>{t instanceof Ut?n=n.concat(e(t)):function(t){return t instanceof Dt||t instanceof zt}(t)&&n.push(t)})),n};return e(t)}makeTableArrowLevelHandler(t){const e="ArrowLeft"===t?this.startTd:this.endTd,n=this.quill.getSelection();if(!n)return;const[r]=this.quill.getLine(n.index),i=M(r);if(!i)return this.tableBetter.hideTools();!i||e&&e.isEqualNode(i.domNode)||(this.setSelected(i.domNode,!1),this.tableBetter.showTools(!1))}makeTableArrowVerticalHandler(t){const n="ArrowUp"===t,r=this.quill.getSelection();if(!r)return;const[i,s]=this.quill.getLine(r.index),o=n?"prev":"next";if(i[o]&&this.selectedTds.length){const t=i[o].offset(this.quill.scroll)+Math.min(s,i[o].length()-1);this.quill.setSelection(t,0,e().sources.USER)}else{if(!this.selectedTds.length){const t=M(i);if(!t)return;return this.tableArrowSelection(n,t),void this.tableBetter.showTools(!1)}const t=n?this.startTd:this.endTd,r=e().find(t).parent[o],{left:s,right:l}=t.getBoundingClientRect();if(r){let t=null,e=r;for(;e&&!t;){let n=e.children.head;for(;n;){const{left:e,right:r}=n.domNode.getBoundingClientRect();if(Math.abs(e-s)<=2){t=n;break}if(Math.abs(r-l)<=2){t=n;break}n=n.next}e=e[o]}t?this.tableArrowSelection(n,t):this.exitTableFocus(i,n)}else this.exitTableFocus(i,n)}}onCaptureCopy(t,e=!1){var n,r,i;if((null===(n=this.selectedTds)||void 0===n?void 0:n.length)<2)return;if(t.defaultPrevented)return;t.preventDefault();const{html:s,text:o}=this.getCopyData();null===(r=t.clipboardData)||void 0===r||r.setData("text/plain",o),null===(i=t.clipboardData)||void 0===i||i.setData("text/html",s),e&&this.removeSelectedTdsContent()}onCapturePaste(t){var n,r,i;if(!(null===(n=this.selectedTds)||void 0===n?void 0:n.length))return;t.preventDefault();const s=null===(r=t.clipboardData)||void 0===r?void 0:r.getData("text/html"),o=(null===(i=t.clipboardData)||void 0===i||i.getData("text/plain"),document.createElement("div"));o.innerHTML=s;const l=Array.from(o.querySelectorAll("tr"));if(!l.length)return;const a=e().find(this.startTd),c=a.row(),u=a.table();this.quill.history.cutoff();const h=this.getCopyColumns(o),[d,f]=this.getPasteInfo(this.startTd,h,l.length),{clospan:p,cloTd:g}=d,{rowspan:b,rowTd:m}=f;p&&this.insertColumnCell(u,p),b&&this.insertRow(u,b,m);const v=p?c.children.tail.domNode:g,y=this.getPasteLastRow(c,l.length),w=this.getPasteComputeBounds(this.startTd,v,y),x=this.getPasteTds(S(w,u.domNode,this.quill.container)),N=l.reduce(((t,e)=>(t.push(Array.from(e.querySelectorAll("td"))),t)),[]),k=[];for(;N.length;){const t=N.shift(),n=x.shift();let r=null,i=null;for(;t.length;){const s=t.shift(),o=n.shift();if(o)r=o,i=this.pasteSelectedTd(o,s);else{const t=r.getAttribute("data-row"),n=e().find(r);i=u.insertColumnCell(n.parent,t,n.next),i=this.pasteSelectedTd(i.domNode,s),r=i.domNode}i&&k.push(i.domNode)}for(;n.length;)n.shift().remove()}this.quill.blur(),this.setSelectedTds(k),this.tableBetter.tableMenus.updateMenus(),this.quill.scrollSelectionIntoView()}pasteSelectedTd(t,n){const r=t.getAttribute("data-row"),i=W.formats(n);Object.assign(i,{"data-row":r});const o=e().find(t),l=o.replaceWith(o.statics.blotName,i);this.quill.setSelection(l.offset(this.quill.scroll)+l.length()-1,0,e().sources.USER);const a=this.quill.getSelection(!0),c=this.quill.getFormat(a.index),u=n.innerHTML,h=this.getText(u),d=this.quill.clipboard.convert({text:h,html:u}),f=(new(s())).retain(a.index).delete(a.length).concat(it(d,c));return this.quill.updateContents(f,e().sources.USER),l}removeCursor(){const t=this.quill.getSelection(!0);t&&0===t.length&&(this.quill.selection.cursor.remove(),this.quill.blur())}removeSelectedTdContent(t){const n=e().find(t);let r=n.children.head;const i=r.formats()[V.blotName],s=this.quill.scroll.create(V.blotName,i);for(n.insertBefore(s,r);r;)r.remove(),r=r.next}removeSelectedTdsContent(){if(!(this.selectedTds.length<2)){for(const t of this.selectedTds)this.removeSelectedTdContent(t);this.tableBetter.tableMenus.updateMenus()}}setCorrectPositionTds(t,e,n){if(!t||!e||n.length<2)return;const r=n[0],i=n[n.length-1],s=[...new Set([t,e,r,i])];s.sort(((t,e)=>{const n=t.getBoundingClientRect(),r=e.getBoundingClientRect();return(n.top<=r.top||n.bottom<=r.bottom)&&(n.left<=r.left||n.right<=r.right)?-1:1})),this.startTd=s[0],this.endTd=s[s.length-1]}setDisabled(t){for(const e of this.disabledList)t?e.classList.add("ql-table-button-disabled"):e.classList.remove("ql-table-button-disabled");this.setSingleDisabled()}setSelected(t,n=!0){const r=e().find(t);this.clearSelected(),this.startTd=t,this.endTd=t,this.selectedTds=[t],t.classList.add("ql-cell-focused"),n&&this.quill.setSelection(r.offset(this.quill.scroll)+r.length()-1,0,e().sources.USER)}setSelectedTds(t){this.clearSelected(),this.startTd=t[0],this.endTd=t[t.length-1],this.selectedTds=t;for(const t of this.selectedTds)t.classList&&t.classList.add("ql-cell-selected")}setSelectedTdsFormat(t,n){const r=[],i=this.quill.getModule("toolbar");for(const s of this.selectedTds)if(null!=i.handlers[t]){const o=e().find(s),l=this.lines(o),a=i.handlers[t].call(i,n,l);a&&r.push(M(a).domNode)}else{const r=window.getSelection();r.selectAllChildren(s),this.quill.format(t,n,e().sources.USER),r.removeAllRanges()}this.quill.blur(),r.length&&this.setSelectedTds(r)}setSingleDisabled(){for(const t of this.singleList)this.selectedTds.length>1?t.classList.add("ql-table-button-disabled"):t.classList.remove("ql-table-button-disabled")}tableArrowSelection(t,n){const r=t?"tail":"head",i=t?n.children[r].length()-1:0;this.setSelected(n.domNode,!1);const s=n.children[r].offset(this.quill.scroll)+i;this.quill.setSelection(s,0,e().sources.USER)}updateSelected(t){switch(t){case"column":{const t=this.endTd.nextElementSibling||this.startTd.previousElementSibling;if(!t)return;this.setSelected(t)}break;case"row":{const t=this.getCorrectRow(this.endTd,"next")||this.getCorrectRow(this.startTd,"prev");if(!t)return;const e=j(this.startTd,this.quill.container);let n=t.firstElementChild;for(;n;){const t=j(n,this.quill.container);if(t.left+2>=e.left||t.right-2>=e.left)return void this.setSelected(n);n=n.nextElementSibling}this.setSelected(t.firstElementChild)}}}},Jt=class{constructor(t,e){this.quill=t,this.options=null,this.drag=!1,this.line=null,this.dragBlock=null,this.dragTable=null,this.direction=null,this.tableBetter=e,this.quill.root.addEventListener("mousemove",this.handleMouseMove.bind(this))}createDragBlock(){const t=document.createElement("div");t.classList.add("ql-operate-block");const{dragBlockProps:e}=this.getProperty(this.options);P(t,e),this.dragBlock=t,this.quill.container.appendChild(t),this.updateCell(t)}createDragTable(t){const e=document.createElement("div"),n=this.getDragTableProperty(t);e.classList.add("ql-operate-drag-table"),P(e,n),this.dragTable=e,this.quill.container.appendChild(e)}createOperateLine(){const t=document.createElement("div"),e=document.createElement("div");t.classList.add("ql-operate-line-container");const{containerProps:n,lineProps:r}=this.getProperty(this.options);P(t,n),P(e,r),t.appendChild(e),this.quill.container.appendChild(t),this.line=t,this.updateCell(t)}getCorrectCol(t,e){let n=t.children.head;for(;n&&--e;)n=n.next;return n}getDragTableProperty(t){const{left:e,top:n,width:r,height:i}=t.getBoundingClientRect(),s=this.quill.container.getBoundingClientRect();return{left:e-s.left+"px",top:n-s.top+"px",width:`${r}px`,height:`${i}px`,display:"block"}}getLevelColSum(t){let e=t,n=0;for(;e;)n+=~~e.getAttribute("colspan")||1,e=e.previousSibling;return n}getMaxColNum(t){const e=t.parentElement.children;let n=0;for(const t of e)n+=~~t.getAttribute("colspan")||1;return n}getProperty(t){const e=this.quill.container.getBoundingClientRect(),{tableNode:n,cellNode:r,mousePosition:i}=t,{clientX:s,clientY:o}=i,l=n.getBoundingClientRect(),a=r.getBoundingClientRect(),c=a.left+a.width,u=a.top+a.height,h={width:"8px",height:"8px",top:l.bottom-e.top+"px",left:l.right-e.left+"px",display:l.bottom>e.bottom?"none":"block"};return Math.abs(c-s)<=5?(this.direction="level",{dragBlockProps:h,containerProps:{width:"5px",height:`${e.height}px`,top:"0",left:c-e.left-2.5+"px",display:"flex",cursor:"col-resize"},lineProps:{width:"1px",height:"100%"}}):Math.abs(u-o)<=5?(this.direction="vertical",{dragBlockProps:h,containerProps:{width:`${e.width}px`,height:"5px",top:u-e.top-2.5+"px",left:"0",display:"flex",cursor:"row-resize"},lineProps:{width:"100%",height:"1px"}}):(this.hideLine(),{dragBlockProps:h})}getVerticalCells(t,e){let n=t.parentElement;for(;e>1&&n;)n=n.nextSibling,e--;return n.children}handleMouseMove(t){const e=t.target.closest("table"),n=t.target.closest("td"),r={clientX:t.clientX,clientY:t.clientY};if(!e||!n)return void(this.line&&!this.drag&&(this.hideLine(),this.hideDragBlock()));const i={tableNode:e,cellNode:n,mousePosition:r};if(this.line){if(this.drag||!n)return;this.updateProperty(i)}else this.options=i,this.createOperateLine(),this.createDragBlock()}hideDragBlock(){this.dragBlock&&P(this.dragBlock,{display:"none"})}hideDragTable(){this.dragTable&&P(this.dragTable,{display:"none"})}hideLine(){this.line&&P(this.line,{display:"none"})}isLine(t){return t.classList.contains("ql-operate-line-container")}setCellLevelRect(t,n){const{right:r}=t.getBoundingClientRect(),i=~~(n-r),s=this.getLevelColSum(t),o=e().find(t).table(),l=o.colgroup(),a=o.domNode.getBoundingClientRect();if(l){const t=this.getCorrectCol(l,s),e=t.next,n=t.formats()[t.statics.blotName];if(t.domNode.setAttribute("width",`${parseFloat(n.width)+i}`),e){const t=e.formats()[e.statics.blotName];e.domNode.setAttribute("width",""+(parseFloat(t.width)-i))}}else{const e=null==t.nextElementSibling,n=t.parentElement.parentElement.children,r=[];for(const t of n){const n=t.children;if(e){const t=n[n.length-1],{width:e}=t.getBoundingClientRect();r.push([t,""+~~(e+i)]);continue}let o=0;for(const t of n){if(o+=~~t.getAttribute("colspan")||1,o>s)break;if(o===s){const{width:e}=t.getBoundingClientRect(),n=t.nextElementSibling;if(!n)continue;const{width:s}=n.getBoundingClientRect();r.push([t,""+~~(e+i)],[n,""+~~(s-i)])}}}for(const[t,e]of r)D(t,{width:e}),P(t,{width:`${e}px`})}null==t.nextElementSibling&&U(o.domNode,a,i)}setCellRect(t,e,n){"level"===this.direction?this.setCellLevelRect(t,e):"vertical"===this.direction&&this.setCellVerticalRect(t,n)}setCellsRect(t,n,r){const i=t.parentElement.parentElement.children,s=n/this.getMaxColNum(t),o=r/i.length,l=[],a=e().find(t).table(),c=a.colgroup(),u=a.domNode.getBoundingClientRect();for(const t of i){const e=t.children;for(const t of e){const e=~~t.getAttribute("colspan")||1,{width:n,height:r}=t.getBoundingClientRect();l.push([t,`${Math.ceil(n+s*e)}`,`${Math.ceil(r+o)}`])}}if(c){let t=c.children.head;for(const[t,,e]of l)D(t,{height:e}),P(t,{height:`${e}px`});for(;t;){const{width:e}=t.domNode.getBoundingClientRect();D(t.domNode,{width:`${Math.ceil(e+s)}`}),t=t.next}}else for(const[t,e,n]of l)D(t,{width:e,height:n}),P(t,{width:`${e}px`,height:`${n}px`});U(a.domNode,u,n)}setCellVerticalRect(t,e){const n=~~t.getAttribute("rowspan")||1,r=n>1?this.getVerticalCells(t,n):t.parentElement.children;for(const t of r){const{top:n}=t.getBoundingClientRect(),r=""+~~(e-n);D(t,{height:r}),P(t,{height:`${r}px`})}}toggleLineChildClass(t){const e=this.line.firstElementChild;t?e.classList.add("ql-operate-line"):e.classList.remove("ql-operate-line")}updateCell(t){if(!t)return;const e=this.isLine(t),n=t=>{t.preventDefault(),this.drag&&(e?(this.updateDragLine(t.clientX,t.clientY),this.hideDragBlock()):(this.updateDragBlock(t.clientX,t.clientY),this.hideLine()))},r=t=>{t.preventDefault();const{cellNode:i,tableNode:s}=this.options;if(e)this.setCellRect(i,t.clientX,t.clientY),this.toggleLineChildClass(!1);else{const{right:e,bottom:n}=s.getBoundingClientRect(),r=t.clientX-e,o=t.clientY-n;this.setCellsRect(i,r,o),this.dragBlock.classList.remove("ql-operate-block-move"),this.hideDragBlock(),this.hideDragTable()}this.drag=!1,document.removeEventListener("mousemove",n,!1),document.removeEventListener("mouseup",r,!1),this.tableBetter.tableMenus.updateMenus(s)};t.addEventListener("mousedown",(t=>{t.preventDefault();const{tableNode:i}=this.options;if(e)this.toggleLineChildClass(!0);else if(this.dragTable){const t=this.getDragTableProperty(i);P(this.dragTable,t)}else this.createDragTable(i);this.drag=!0,document.addEventListener("mousemove",n),document.addEventListener("mouseup",r)}))}updateDragBlock(t,e){const n=this.quill.container.getBoundingClientRect();this.dragBlock.classList.add("ql-operate-block-move"),P(this.dragBlock,{top:~~(e-n.top-4)+"px",left:~~(t-n.left-4)+"px"}),this.updateDragTable(t,e)}updateDragLine(t,e){const n=this.quill.container.getBoundingClientRect();"level"===this.direction?P(this.line,{left:~~(t-n.left-2.5)+"px"}):"vertical"===this.direction&&P(this.line,{top:~~e-n.top-2.5+"px"})}updateDragTable(t,e){const{top:n,left:r}=this.dragTable.getBoundingClientRect(),i=t-r,s=e-n;P(this.dragTable,{width:`${i}px`,height:`${s}px`,display:"block"})}updateProperty(t){const{containerProps:e,lineProps:n,dragBlockProps:r}=this.getProperty(t);e&&n&&(this.options=t,P(this.line,e),P(this.line.firstChild,n),P(this.dragBlock,r))}},te='<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1692084293475" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2632" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M1012.62222223 944.76190506a78.01904747 78.01904747 0 0 1-78.01904747 78.01904747H76.3936505a78.01904747 78.01904747 0 0 1-78.01904747-78.01904747V86.55238079a78.01904747 78.01904747 0 0 1 78.01904747-78.01904746h858.20952426a78.01904747 78.01904747 0 0 1 78.01904747 78.01904746v858.20952427zM466.4888889 554.66666666H76.3936505v390.0952384h390.0952384V554.66666666z m468.11428586 0H544.50793636v390.0952384h390.0952384V554.66666666zM466.4888889 86.55238079H76.3936505v390.0952384h390.0952384V86.55238079z m468.11428586 0H544.50793636v390.0952384h390.0952384V86.55238079z" fill="#515151" p-id="2633"></path></svg>',ee='<?xml version="1.0" encoding="UTF-8"?><svg width="18" height="18" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M36 18L24 30L12 18" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',ne={},re=[],ie=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|^--/i;function se(t,e){for(var n in e)t[n]=e[n];return t}function oe(t){var e=t.parentNode;e&&e.removeChild(t)}function le(t,e,n){var r,i,s,o,l=arguments;if(e=se({},e),arguments.length>3)for(n=[n],r=3;r<arguments.length;r++)n.push(l[r]);if(null!=n&&(e.children=n),null!=t&&null!=t.defaultProps)for(i in t.defaultProps)void 0===e[i]&&(e[i]=t.defaultProps[i]);return o=e.key,null!=(s=e.ref)&&delete e.ref,null!=o&&delete e.key,ae(t,e,o,s)}function ae(t,e,n,r){var i={type:t,props:e,key:n,ref:r,__k:null,__p:null,__b:0,__e:null,l:null,__c:null,constructor:void 0};return Kt.vnode&&Kt.vnode(i),i}function ce(t){return t.children}function ue(t,e){this.props=t,this.context=e}function he(t,e){if(null==e)return t.__p?he(t.__p,t.__p.__k.indexOf(t)+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?he(t):null}function de(t){var e,n;if(null!=(t=t.__p)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return de(t)}}function fe(t){(!t.__d&&(t.__d=!0)&&1===Gt.push(t)||Yt!==Kt.debounceRendering)&&(Yt=Kt.debounceRendering,(Kt.debounceRendering||Zt)(pe))}function pe(){var t,e,n,r,i,s,o,l;for(Gt.sort((function(t,e){return e.__v.__b-t.__v.__b}));t=Gt.pop();)t.__d&&(n=void 0,r=void 0,s=(i=(e=t).__v).__e,o=e.__P,l=e.u,e.u=!1,o&&(n=[],r=we(o,i,se({},i),e.__n,void 0!==o.ownerSVGElement,null,n,l,null==s?he(i):s),xe(n,i),r!=s&&de(i)))}function ge(t,e,n,r,i,s,o,l,a){var c,u,h,d,f,p,g,b=n&&n.__k||re,m=b.length;if(l==ne&&(l=null!=s?s[0]:m?he(n,0):null),c=0,e.__k=be(e.__k,(function(n){if(null!=n){if(n.__p=e,n.__b=e.__b+1,null===(h=b[c])||h&&n.key==h.key&&n.type===h.type)b[c]=void 0;else for(u=0;u<m;u++){if((h=b[u])&&n.key==h.key&&n.type===h.type){b[u]=void 0;break}h=null}if(d=we(t,n,h=h||ne,r,i,s,o,null,l,a),(u=n.ref)&&h.ref!=u&&(g||(g=[])).push(u,n.__c||d,n),null!=d){if(null==p&&(p=d),null!=n.l)d=n.l,n.l=null;else if(s==h||d!=l||null==d.parentNode){t:if(null==l||l.parentNode!==t)t.appendChild(d);else{for(f=l,u=0;(f=f.nextSibling)&&u<m;u+=2)if(f==d)break t;t.insertBefore(d,l)}"option"==e.type&&(t.value="")}l=d.nextSibling,"function"==typeof e.type&&(e.l=d)}}return c++,n})),e.__e=p,null!=s&&"function"!=typeof e.type)for(c=s.length;c--;)null!=s[c]&&oe(s[c]);for(c=m;c--;)null!=b[c]&&ke(b[c],b[c]);if(g)for(c=0;c<g.length;c++)Ne(g[c],g[++c],g[++c])}function be(t,e,n){if(null==n&&(n=[]),null==t||"boolean"==typeof t)e&&n.push(e(null));else if(Array.isArray(t))for(var r=0;r<t.length;r++)be(t[r],e,n);else n.push(e?e(function(t){if(null==t||"boolean"==typeof t)return null;if("string"==typeof t||"number"==typeof t)return ae(null,t,null,null);if(null!=t.__e||null!=t.__c){var e=ae(t.type,t.props,t.key,null);return e.__e=t.__e,e}return t}(t)):t);return n}function me(t,e,n){"-"===e[0]?t.setProperty(e,n):t[e]="number"==typeof n&&!1===ie.test(e)?n+"px":null==n?"":n}function ve(t,e,n,r,i){var s,o,l,a,c;if("key"===(e=i?"className"===e?"class":e:"class"===e?"className":e)||"children"===e);else if("style"===e)if(s=t.style,"string"==typeof n)s.cssText=n;else{if("string"==typeof r&&(s.cssText="",r=null),r)for(o in r)n&&o in n||me(s,o,"");if(n)for(l in n)r&&n[l]===r[l]||me(s,l,n[l])}else"o"===e[0]&&"n"===e[1]?(a=e!==(e=e.replace(/Capture$/,"")),c=e.toLowerCase(),e=(c in t?c:e).slice(2),n?(r||t.addEventListener(e,ye,a),(t.t||(t.t={}))[e]=n):t.removeEventListener(e,ye,a)):"list"!==e&&"tagName"!==e&&"form"!==e&&!i&&e in t?t[e]=null==n?"":n:"function"!=typeof n&&"dangerouslySetInnerHTML"!==e&&(e!==(e=e.replace(/^xlink:?/,""))?null==n||!1===n?t.removeAttributeNS("http://www.w3.org/1999/xlink",e.toLowerCase()):t.setAttributeNS("http://www.w3.org/1999/xlink",e.toLowerCase(),n):null==n||!1===n?t.removeAttribute(e):t.setAttribute(e,n))}function ye(t){return this.t[t.type](Kt.event?Kt.event(t):t)}function we(t,e,n,r,i,s,o,l,a,c){var u,h,d,f,p,g,b,m,v,y,w=e.type;if(void 0!==e.constructor)return null;(u=Kt.__b)&&u(e);try{t:if("function"==typeof w){if(m=e.props,v=(u=w.contextType)&&r[u.__c],y=u?v?v.props.value:u.__p:r,n.__c?b=(h=e.__c=n.__c).__p=h.__E:("prototype"in w&&w.prototype.render?e.__c=h=new w(m,y):(e.__c=h=new ue(m,y),h.constructor=w,h.render=Ae),v&&v.sub(h),h.props=m,h.state||(h.state={}),h.context=y,h.__n=r,d=h.__d=!0,h.__h=[]),null==h.__s&&(h.__s=h.state),null!=w.getDerivedStateFromProps&&se(h.__s==h.state?h.__s=se({},h.__s):h.__s,w.getDerivedStateFromProps(m,h.__s)),d)null==w.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),null!=h.componentDidMount&&o.push(h);else{if(null==w.getDerivedStateFromProps&&null==l&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(m,y),!l&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(m,h.__s,y)){for(h.props=m,h.state=h.__s,h.__d=!1,h.__v=e,e.__e=null!=a?a!==n.__e?a:n.__e:null,e.__k=n.__k,u=0;u<e.__k.length;u++)e.__k[u]&&(e.__k[u].__p=e);break t}null!=h.componentWillUpdate&&h.componentWillUpdate(m,h.__s,y)}for(f=h.props,p=h.state,h.context=y,h.props=m,h.state=h.__s,(u=Kt.__r)&&u(e),h.__d=!1,h.__v=e,h.__P=t,u=h.render(h.props,h.state,h.context),e.__k=be(null!=u&&u.type==ce&&null==u.key?u.props.children:u),null!=h.getChildContext&&(r=se(se({},r),h.getChildContext())),d||null==h.getSnapshotBeforeUpdate||(g=h.getSnapshotBeforeUpdate(f,p)),ge(t,e,n,r,i,s,o,a,c),h.base=e.__e;u=h.__h.pop();)h.__s&&(h.state=h.__s),u.call(h);d||null==f||null==h.componentDidUpdate||h.componentDidUpdate(f,p,g),b&&(h.__E=h.__p=null)}else e.__e=function(t,e,n,r,i,s,o,l){var a,c,u,h,d=n.props,f=e.props;if(i="svg"===e.type||i,null==t&&null!=s)for(a=0;a<s.length;a++)if(null!=(c=s[a])&&(null===e.type?3===c.nodeType:c.localName===e.type)){t=c,s[a]=null;break}if(null==t){if(null===e.type)return document.createTextNode(f);t=i?document.createElementNS("http://www.w3.org/2000/svg",e.type):document.createElement(e.type),s=null}return null===e.type?d!==f&&(null!=s&&(s[s.indexOf(t)]=null),t.data=f):e!==n&&(null!=s&&(s=re.slice.call(t.childNodes)),u=(d=n.props||ne).dangerouslySetInnerHTML,h=f.dangerouslySetInnerHTML,l||(h||u)&&(h&&u&&h.__html==u.__html||(t.innerHTML=h&&h.__html||"")),function(t,e,n,r,i){var s;for(s in n)s in e||ve(t,s,null,n[s],r);for(s in e)i&&"function"!=typeof e[s]||"value"===s||"checked"===s||n[s]===e[s]||ve(t,s,e[s],n[s],r)}(t,f,d,i,l),e.__k=e.props.children,h||ge(t,e,n,r,"foreignObject"!==e.type&&i,s,o,ne,l),l||("value"in f&&void 0!==f.value&&f.value!==t.value&&(t.value=null==f.value?"":f.value),"checked"in f&&void 0!==f.checked&&f.checked!==t.checked&&(t.checked=f.checked))),t}(n.__e,e,n,r,i,s,o,c);(u=Kt.diffed)&&u(e)}catch(t){Kt.__e(t,e,n)}return e.__e}function xe(t,e){for(var n;n=t.pop();)try{n.componentDidMount()}catch(t){Kt.__e(t,n.__v)}Kt.__c&&Kt.__c(e)}function Ne(t,e,n){try{"function"==typeof t?t(e):t.current=e}catch(t){Kt.__e(t,n)}}function ke(t,e,n){var r,i,s;if(Kt.unmount&&Kt.unmount(t),(r=t.ref)&&Ne(r,null,e),n||"function"==typeof t.type||(n=null!=(i=t.__e)),t.__e=t.l=null,null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){Kt.__e(t,e)}r.base=r.__P=null}if(r=t.__k)for(s=0;s<r.length;s++)r[s]&&ke(r[s],e,n);null!=i&&oe(i)}function Ae(t,e,n){return this.constructor(t,n)}function _e(){return _e=Object.assign||function(t){for(var e=arguments,n=1;n<arguments.length;n++){var r=e[n];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},_e.apply(this,arguments)}Kt={},ue.prototype.setState=function(t,e){var n=this.__s!==this.state&&this.__s||(this.__s=se({},this.state));("function"!=typeof t||(t=t(n,this.props)))&&se(n,t),null!=t&&this.__v&&(this.u=!1,e&&this.__h.push(e),fe(this))},ue.prototype.forceUpdate=function(t){this.__v&&(t&&this.__h.push(t),this.u=!0,fe(this))},ue.prototype.render=ce,Gt=[],Zt="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Yt=Kt.debounceRendering,Kt.__e=function(t,e,n){for(var r;e=e.__p;)if((r=e.__c)&&!r.__p)try{if(r.constructor&&null!=r.constructor.getDerivedStateFromError)r.setState(r.constructor.getDerivedStateFromError(t));else{if(null==r.componentDidCatch)continue;r.componentDidCatch(t)}return fe(r.__E=r)}catch(e){t=e}throw t},Xt=ne;var Ee="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)",Ce="[\\s|\\(]+("+Ee+")[,|\\s]+("+Ee+")[,|\\s]+("+Ee+")\\s*\\)?",Te="[\\s|\\(]+("+Ee+")[,|\\s]+("+Ee+")[,|\\s]+("+Ee+")[,|\\s]+("+Ee+")\\s*\\)?",qe=new RegExp("rgb"+Ce),Le=new RegExp("rgba"+Te),Se=new RegExp("hsl"+Ce),Oe=new RegExp("hsla"+Te),je="^(?:#?|0x?)",Me="([0-9a-fA-F]{1})",Be="([0-9a-fA-F]{2})",Re=new RegExp(je+Me+Me+Me+"$"),Ie=new RegExp(je+Me+Me+Me+Me+"$"),De=new RegExp(je+Be+Be+Be+"$"),Pe=new RegExp(je+Be+Be+Be+Be+"$"),Ue=Math.log,ze=Math.round,He=Math.floor;function Fe(t,e,n){return Math.min(Math.max(t,e),n)}function $e(t,e){var n=t.indexOf("%")>-1,r=parseFloat(t);return n?e/100*r:r}function Ve(t){return parseInt(t,16)}function We(t){return t.toString(16).padStart(2,"0")}var Ke=function(){function t(t,e){this.$={h:0,s:0,v:0,a:1},t&&this.set(t),this.onChange=e,this.initialValue=_e({},this.$)}var e,n=t.prototype;return n.set=function(e){if("string"==typeof e)/^(?:#?|0x?)[0-9a-fA-F]{3,8}$/.test(e)?this.hexString=e:/^rgba?/.test(e)?this.rgbString=e:/^hsla?/.test(e)&&(this.hslString=e);else{if("object"!=typeof e)throw new Error("Invalid color value");e instanceof t?this.hsva=e.hsva:"r"in e&&"g"in e&&"b"in e?this.rgb=e:"h"in e&&"s"in e&&"v"in e?this.hsv=e:"h"in e&&"s"in e&&"l"in e?this.hsl=e:"kelvin"in e&&(this.kelvin=e.kelvin)}},n.setChannel=function(t,e,n){var r;this[t]=_e({},this[t],((r={})[e]=n,r))},n.reset=function(){this.hsva=this.initialValue},n.clone=function(){return new t(this)},n.unbind=function(){this.onChange=void 0},t.hsvToRgb=function(t){var e=t.h/60,n=t.s/100,r=t.v/100,i=He(e),s=e-i,o=r*(1-n),l=r*(1-s*n),a=r*(1-(1-s)*n),c=i%6,u=[a,r,r,l,o,o][c],h=[o,o,a,r,r,l][c];return{r:Fe(255*[r,l,o,o,a,r][c],0,255),g:Fe(255*u,0,255),b:Fe(255*h,0,255)}},t.rgbToHsv=function(t){var e=t.r/255,n=t.g/255,r=t.b/255,i=Math.max(e,n,r),s=Math.min(e,n,r),o=i-s,l=0,a=i,c=0===i?0:o/i;switch(i){case s:l=0;break;case e:l=(n-r)/o+(n<r?6:0);break;case n:l=(r-e)/o+2;break;case r:l=(e-n)/o+4}return{h:60*l%360,s:Fe(100*c,0,100),v:Fe(100*a,0,100)}},t.hsvToHsl=function(t){var e=t.s/100,n=t.v/100,r=(2-e)*n,i=r<=1?r:2-r,s=i<1e-9?0:e*n/i;return{h:t.h,s:Fe(100*s,0,100),l:Fe(50*r,0,100)}},t.hslToHsv=function(t){var e=2*t.l,n=t.s*(e<=100?e:200-e)/100,r=e+n<1e-9?0:2*n/(e+n);return{h:t.h,s:Fe(100*r,0,100),v:Fe((e+n)/2,0,100)}},t.kelvinToRgb=function(t){var e,n,r,i=t/100;return i<66?(e=255,n=-155.25485562709179-.44596950469579133*(n=i-2)+104.49216199393888*Ue(n),r=i<20?0:.8274096064007395*(r=i-10)-254.76935184120902+115.67994401066147*Ue(r)):(e=351.97690566805693+.114206453784165*(e=i-55)-40.25366309332127*Ue(e),n=325.4494125711974+.07943456536662342*(n=i-50)-28.0852963507957*Ue(n),r=255),{r:Fe(He(e),0,255),g:Fe(He(n),0,255),b:Fe(He(r),0,255)}},t.rgbToKelvin=function(e){for(var n,r=e.r,i=e.b,s=2e3,o=4e4;o-s>.4;){n=.5*(o+s);var l=t.kelvinToRgb(n);l.b/l.r>=i/r?o=n:s=n}return n},e=[{key:"hsv",get:function(){var t=this.$;return{h:t.h,s:t.s,v:t.v}},set:function(t){var e=this.$;if(t=_e({},e,t),this.onChange){var n={h:!1,v:!1,s:!1,a:!1};for(var r in e)n[r]=t[r]!=e[r];this.$=t,(n.h||n.s||n.v||n.a)&&this.onChange(this,n)}else this.$=t}},{key:"hsva",get:function(){return _e({},this.$)},set:function(t){this.hsv=t}},{key:"hue",get:function(){return this.$.h},set:function(t){this.hsv={h:t}}},{key:"saturation",get:function(){return this.$.s},set:function(t){this.hsv={s:t}}},{key:"value",get:function(){return this.$.v},set:function(t){this.hsv={v:t}}},{key:"alpha",get:function(){return this.$.a},set:function(t){this.hsv=_e({},this.hsv,{a:t})}},{key:"kelvin",get:function(){return t.rgbToKelvin(this.rgb)},set:function(e){this.rgb=t.kelvinToRgb(e)}},{key:"red",get:function(){return this.rgb.r},set:function(t){this.rgb=_e({},this.rgb,{r:t})}},{key:"green",get:function(){return this.rgb.g},set:function(t){this.rgb=_e({},this.rgb,{g:t})}},{key:"blue",get:function(){return this.rgb.b},set:function(t){this.rgb=_e({},this.rgb,{b:t})}},{key:"rgb",get:function(){var e=t.hsvToRgb(this.$),n=e.r,r=e.g,i=e.b;return{r:ze(n),g:ze(r),b:ze(i)}},set:function(e){this.hsv=_e({},t.rgbToHsv(e),{a:void 0===e.a?1:e.a})}},{key:"rgba",get:function(){return _e({},this.rgb,{a:this.alpha})},set:function(t){this.rgb=t}},{key:"hsl",get:function(){var e=t.hsvToHsl(this.$),n=e.h,r=e.s,i=e.l;return{h:ze(n),s:ze(r),l:ze(i)}},set:function(e){this.hsv=_e({},t.hslToHsv(e),{a:void 0===e.a?1:e.a})}},{key:"hsla",get:function(){return _e({},this.hsl,{a:this.alpha})},set:function(t){this.hsl=t}},{key:"rgbString",get:function(){var t=this.rgb;return"rgb("+t.r+", "+t.g+", "+t.b+")"},set:function(t){var e,n,r,i,s=1;if((e=qe.exec(t))?(n=$e(e[1],255),r=$e(e[2],255),i=$e(e[3],255)):(e=Le.exec(t))&&(n=$e(e[1],255),r=$e(e[2],255),i=$e(e[3],255),s=$e(e[4],1)),!e)throw new Error("Invalid rgb string");this.rgb={r:n,g:r,b:i,a:s}}},{key:"rgbaString",get:function(){var t=this.rgba;return"rgba("+t.r+", "+t.g+", "+t.b+", "+t.a+")"},set:function(t){this.rgbString=t}},{key:"hexString",get:function(){var t=this.rgb;return"#"+We(t.r)+We(t.g)+We(t.b)},set:function(t){var e,n,r,i,s=255;if((e=Re.exec(t))?(n=17*Ve(e[1]),r=17*Ve(e[2]),i=17*Ve(e[3])):(e=Ie.exec(t))?(n=17*Ve(e[1]),r=17*Ve(e[2]),i=17*Ve(e[3]),s=17*Ve(e[4])):(e=De.exec(t))?(n=Ve(e[1]),r=Ve(e[2]),i=Ve(e[3])):(e=Pe.exec(t))&&(n=Ve(e[1]),r=Ve(e[2]),i=Ve(e[3]),s=Ve(e[4])),!e)throw new Error("Invalid hex string");this.rgb={r:n,g:r,b:i,a:s/255}}},{key:"hex8String",get:function(){var t=this.rgba;return"#"+We(t.r)+We(t.g)+We(t.b)+We(He(255*t.a))},set:function(t){this.hexString=t}},{key:"hslString",get:function(){var t=this.hsl;return"hsl("+t.h+", "+t.s+"%, "+t.l+"%)"},set:function(t){var e,n,r,i,s=1;if((e=Se.exec(t))?(n=$e(e[1],360),r=$e(e[2],100),i=$e(e[3],100)):(e=Oe.exec(t))&&(n=$e(e[1],360),r=$e(e[2],100),i=$e(e[3],100),s=$e(e[4],1)),!e)throw new Error("Invalid hsl string");this.hsl={h:n,s:r,l:i,a:s}}},{key:"hslaString",get:function(){var t=this.hsla;return"hsla("+t.h+", "+t.s+"%, "+t.l+"%, "+t.a+")"},set:function(t){this.hslString=t}}],e&&function(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}(t.prototype,e),t}();function Ge(t){var e,n=t.width,r=t.sliderSize,i=t.borderWidth,s=t.handleRadius,o=t.padding,l=t.sliderShape,a="horizontal"===t.layoutDirection;return r=null!=(e=r)?e:2*o+2*s,"circle"===l?{handleStart:t.padding+t.handleRadius,handleRange:n-2*o-2*s,width:n,height:n,cx:n/2,cy:n/2,radius:n/2-i/2}:{handleStart:r/2,handleRange:n-r,radius:r/2,x:0,y:0,width:a?r:n,height:a?n:r}}var Ze,Ye=2*Math.PI,Xe=function(t,e){return(t%e+e)%e},Qe=function(t,e){return Math.sqrt(t*t+e*e)};function Je(t){return t.width/2-t.padding-t.handleRadius-t.borderWidth}function tn(t){var e=t.width/2;return{width:t.width,radius:e-t.borderWidth,cx:e,cy:e}}function en(t,e,n){var r=t.wheelAngle,i=t.wheelDirection;return n&&"clockwise"===i?e=r+e:"clockwise"===i?e=360-r+e:n&&"anticlockwise"===i?e=r+180-e:"anticlockwise"===i&&(e=r-e),Xe(e,360)}function nn(t,e,n){var r=tn(t),i=r.cx,s=r.cy,o=Je(t);e=i-e,n=s-n;var l=en(t,Math.atan2(-n,-e)*(360/Ye)),a=Math.min(Qe(e,n),o);return{h:Math.round(l),s:Math.round(100/o*a)}}function rn(t){var e=t.width,n=t.boxHeight;return{width:e,height:null!=n?n:e,radius:t.padding+t.handleRadius}}function sn(t,e,n){var r=rn(t),i=r.width,s=r.height,o=r.radius,l=(e-o)/(i-2*o)*100,a=(n-o)/(s-2*o)*100;return{s:Math.max(0,Math.min(l,100)),v:Math.max(0,Math.min(100-a,100))}}function on(t){Ze||(Ze=document.getElementsByTagName("base"));var e=window.navigator.userAgent,n=/^((?!chrome|android).)*safari/i.test(e),r=/iPhone|iPod|iPad/i.test(e),i=window.location;return(n||r)&&Ze.length>0?i.protocol+"//"+i.host+i.pathname+i.search+t:t}function ln(t,e,n,r){for(var i=0;i<r.length;i++){var s=r[i].x-e,o=r[i].y-n;if(Math.sqrt(s*s+o*o)<t.handleRadius)return i}return null}function an(t){return{boxSizing:"border-box",border:t.borderWidth+"px solid "+t.borderColor}}function cn(t,e,n){return t+"-gradient("+e+", "+n.map((function(t){var e=t[0];return t[1]+" "+e+"%"})).join(",")+")"}function un(t){return"string"==typeof t?t:t+"px"}var hn=["mousemove","touchmove","mouseup","touchend"],dn=function(t){function e(e){t.call(this,e),this.uid=(Math.random()+1).toString(36).substring(5)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.render=function(t){var e=this.handleEvent.bind(this),n={onMouseDown:e,ontouchstart:e},r="horizontal"===t.layoutDirection,i=null===t.margin?t.sliderMargin:t.margin,s={overflow:"visible",display:r?"inline-block":"block"};return t.index>0&&(s[r?"marginLeft":"marginTop"]=i),le(ce,null,t.children(this.uid,n,s))},e.prototype.handleEvent=function(t){var e=this,n=this.props.onInput,r=this.base.getBoundingClientRect();t.preventDefault();var i=t.touches?t.changedTouches[0]:t,s=i.clientX-r.left,o=i.clientY-r.top;switch(t.type){case"mousedown":case"touchstart":!1!==n(s,o,0)&&hn.forEach((function(t){document.addEventListener(t,e,{passive:!1})}));break;case"mousemove":case"touchmove":n(s,o,1);break;case"mouseup":case"touchend":n(s,o,2),hn.forEach((function(t){document.removeEventListener(t,e,{passive:!1})}))}},e}(ue);function fn(t){var e=t.r,n=t.url,r=e,i=e;return le("svg",{className:"IroHandle IroHandle--"+t.index+" "+(t.isActive?"IroHandle--isActive":""),style:{"-webkit-tap-highlight-color":"rgba(0, 0, 0, 0);",transform:"translate("+un(t.x)+", "+un(t.y)+")",willChange:"transform",top:un(-e),left:un(-e),width:un(2*e),height:un(2*e),position:"absolute",overflow:"visible"}},n&&le("use",Object.assign({xlinkHref:on(n)},t.props)),!n&&le("circle",{cx:r,cy:i,r:e,fill:"none","stroke-width":2,stroke:"#000"}),!n&&le("circle",{cx:r,cy:i,r:e-2,fill:t.fill,"stroke-width":2,stroke:"#fff"}))}function pn(t){var e=t.activeIndex,n=void 0!==e&&e<t.colors.length?t.colors[e]:t.color,r=Ge(t),i=r.width,s=r.height,o=r.radius,l=function(t,e){var n=Ge(t),r=n.width,i=n.height,s=n.handleRange,o=n.handleStart,l="horizontal"===t.layoutDirection,a=function(t,e){var n=e.hsva,r=e.rgb;switch(t.sliderType){case"red":return r.r/2.55;case"green":return r.g/2.55;case"blue":return r.b/2.55;case"alpha":return 100*n.a;case"kelvin":var i=t.minTemperature,s=t.maxTemperature-i,o=(e.kelvin-i)/s*100;return Math.max(0,Math.min(o,100));case"hue":return n.h/=3.6;case"saturation":return n.s;default:return n.v}}(t,e),c=l?r/2:i/2,u=o+a/100*s;return l&&(u=-1*u+s+2*o),{x:l?c:u,y:l?u:c}}(t,n),a=function(t,e){var n=e.hsv,r=e.rgb;switch(t.sliderType){case"red":return[[0,"rgb(0,"+r.g+","+r.b+")"],[100,"rgb(255,"+r.g+","+r.b+")"]];case"green":return[[0,"rgb("+r.r+",0,"+r.b+")"],[100,"rgb("+r.r+",255,"+r.b+")"]];case"blue":return[[0,"rgb("+r.r+","+r.g+",0)"],[100,"rgb("+r.r+","+r.g+",255)"]];case"alpha":return[[0,"rgba("+r.r+","+r.g+","+r.b+",0)"],[100,"rgb("+r.r+","+r.g+","+r.b+")"]];case"kelvin":for(var i=[],s=t.minTemperature,o=t.maxTemperature,l=o-s,a=s,c=0;a<o;a+=l/8,c+=1){var u=Ke.kelvinToRgb(a),h=u.r,d=u.g,f=u.b;i.push([12.5*c,"rgb("+h+","+d+","+f+")"])}return i;case"hue":return[[0,"#f00"],[16.666,"#ff0"],[33.333,"#0f0"],[50,"#0ff"],[66.666,"#00f"],[83.333,"#f0f"],[100,"#f00"]];case"saturation":var p=Ke.hsvToHsl({h:n.h,s:0,v:n.v}),g=Ke.hsvToHsl({h:n.h,s:100,v:n.v});return[[0,"hsl("+p.h+","+p.s+"%,"+p.l+"%)"],[100,"hsl("+g.h+","+g.s+"%,"+g.l+"%)"]];default:var b=Ke.hsvToHsl({h:n.h,s:n.s,v:100});return[[0,"#000"],[100,"hsl("+b.h+","+b.s+"%,"+b.l+"%)"]]}}(t,n);return le(dn,Object.assign({},t,{onInput:function(e,r,i){var s=function(t,e,n){var r,i=Ge(t),s=i.handleRange,o=i.handleStart;r="horizontal"===t.layoutDirection?-1*n+s+o:e-o,r=Math.max(Math.min(r,s),0);var l=Math.round(100/s*r);switch(t.sliderType){case"kelvin":var a=t.minTemperature;return a+(t.maxTemperature-a)*(l/100);case"alpha":return l/100;case"hue":return 3.6*l;case"red":case"blue":case"green":return 2.55*l;default:return l}}(t,e,r);t.parent.inputActive=!0,n[t.sliderType]=s,t.onInput(i,t.id)}}),(function(e,r,c){return le("div",Object.assign({},r,{className:"IroSlider",style:Object.assign({},{position:"relative",width:un(i),height:un(s),borderRadius:un(o),background:"conic-gradient(#ccc 25%, #fff 0 50%, #ccc 0 75%, #fff 0)",backgroundSize:"8px 8px"},c)}),le("div",{className:"IroSliderGradient",style:Object.assign({},{position:"absolute",top:0,left:0,width:"100%",height:"100%",borderRadius:un(o),background:cn("linear","horizontal"===t.layoutDirection?"to top":"to right",a)},an(t))}),le(fn,{isActive:!0,index:n.index,r:t.handleRadius,url:t.handleSvg,props:t.handleProps,x:l.x,y:l.y}))}))}function gn(t){var e=rn(t),n=e.width,r=e.height,i=e.radius,s=t.colors,o=t.parent,l=t.activeIndex,a=void 0!==l&&l<t.colors.length?t.colors[l]:t.color,c=[[[0,"#fff"],[100,"hsl("+a.hue+",100%,50%)"]],[[0,"rgba(0,0,0,0)"],[100,"#000"]]],u=s.map((function(e){return function(t,e){var n=rn(t),r=n.width,i=n.height,s=n.radius,o=e.hsv,l=s,a=r-2*s,c=i-2*s;return{x:l+o.s/100*a,y:l+(c-o.v/100*c)}}(t,e)}));return le(dn,Object.assign({},t,{onInput:function(e,n,r){if(0===r){var i=ln(t,e,n,u);null!==i?o.setActiveColor(i):(o.inputActive=!0,a.hsv=sn(t,e,n),t.onInput(r,t.id))}else 1===r&&(o.inputActive=!0,a.hsv=sn(t,e,n));t.onInput(r,t.id)}}),(function(e,o,l){return le("div",Object.assign({},o,{className:"IroBox",style:Object.assign({},{width:un(n),height:un(r),position:"relative"},l)}),le("div",{className:"IroBox",style:Object.assign({},{width:"100%",height:"100%",borderRadius:un(i)},an(t),{background:cn("linear","to bottom",c[1])+","+cn("linear","to right",c[0])})}),s.filter((function(t){return t!==a})).map((function(e){return le(fn,{isActive:!1,index:e.index,fill:e.hslString,r:t.handleRadius,url:t.handleSvg,props:t.handleProps,x:u[e.index].x,y:u[e.index].y})})),le(fn,{isActive:!0,index:a.index,fill:a.hslString,r:t.activeHandleRadius||t.handleRadius,url:t.handleSvg,props:t.handleProps,x:u[a.index].x,y:u[a.index].y}))}))}function bn(t){var e=tn(t).width,n=t.colors,r=(t.borderWidth,t.parent),i=t.color,s=i.hsv,o=n.map((function(e){return function(t,e){var n=e.hsv,r=tn(t),i=r.cx,s=r.cy,o=Je(t),l=(180+en(t,n.h,!0))*(Ye/360),a=n.s/100*o,c="clockwise"===t.wheelDirection?-1:1;return{x:i+a*Math.cos(l)*c,y:s+a*Math.sin(l)*c}}(t,e)})),l={position:"absolute",top:0,left:0,width:"100%",height:"100%",borderRadius:"50%",boxSizing:"border-box"};return le(dn,Object.assign({},t,{onInput:function(e,n,s){if(0===s){if(!function(t,e,n){var r=tn(t),i=r.cx,s=r.cy,o=t.width/2;return Qe(i-e,s-n)<o}(t,e,n))return!1;var l=ln(t,e,n,o);null!==l?r.setActiveColor(l):(r.inputActive=!0,i.hsv=nn(t,e,n),t.onInput(s,t.id))}else 1===s&&(r.inputActive=!0,i.hsv=nn(t,e,n));t.onInput(s,t.id)}}),(function(r,a,c){return le("div",Object.assign({},a,{className:"IroWheel",style:Object.assign({},{width:un(e),height:un(e),position:"relative"},c)}),le("div",{className:"IroWheelHue",style:Object.assign({},l,{transform:"rotateZ("+(t.wheelAngle+90)+"deg)",background:"clockwise"===t.wheelDirection?"conic-gradient(red, yellow, lime, aqua, blue, magenta, red)":"conic-gradient(red, magenta, blue, aqua, lime, yellow, red)"})}),le("div",{className:"IroWheelSaturation",style:Object.assign({},l,{background:"radial-gradient(circle closest-side, #fff, transparent)"})}),t.wheelLightness&&le("div",{className:"IroWheelLightness",style:Object.assign({},l,{background:"#000",opacity:1-s.v/100})}),le("div",{className:"IroWheelBorder",style:Object.assign({},l,an(t))}),n.filter((function(t){return t!==i})).map((function(e){return le(fn,{isActive:!1,index:e.index,fill:e.hslString,r:t.handleRadius,url:t.handleSvg,props:t.handleProps,x:o[e.index].x,y:o[e.index].y})})),le(fn,{isActive:!0,index:i.index,fill:i.hslString,r:t.activeHandleRadius||t.handleRadius,url:t.handleSvg,props:t.handleProps,x:o[i.index].x,y:o[i.index].y}))}))}fn.defaultProps={fill:"none",x:0,y:0,r:8,url:null,props:{x:0,y:0}},pn.defaultProps=Object.assign({},{sliderShape:"bar",sliderType:"value",minTemperature:2200,maxTemperature:11e3});var mn=function(t){function e(e){var n=this;t.call(this,e),this.colors=[],this.inputActive=!1,this.events={},this.activeEvents={},this.deferredEvents={},this.id=e.id,(e.colors.length>0?e.colors:[e.color]).forEach((function(t){return n.addColor(t)})),this.setActiveColor(0),this.state=Object.assign({},e,{color:this.color,colors:this.colors,layout:e.layout})}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.addColor=function(t,e){void 0===e&&(e=this.colors.length);var n=new Ke(t,this.onColorChange.bind(this));this.colors.splice(e,0,n),this.colors.forEach((function(t,e){return t.index=e})),this.state&&this.setState({colors:this.colors}),this.deferredEmit("color:init",n)},e.prototype.removeColor=function(t){var e=this.colors.splice(t,1)[0];e.unbind(),this.colors.forEach((function(t,e){return t.index=e})),this.state&&this.setState({colors:this.colors}),e.index===this.color.index&&this.setActiveColor(0),this.emit("color:remove",e)},e.prototype.setActiveColor=function(t){this.color=this.colors[t],this.state&&this.setState({color:this.color}),this.emit("color:setActive",this.color)},e.prototype.setColors=function(t,e){var n=this;void 0===e&&(e=0),this.colors.forEach((function(t){return t.unbind()})),this.colors=[],t.forEach((function(t){return n.addColor(t)})),this.setActiveColor(e),this.emit("color:setAll",this.colors)},e.prototype.on=function(t,e){var n=this,r=this.events;(Array.isArray(t)?t:[t]).forEach((function(t){(r[t]||(r[t]=[])).push(e),n.deferredEvents[t]&&(n.deferredEvents[t].forEach((function(t){e.apply(null,t)})),n.deferredEvents[t]=[])}))},e.prototype.off=function(t,e){var n=this;(Array.isArray(t)?t:[t]).forEach((function(t){var r=n.events[t];r&&r.splice(r.indexOf(e),1)}))},e.prototype.emit=function(t){for(var e=this,n=[],r=arguments.length-1;r-- >0;)n[r]=arguments[r+1];var i=this.activeEvents;i.hasOwnProperty(t)&&i[t]||(i[t]=!0,(this.events[t]||[]).forEach((function(t){return t.apply(e,n)})),i[t]=!1)},e.prototype.deferredEmit=function(t){for(var e,n=[],r=arguments.length-1;r-- >0;)n[r]=arguments[r+1];var i=this.deferredEvents;(e=this).emit.apply(e,[t].concat(n)),(i[t]||(i[t]=[])).push(n)},e.prototype.setOptions=function(t){this.setState(t)},e.prototype.resize=function(t){this.setOptions({width:t})},e.prototype.reset=function(){this.colors.forEach((function(t){return t.reset()})),this.setState({colors:this.colors})},e.prototype.onMount=function(t){this.el=t,this.deferredEmit("mount",this)},e.prototype.onColorChange=function(t,e){this.setState({color:this.color}),this.inputActive&&(this.inputActive=!1,this.emit("input:change",t,e)),this.emit("color:change",t,e)},e.prototype.emitInputEvent=function(t,e){0===t?this.emit("input:start",this.color,e):1===t?this.emit("input:move",this.color,e):2===t&&this.emit("input:end",this.color,e)},e.prototype.render=function(t,e){var n=this,r=e.layout;return Array.isArray(r)||(r=[{component:bn},{component:pn}],e.transparency&&r.push({component:pn,options:{sliderType:"alpha"}})),le("div",{class:"IroColorPicker",id:e.id,style:{display:e.display}},r.map((function(t,r){var i=t.component,s=t.options;return le(i,Object.assign({},e,s,{ref:void 0,onInput:n.emitInputEvent.bind(n),parent:n,index:r}))})))},e}(ue);mn.defaultProps=Object.assign({},{width:300,height:300,color:"#fff",colors:[],padding:6,layoutDirection:"vertical",borderColor:"#fff",borderWidth:0,handleRadius:8,activeHandleRadius:null,handleSvg:null,handleProps:{x:0,y:0},wheelLightness:!0,wheelAngle:0,wheelDirection:"anticlockwise",sliderSize:null,sliderMargin:12,boxHeight:null},{colors:[],display:"block",id:null,layout:"default",margin:null});var vn,yn,wn,xn=(yn=function(t,e){var n,r=document.createElement("div");function i(){var e=t instanceof Element?t:document.querySelector(t);e.appendChild(n.base),n.onMount(e)}return function(t,e,n){var r,i,s;Kt.__p&&Kt.__p(t,e),i=(r=void 0===Xt)?null:e.__k,t=le(ce,null,[t]),s=[],we(e,e.__k=t,i||ne,ne,void 0!==e.ownerSVGElement,i?null:re.slice.call(e.childNodes),s,!1,ne,r),xe(s,t)}(le(vn,Object.assign({},{ref:function(t){return n=t}},e)),r),"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i),n},yn.prototype=(vn=mn).prototype,Object.assign(yn,vn),yn.__component=vn,yn);!function(t){var e;t.version="5.5.2",t.Color=Ke,t.ColorPicker=xn,(e=t.ui||(t.ui={})).h=le,e.ComponentBase=dn,e.Handle=fn,e.Slider=pn,e.Wheel=bn,e.Box=gn}(wn||(wn={}));var Nn=wn;const kn=[{icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M43 11L16.875 37L5 25.1818" stroke="#008a00" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',label:"save"},{icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 8L40 40" stroke="#db3700" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M8 40L40 8" stroke="#db3700" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',label:"cancel"}],An=[{value:"#000000",describe:"black"},{value:"#4d4d4d",describe:"dimGrey"},{value:"#808080",describe:"grey"},{value:"#e6e6e6",describe:"lightGrey"},{value:"#ffffff",describe:"white"},{value:"#ff0000",describe:"red"},{value:"#ffa500",describe:"orange"},{value:"#ffff00",describe:"yellow"},{value:"#99e64d",describe:"lightGreen"},{value:"#008000",describe:"green"},{value:"#7fffd4",describe:"aquamarine"},{value:"#40e0d0",describe:"turquoise"},{value:"#4d99e6",describe:"lightBlue"},{value:"#0000ff",describe:"blue"},{value:"#800080",describe:"purple"}];var _n,En=class{constructor(t,e){this.tableMenus=t,this.options=e,this.attrs=Object.assign({},e.attribute),this.borderForm=[],this.saveButton=null,this.form=this.createPropertiesForm(e)}checkBtnsAction(t){"save"===t&&this.saveAction(this.options.type),this.removePropertiesForm(),this.tableMenus.showMenus(),this.tableMenus.updateMenus()}createActionBtns(t,e){const n=this.getUseLanguage(),r=document.createElement("div"),i=document.createDocumentFragment();r.classList.add("properties-form-action-row");for(const{icon:t,label:r}of kn){const s=document.createElement("button"),o=document.createElement("span");if(o.innerHTML=t,s.appendChild(o),D(s,{label:r}),e){const t=document.createElement("span");t.innerText=n(r),s.appendChild(t)}i.appendChild(s)}return r.addEventListener("click",(e=>t(e))),r.appendChild(i),r}createCheckBtns(t){const{menus:e,propertyName:n}=t,r=document.createElement("div"),i=document.createDocumentFragment();for(const{icon:t,describe:r,align:s}of e){const e=document.createElement("span");e.innerHTML=t,e.setAttribute("data-align",s),e.classList.add("ql-table-tooltip-hover"),this.options.attribute[n]===s&&e.classList.add("ql-table-btns-checked");const o=k(r);e.appendChild(o),i.appendChild(e)}return r.classList.add("ql-table-check-container"),r.appendChild(i),r.addEventListener("click",(t=>{const e=t.target.closest("span.ql-table-tooltip-hover"),i=e.getAttribute("data-align");this.switchButton(r,e),this.setAttribute(n,i)})),r}createColorContainer(t){const e=document.createElement("div");e.classList.add("ql-table-color-container");const n=this.createColorInput(t),r=this.createColorPicker(t);return e.appendChild(n),e.appendChild(r),e}createColorInput(t){const e=this.createInput(t);return e.classList.add("label-field-view-color"),e}createColorList(t){const e=this.getUseLanguage(),n=document.createElement("ul"),r=document.createDocumentFragment();n.classList.add("color-list");for(const{value:t,describe:n}of An){const i=document.createElement("li"),s=k(e(n));i.setAttribute("data-color",t),i.classList.add("ql-table-tooltip-hover"),P(i,{"background-color":t}),i.appendChild(s),r.appendChild(i)}return n.appendChild(r),n.addEventListener("click",(e=>{const r=e.target,i=("DIV"===r.tagName?r.parentElement:r).getAttribute("data-color");this.setAttribute(t,i,n),this.updateInputStatus(n,!1,!0)})),n}createColorPicker(t){const{propertyName:e,value:n}=t,r=document.createElement("span"),i=document.createElement("span");r.classList.add("color-picker"),i.classList.add("color-button"),n?P(i,{"background-color":n}):i.classList.add("color-unselected");const s=this.createColorPickerSelect(e);return i.addEventListener("click",(()=>{this.toggleHidden(s);const t=this.getColorClosest(r),e=null==t?void 0:t.querySelector(".property-input");this.updateSelectedStatus(s,null==e?void 0:e.value,"color")})),r.appendChild(i),r.appendChild(s),r}createColorPickerIcon(t,e,n){const r=document.createElement("div"),i=document.createElement("span"),s=document.createElement("button");return i.innerHTML=t,s.innerText=e,r.classList.add("erase-container"),r.appendChild(i),r.appendChild(s),r.addEventListener("click",n),r}createColorPickerSelect(t){const e=this.getUseLanguage(),n=document.createElement("div"),r=this.createColorPickerIcon('<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M4 42H44" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M31 4L7 28L13 34H21L41 14L31 4Z" fill="none" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',e("removeColor"),(()=>{this.setAttribute(t,"",n),this.updateInputStatus(n,!1,!0)})),i=this.createColorList(t),s=this.createPalette(t,e,n);return n.classList.add("color-picker-select","ql-hidden"),n.appendChild(r),n.appendChild(i),n.appendChild(s),n}createDropdown(t,e){const n=document.createElement("div"),r=document.createElement("span"),i=document.createElement("span");return"dropdown"===e&&(i.innerHTML=ee,i.classList.add("ql-table-dropdown-icon")),t&&(r.innerText=t),n.classList.add("ql-table-dropdown-properties"),r.classList.add("ql-table-dropdown-text"),n.appendChild(r),"dropdown"===e&&n.appendChild(i),{dropdown:n,dropText:r}}createInput(t){const{attribute:e,message:n,propertyName:r,value:i,valid:s}=t,{placeholder:o=""}=e,l=document.createElement("div"),a=document.createElement("div"),c=document.createElement("label"),u=document.createElement("input"),h=document.createElement("div");return l.classList.add("label-field-view"),a.classList.add("label-field-view-input-wrapper"),c.innerText=o,D(u,e),u.classList.add("property-input"),u.value=i,u.addEventListener("input",(t=>{const e=t.target.value;s&&this.switchHidden(h,s(e)),this.updateInputStatus(a,s&&!s(e)),this.setAttribute(r,e,l)})),h.classList.add("label-field-view-status","ql-hidden"),n&&(h.innerText=n),a.appendChild(u),a.appendChild(c),l.appendChild(a),s&&l.appendChild(h),l}createList(t,e){const{options:n,propertyName:r}=t;if(!n.length)return null;const i=document.createElement("ul");for(const t of n){const e=document.createElement("li");e.innerText=t,i.appendChild(e)}return i.classList.add("ql-table-dropdown-list","ql-hidden"),i.addEventListener("click",(t=>{const n=t.target.innerText;e.innerText=n,this.toggleBorderDisabled(n),this.setAttribute(r,n)})),i}createPalette(t,e,n){const r=document.createElement("div"),i=document.createElement("div"),s=document.createElement("div"),o=document.createElement("div"),l=new Nn.ColorPicker(o,{width:110,layout:[{component:Nn.ui.Wheel,options:{}}]}),a=this.createColorPickerIcon('<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 44C29.9601 44 26.3359 35.136 30 31C33.1264 27.4709 44 29.0856 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round"/><path d="M28 17C29.6569 17 31 15.6569 31 14C31 12.3431 29.6569 11 28 11C26.3431 11 25 12.3431 25 14C25 15.6569 26.3431 17 28 17Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round"/><path d="M16 21C17.6569 21 19 19.6569 19 18C19 16.3431 17.6569 15 16 15C14.3431 15 13 16.3431 13 18C13 19.6569 14.3431 21 16 21Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round"/><path d="M17 34C18.6569 34 20 32.6569 20 31C20 29.3431 18.6569 28 17 28C15.3431 28 14 29.3431 14 31C14 32.6569 15.3431 34 17 34Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round"/></svg>',e("colorPicker"),(()=>this.toggleHidden(i))),c=this.createActionBtns((e=>{const s=e.target.closest("button");s&&("save"===s.getAttribute("label")&&(this.setAttribute(t,l.color.hexString,n),this.updateInputStatus(r,!1,!0)),i.classList.add("ql-hidden"),n.classList.add("ql-hidden"))}),!1);return i.classList.add("color-picker-palette","ql-hidden"),s.classList.add("color-picker-wrap"),o.classList.add("iro-container"),s.appendChild(o),s.appendChild(c),i.appendChild(s),r.appendChild(a),r.appendChild(i),r}createProperty(t){const{content:e,children:n}=t,r=this.getUseLanguage(),i=document.createElement("div"),s=document.createElement("label");s.innerText=e,s.classList.add("ql-table-dropdown-label"),i.classList.add("properties-form-row"),1===n.length&&i.classList.add("properties-form-row-full"),i.appendChild(s);for(const t of n){const n=this.createPropertyChild(t);n&&i.appendChild(n),n&&e===r("border")&&this.borderForm.push(n)}return i}createPropertyChild(t){const{category:e,value:n}=t;switch(e){case"dropdown":const{dropdown:r,dropText:i}=this.createDropdown(n,e),s=this.createList(t,i);return r.appendChild(s),r.addEventListener("click",(()=>{this.toggleHidden(s),this.updateSelectedStatus(r,i.innerText,"dropdown")})),r;case"color":return this.createColorContainer(t);case"menus":return this.createCheckBtns(t);case"input":return this.createInput(t)}}createPropertiesForm(t){const e=this.getUseLanguage(),{title:n,properties:r}=function({type:t,attribute:e},n){return"table"===t?function(t,e){return{title:e("tblProps"),properties:[{content:e("border"),children:[{category:"dropdown",propertyName:"border-style",value:t["border-style"],options:["dashed","dotted","double","groove","inset","none","outset","ridge","solid"]},{category:"color",propertyName:"border-color",value:t["border-color"],attribute:{type:"text",placeholder:e("color")},valid:R,message:e("colorMsg")},{category:"input",propertyName:"border-width",value:N(t["border-width"]),attribute:{type:"text",placeholder:e("width")},valid:I,message:e("dimsMsg")}]},{content:e("background"),children:[{category:"color",propertyName:"background-color",value:t["background-color"],attribute:{type:"text",placeholder:e("color")},valid:R,message:e("colorMsg")}]},{content:e("dimsAlm"),children:[{category:"input",propertyName:"width",value:N(t.width),attribute:{type:"text",placeholder:e("width")},valid:I,message:e("dimsMsg")},{category:"input",propertyName:"height",value:N(t.height),attribute:{type:"text",placeholder:e("height")},valid:I,message:e("dimsMsg")},{category:"menus",propertyName:"align",value:t.align,menus:[{icon:l,describe:e("alTblL"),align:"left"},{icon:o,describe:e("tblC"),align:"center"},{icon:a,describe:e("alTblR"),align:"right"}]}]}]}}(e,n):function(t,e){return{title:e("cellProps"),properties:[{content:e("border"),children:[{category:"dropdown",propertyName:"border-style",value:t["border-style"],options:["dashed","dotted","double","groove","inset","none","outset","ridge","solid"]},{category:"color",propertyName:"border-color",value:t["border-color"],attribute:{type:"text",placeholder:e("color")},valid:R,message:e("colorMsg")},{category:"input",propertyName:"border-width",value:N(t["border-width"]),attribute:{type:"text",placeholder:e("width")},valid:I,message:e("dimsMsg")}]},{content:e("background"),children:[{category:"color",propertyName:"background-color",value:t["background-color"],attribute:{type:"text",placeholder:e("color")},valid:R,message:e("colorMsg")}]},{content:e("dims"),children:[{category:"input",propertyName:"width",value:N(t.width),attribute:{type:"text",placeholder:e("width")},valid:I,message:e("dimsMsg")},{category:"input",propertyName:"height",value:N(t.height),attribute:{type:"text",placeholder:e("height")},valid:I,message:e("dimsMsg")},{category:"input",propertyName:"padding",value:N(t.padding),attribute:{type:"text",placeholder:e("padding")},valid:I,message:e("dimsMsg")}]},{content:e("tblCellTxtAlm"),children:[{category:"menus",propertyName:"text-align",value:t["text-align"],menus:[{icon:l,describe:e("alCellTxtL"),align:"left"},{icon:o,describe:e("alCellTxtC"),align:"center"},{icon:a,describe:e("alCellTxtR"),align:"right"},{icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M42 19H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 9H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 29H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M42 39H6" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',describe:e("jusfCellTxt"),align:"justify"}]},{category:"menus",propertyName:"vertical-align",value:t["vertical-align"],menus:[{icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 36.3056H42" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M6 42H42" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M30 12L24 6L18 12V12" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M24 6V29" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',describe:e("alCellTxtT"),align:"top"},{icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18 36L24 30L30 36" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M23.9999 30.9998V43.9998" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M18 12L24 18L30 12" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M23.9999 17.0002V4.00022" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M6 24.0004H42" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',describe:e("alCellTxtM"),align:"middle"},{icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M6 36.3056H42" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M6 42H42" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M30 23L24 29L18 23V23" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M24 6V29" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>',describe:e("alCellTxtB"),align:"bottom"}]}]}]}}(e,n)}(t,e),i=document.createElement("div");i.classList.add("ql-table-properties-form");const s=document.createElement("h2"),c=this.createActionBtns((t=>{const e=t.target.closest("button");e&&this.checkBtnsAction(e.getAttribute("label"))}),!0);s.innerText=n,s.classList.add("properties-form-header"),i.appendChild(s);for(const t of r){const e=this.createProperty(t);i.appendChild(e)}return i.appendChild(c),this.setBorderDisabled(),this.tableMenus.quill.container.appendChild(i),this.updatePropertiesForm(i,t.type),this.setSaveButton(c),i.addEventListener("click",(t=>{const e=t.target;this.hiddenSelectList(e)})),i}getCellStyle(t,e){const n=(t.getAttribute("style")||"").split(";").filter((t=>t.trim())).reduce(((t,e)=>{const n=e.split(":");return Object.assign(Object.assign({},t),{[n[0].trim()]:n[1].trim()})}),{});return Object.assign(n,e),Object.keys(n).reduce(((t,e)=>t+`${e}: ${n[e]}; `),"")}getColorClosest(t){return T(t,".ql-table-color-container")}getComputeBounds(t){if("table"===t){const{table:t}=this.tableMenus,[e,n]=this.tableMenus.getCorrectBounds(t);return e.bottom>n.bottom?Object.assign(Object.assign({},e),{bottom:n.height}):e}{const{computeBounds:t}=this.tableMenus.getSelectedTdsInfo();return t}}getDiffProperties(){const t=this.attrs,e=this.options.attribute;return Object.keys(t).reduce(((n,r)=>(t[r]!==e[r]&&(n[r]=function(t){return!(!t.endsWith("width")&&!t.endsWith("height"))}(r)?function(t){if(!t)return t;const e=t.slice(-2);return"px"!==e&&"em"!==e?t+"px":t}(t[r]):t[r]),n)),{})}getUseLanguage(){const{language:t}=this.tableMenus.tableBetter;return t.useLanguage.bind(t)}getViewportSize(){return{viewWidth:document.documentElement.clientWidth,viewHeight:document.documentElement.clientHeight}}hiddenSelectList(t){var e,n;const r=".ql-table-dropdown-properties",i=".color-picker",s=this.form.querySelectorAll(".ql-table-dropdown-list"),o=this.form.querySelectorAll(".color-picker-select");for(const l of[...s,...o])(null===(e=l.closest(r))||void 0===e?void 0:e.isEqualNode(t.closest(r)))||(null===(n=l.closest(i))||void 0===n?void 0:n.isEqualNode(t.closest(i)))||l.classList.add("ql-hidden")}removePropertiesForm(){this.form.remove(),this.borderForm=[]}saveAction(t){"table"===t?this.saveTableAction():this.saveCellAction()}saveCellAction(){const{selectedTds:t}=this.tableMenus.tableBetter.cellSelection,{quill:n,table:r}=this.tableMenus,i=e().find(r).colgroup(),s=this.getDiffProperties(),o=parseFloat(s.width),l=s["text-align"];l&&delete s["text-align"];const a=[];if(i&&o){delete s.width;const{computeBounds:t}=this.tableMenus.getSelectedTdsInfo(),e=L(t,r,n.container);for(const t of e)t.setAttribute("width",`${o}`)}for(const n of t){const t=e().find(n),r=t.statics.blotName,i=t.formats()[r],o=this.getCellStyle(n,s);if(l){const e="left"===l?"":l;t.children.forEach((t=>{t.statics.blotName===m.blotName?t.children.forEach((t=>{t.format&&t.format("align",e)})):t.format("align",e)}))}const c=t.replaceWith(r,Object.assign(Object.assign({},i),{style:o}));a.push(c.domNode)}this.tableMenus.tableBetter.cellSelection.setSelectedTds(a)}saveTableAction(){var t;const{table:n,tableBetter:r}=this.tableMenus,i=null===(t=e().find(n).temporary())||void 0===t?void 0:t.domNode,s=n.querySelector("td"),o=this.getDiffProperties(),l=o.align;switch(delete o.align,l){case"center":Object.assign(o,{margin:"0 auto"});break;case"left":Object.assign(o,{margin:""});break;case"right":Object.assign(o,{"margin-left":"auto","margin-right":""})}P(i||n,o),r.cellSelection.setSelected(s)}setAttribute(t,e,n){this.attrs[t]=e,t.includes("-color")&&this.updateSelectColor(this.getColorClosest(n),e)}setBorderDisabled(){const[t]=this.borderForm,e=t.querySelector(".ql-table-dropdown-text").innerText;this.toggleBorderDisabled(e)}setSaveButton(t){const e=t.querySelector('button[label="save"]');this.saveButton=e}setSaveButtonDisabled(t){this.saveButton&&(t?this.saveButton.setAttribute("disabled","true"):this.saveButton.removeAttribute("disabled"))}switchButton(t,e){const n=t.querySelectorAll("span.ql-table-tooltip-hover");for(const t of n)t.classList.remove("ql-table-btns-checked");e.classList.add("ql-table-btns-checked")}switchHidden(t,e){e?t.classList.add("ql-hidden"):t.classList.remove("ql-hidden")}toggleBorderDisabled(t){const[,e,n]=this.borderForm;"none"!==t&&t?(e.classList.remove("ql-table-disabled"),n.classList.remove("ql-table-disabled")):(this.attrs["border-color"]="",this.attrs["border-width"]="",this.updateSelectColor(e,""),this.updateInputValue(n,""),e.classList.add("ql-table-disabled"),n.classList.add("ql-table-disabled"))}toggleHidden(t){t.classList.toggle("ql-hidden")}updateInputValue(t,e){t.querySelector(".property-input").value=e}updateInputStatus(t,e,n){const r=(n?this.getColorClosest(t):T(t,".label-field-view")).querySelector(".label-field-view-input-wrapper");e?(r.classList.add("label-field-view-error"),this.setSaveButtonDisabled(!0)):(r.classList.remove("label-field-view-error"),this.form.querySelectorAll(".label-field-view-error").length||this.setSaveButtonDisabled(!1))}updatePropertiesForm(t,e){t.classList.remove("ql-table-triangle-none");const{height:n,width:r}=t.getBoundingClientRect(),i=this.tableMenus.quill.container.getBoundingClientRect(),{top:s,left:o,right:l,bottom:a}=this.getComputeBounds(e),{viewHeight:c}=this.getViewportSize();let u=a+10,h=o+l-r>>1;u+i.top+n>c?(u=s-n-10,u<0?(u=i.height-n>>1,t.classList.add("ql-table-triangle-none")):(t.classList.add("ql-table-triangle-up"),t.classList.remove("ql-table-triangle-down"))):(t.classList.add("ql-table-triangle-down"),t.classList.remove("ql-table-triangle-up")),h<i.left?(h=0,t.classList.add("ql-table-triangle-none")):h+r>i.right&&(h=i.right-r,t.classList.add("ql-table-triangle-none")),P(t,{left:`${h}px`,top:`${u}px`})}updateSelectColor(t,e){const n=t.querySelector(".property-input"),r=t.querySelector(".color-button"),i=t.querySelector(".color-picker-select"),s=t.querySelector(".label-field-view-status");e?r.classList.remove("color-unselected"):r.classList.add("color-unselected"),n.value=e,P(r,{"background-color":e}),i.classList.add("ql-hidden"),this.switchHidden(s,R(e))}updateSelectedStatus(t,e,n){const r="color"===n?".color-list":".ql-table-dropdown-list",i=t.querySelector(r);if(!i)return;const s=Array.from(i.querySelectorAll("li"));for(const t of s)t.classList.remove(`ql-table-${n}-selected`);const o=s.find((t=>("color"===n?t.getAttribute("data-color"):t.innerText)===e));o&&o.classList.add(`ql-table-${n}-selected`)}};!function(t){t.left="margin-left",t.right="margin-right"}(_n||(_n={}));var Cn=class{constructor(t,e){this.quill=t,this.table=null,this.prevList=null,this.prevTooltip=null,this.scroll=!1,this.tableBetter=e,this.tablePropertiesForm=null,this.quill.root.addEventListener("click",this.handleClick.bind(this)),this.root=this.createMenus()}copyTable(){return function(t,e,n,r){return new(n||(n=Promise))((function(e,i){function s(t){try{l(r.next(t))}catch(t){i(t)}}function o(t){try{l(r.throw(t))}catch(t){i(t)}}function l(t){var r;t.done?e(t.value):(r=t.value,r instanceof n?r:new n((function(t){t(r)}))).then(s,o)}l((r=r.apply(t,[])).next())}))}(this,0,void 0,(function*(){if(!this.table)return;const t=e().find(this.table);if(!t)return;const n="<p><br></p>"+t.getCopyTable(),r=this.tableBetter.cellSelection.getText(n),i=new ClipboardItem({"text/html":new Blob([n],{type:"text/html"}),"text/plain":new Blob([r],{type:"text/plain"})});try{yield navigator.clipboard.write([i]);const n=this.quill.getIndex(t),r=t.length();this.quill.setSelection(n+r,e().sources.SILENT),this.tableBetter.hideTools(),this.quill.scrollSelectionIntoView()}catch(t){console.error("Failed to copy table:",t)}}))}createList(t){if(!t)return null;const e=document.createElement("ul");for(const[,n]of Object.entries(t)){const{content:t,handler:r}=n,i=document.createElement("li");i.innerText=t,i.addEventListener("click",r.bind(this)),e.appendChild(i)}return e.classList.add("ql-table-dropdown-list","ql-hidden"),e}createMenu(t,e,n){const r=document.createElement("div"),i=document.createElement("span");return i.innerHTML=n?t+e:t,r.classList.add("ql-table-dropdown"),i.classList.add("ql-table-tooltip-hover"),r.appendChild(i),r}createMenus(){const{language:t,options:e={}}=this.tableBetter,{menus:n}=e,r=t.useLanguage.bind(t),i=document.createElement("div");i.classList.add("ql-table-menus-container","ql-hidden");for(const[,t]of Object.entries(function(t,e){const n={column:{content:t("col"),icon:'<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1692084271333" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2200" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M9.14372835 1039.20071111L1020.26808889 1039.20071111l0-1048.576L9.14372835-9.37528889 9.14372835 1039.20071111z m252.77672107-711.53454649l1e-8-262.144 175.00150897 0 0 262.144L261.92044942 327.66616462zM942.48705138 702.1592576l0 262.14400001-178.89289103-1e-8 1e-8-262.144 178.89289102 0z m-256.66810311 0l0 262.144-171.11595236 0 0-262.144 171.11595236 0z m-248.89698987 0l0 262.144L261.92044943 964.3032576l-1e-8-262.144 175.00150898 0z m505.56509298-299.59563948L942.48705139 627.26180409l-178.89289104 0 0-224.69818596 178.89289103-1e-8z m-256.66810311 1e-8L685.81894827 627.26180409l-171.11595236 0 0-224.69818596 171.11595236 0z m-248.89698987 0L436.9219584 627.26180409 261.92044943 627.26180409l0-224.69818596 175.00150897 0z m505.56509298-337.04145352l0 262.14400001-178.89289102 0-1e-8-262.144 178.89289103-1e-8z m-256.66810311 1e-8l0 262.144-171.11595236 0 0-262.144 171.11595236 0z" fill="#515151" p-id="2201"></path></svg>',handler(t,e){this.toggleAttribute(t,e)},children:{left:{content:t("insColL"),handler(){const{leftTd:t}=this.getSelectedTdsInfo(),e=this.table.getBoundingClientRect();this.insertColumn(t,0),U(this.table,e,72),this.updateMenus()}},right:{content:t("insColR"),handler(){const{rightTd:t}=this.getSelectedTdsInfo(),e=this.table.getBoundingClientRect();this.insertColumn(t,1),U(this.table,e,72),this.updateMenus()}},delete:{content:t("delCol"),handler(){this.deleteColumn()}}}},row:{content:t("row"),icon:'<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1692084279720" class="icon" viewBox="0 0 1181 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2344" xmlns:xlink="http://www.w3.org/1999/xlink" width="18.453125" height="16"><path d="M1142.15367111 0H39.38417778C7.99630222 0 0 8.27050667 0 39.38531555v945.2293689C0 1015.72949333 7.99516445 1024 39.38531555 1024h1102.76835556c31.39128889 0 39.38417778-8.27050667 39.38417778-39.38531555V39.38531555c0-31.11480889-7.99516445-39.38531555-39.38417778-39.38531555zM354.46328889 945.23050667l-276.992 3.26997333V749.568l276.992-1.25952v196.92202667z m0-275.69265778H78.76835555V472.61468445h275.69265778v196.92316444z m0-275.69152H78.76835555V236.30848h275.69265778v157.53671111z m393.84632889 551.38417778H433.23050667V748.30848h315.07683555v196.92202667z m0-275.69265778H433.23050667V472.61468445h315.07683555v196.92316444z m0-275.69152H433.23050667V236.30848h315.07683555v157.53671111z m354.46101333 551.38417778H827.07683555V748.30848h275.69265778v196.92202667z m0-275.69265778H827.07683555V472.61468445h275.69265778v196.92316444z m0-275.69152H827.07683555V236.30848h275.69265778v157.53671111z" fill="#515151" p-id="2345"></path></svg>',handler(t,e){this.toggleAttribute(t,e)},children:{above:{content:t("insRowAbv"),handler(){const{leftTd:t}=this.getSelectedTdsInfo();this.insertRow(t,0),this.updateMenus()}},below:{content:t("insRowBlw"),handler(){const{rightTd:t}=this.getSelectedTdsInfo();this.insertRow(t,1),this.updateMenus()}},delete:{content:t("delRow"),handler(){this.deleteRow()}}}},merge:{content:t("mCells"),icon:'<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1692084199654" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1975" width="16" height="16" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M776.08580741 364.42263703c-15.53445925-7.76722963-31.06891852-7.76722963-46.60337778 0L589.6722963 512l139.81013333 147.57736297c15.53445925 7.76722963 31.06891852 7.76722963 46.60337778 0 15.53445925-15.53445925 15.53445925-31.06891852 0-46.60337779L706.18074075 543.06891852h163.11182222c15.53445925 0 31.06891852-15.53445925 31.06891851-31.06891852s-15.53445925-31.06891852-31.06891851-31.06891852H706.18074075l69.90506666-69.90506666c7.76722963-15.53445925 7.76722963-31.06891852 0-46.60337779z m-528.17161482 0c-15.53445925 15.53445925-15.53445925 31.06891852 0 46.60337779l69.90506666 69.90506666H154.70743703c-15.53445925 0-31.06891852 15.53445925-31.06891851 31.06891852s15.53445925 31.06891852 31.06891851 31.06891852H317.81925925l-69.90506666 69.90506666c-15.53445925 15.53445925-15.53445925 31.06891852 0 46.60337779 15.53445925 7.76722963 31.06891852 7.76722963 46.60337778 0L434.3277037 512 294.51757037 364.42263703c-15.53445925-7.76722963-31.06891852-7.76722963-46.60337778 0z" fill="#515151" p-id="1976"></path><path d="M317.81925925 939.19762963H84.80237037V84.80237037h233.01688888v116.50844445h77.6722963V7.13007408H7.13007408v1009.73985184h388.36148147V822.68918518h-77.6722963zM628.50844445 7.13007408v194.18074074h77.6722963v-116.50844445h233.01688888v854.39525926H706.18074075v-116.50844445h-77.6722963v194.18074074h388.36148147V7.13007408z" fill="#515151" p-id="1977"></path></svg>',handler(t,e){this.toggleAttribute(t,e)},children:{merge:{content:t("mCells"),handler(){this.mergeCells(),this.updateMenus()}},split:{content:t("sCell"),handler(){this.splitCell(),this.updateMenus()}}}},table:{content:t("tblProps"),icon:te,handler(t,e){const n=Object.assign(Object.assign({},B(this.table,f)),{align:this.getTableAlignment(this.table)});this.toggleAttribute(t,e),this.tablePropertiesForm=new En(this,{attribute:n,type:"table"}),this.hideMenus()}},cell:{content:t("cellProps"),icon:'<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1692084286647" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2488" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M1058.13333333 0v1024H-34.13333333V0h1092.26666666zM460.8 563.2H68.26666667V921.6h392.53333333V563.2z m494.93333333 0H563.2V921.6h392.53333333V563.2zM460.8 102.4H68.26666667v358.4h392.53333333V102.4z" fill="#515151" p-id="2489"></path></svg>',handler(t,e){const{selectedTds:n}=this.tableBetter.cellSelection,r=n.length>1?this.getSelectedTdsAttrs(n):this.getSelectedTdAttrs(n[0]);this.toggleAttribute(t,e),this.tablePropertiesForm=new En(this,{attribute:r,type:"cell"}),this.hideMenus()}},wrap:{content:t("insParaOTbl"),icon:'<?xml version="1.0" standalone="no"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd"><svg t="1692084879007" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="968" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><path d="M512 332.57913685H49.39294151c-20.56031346 0-41.12062691-17.13359531-41.12062805-41.12062692V44.73474502c0-20.56031346 17.13359531-41.12062691 41.12062805-41.12062691H512c20.56031346 0 41.12062691 17.13359531 41.12062691 41.12062691v246.72376491c0 23.98703275-17.13359531 41.12062691-41.12062691 41.12062692zM90.51356843 250.33788188h380.36580466V85.85537308H90.51356843v164.4825088z m884.09349006 757.30488889h-925.21411698c-20.56031346 0-41.12062691-17.13359531-41.12062805-41.12062692v-246.72376491c0-20.56031346 17.13359531-41.12062691 41.12062805-41.12062691h921.78739883c20.56031346 0 41.12062691 17.13359531 41.12062691 41.12062691v246.72376491c0 23.98703275-17.13359531 41.12062691-37.69390876 41.12062692zM90.51356843 928.82823509h842.97286314v-164.48250994H90.51356843v164.48250994z" fill="#515151" p-id="969"></path><path d="M974.60705849 1017.92292864h-925.21411698c-27.41375203 0-47.97406549-20.56031346-47.97406549-47.97406549v-246.72376491c0-27.41375203 20.56031346-47.97406549 47.97406549-47.97406549h921.78739883c27.41375203 0 47.97406549 20.56031346 47.97406435 47.97406549v246.72376491c3.42671929 23.98703275-20.56031346 47.97406549-44.5473462 47.97406549z m-925.21411698-325.53830173c-17.13359531 0-30.84047019 13.70687602-30.84047132 30.84047133v246.72376491c0 17.13359531 13.70687602 30.84047019 30.84047132 30.84047018h921.78739883c17.13359531 0 30.84047019-13.70687602 30.84047019-30.84047018v-246.72376491c0-17.13359531-13.70687602-30.84047019-30.84047019-30.84047133H49.39294151z m890.9469275 243.29704675h-856.67973802v-181.61610523h860.10645731v181.61610523h-3.42671929zM100.79372515 921.97479765h825.83926784V774.62588188H100.79372515v147.34891577z m411.20627485-582.54222223H49.39294151c-27.41375203 0-47.97406549-20.56031346-47.97406549-47.97406549V44.73474502c0-27.41375203 20.56031346-47.97406549 47.97406549-47.97406549H512c27.41375203 0 47.97406549 20.56031346 47.97406549 47.97406549v246.72376491c0 27.41375203-20.56031346 47.97406549-47.97406549 47.97406549zM49.39294151 13.89427484c-17.13359531 0-30.84047019 13.70687602-30.84047132 30.84047018v246.72376491c0 17.13359531 13.70687602 30.84047019 30.84047132 30.84047019H512c17.13359531 0 30.84047019-13.70687602 30.84047019-30.84047019V44.73474502c0-17.13359531-13.70687602-30.84047019-30.84047019-30.84047018H49.39294151zM481.15952981 260.61803974H83.66013099V79.00193451h397.49939882V260.61803974zM100.79372515 243.48444444h363.23220936V96.13552981H100.79372515v147.34891463z" fill="#515151" p-id="970"></path><path d="M974.60705849 130.40271929H628.50844445c-6.85343744 0-10.28015673 3.42671929-10.28015674 10.28015672v58.25422223c0 6.85343744 3.42671929 10.28015673 10.28015674 10.28015673h304.97798712V466.2211766H546.26718947l27.41375204-20.56031345c3.42671929-3.42671929 6.85343744-10.28015673 6.85343744-17.13359531v-58.25422223c0-6.85343744-3.42671929-10.28015673-10.28015673-10.28015672-3.42671929 0-3.42671929 0-6.85343744 3.42671928L409.19843157 486.78149006c-10.28015673 6.85343744-10.28015673 20.56031346-3.42671928 27.41375203l3.42671928 3.42671816 157.62907136 130.21532045c3.42671929 3.42671929 10.28015673 3.42671929 13.70687602 0 0-3.42671929 3.42671929-3.42671929 3.42671929-6.85343744v-61.6809415c0-6.85343744-3.42671929-10.28015673-6.85343858-13.70687602l-20.56031345-17.13359417h421.48643157c20.56031346 0 41.12062691-17.13359531 41.12062691-41.12062805V168.09662691c-6.85343744-20.56031346-23.98703275-37.69390877-44.5473462-37.69390762z" fill="#515151" p-id="971"></path><path d="M573.68094151 661.54415673c-3.42671929 0-6.85343744 0-10.28015673-3.42671929l-157.62907249-130.21531933-3.4267193-3.42671928c-3.42671929-6.85343744-6.85343744-13.70687602-6.85343744-20.56031346 0-6.85343744 3.42671929-13.70687602 10.28015674-20.5603146l157.62907249-126.78860117c3.42671929-3.42671929 6.85343744-3.42671929 10.28015673-3.42671815 10.28015673 0 17.13359531 6.85343744 17.13359417 17.13359416v58.25422223c0 10.28015673-3.42671929 17.13359531-10.28015673 23.98703275l-10.28015673 6.85343744H923.20627485v-239.87032634h-294.6978304c-10.28015673 0-17.13359531-6.85343744-17.13359531-17.13359416V140.68287601c0-10.28015673 6.85343744-17.13359531 17.13359531-17.13359531h346.09861404c27.41375203 0 47.97406549 20.56031346 47.97406549 47.9740655v335.81845732c0 27.41375203-20.56031346 47.97406549-47.97406549 47.97406549H577.10765966l3.42671929 3.42671929c6.85343744 6.85343744 10.28015673 13.70687602 10.28015673 20.56031346v61.6809415c0 3.42671929 0 6.85343744-3.42671815 10.28015674-3.42671929 6.85343744-10.28015673 10.28015673-13.70687602 10.28015673z m0-291.27111112l-157.6290725 126.78860117c-3.42671929 3.42671929-3.42671929 3.42671929-3.42671815 6.85343859s0 6.85343744 3.42671815 10.28015672l157.6290725 130.21532047h3.42671815v-61.68094151c0-3.42671929 0-6.85343744-3.42671815-10.28015673l-41.12062805-34.26718948h442.04674503c17.13359531 0 30.84047019-13.70687602 30.84047132-30.84047132V168.09662691c0-17.13359531-13.70687602-30.84047019-30.84047132-30.84047018H628.50844445v61.68094151h311.83142456v274.1375158H522.28015673l47.97406549-37.69390763c3.42671929-3.42671929 3.42671929-6.85343744 3.42671929-10.28015787v-54.82750293z" fill="#515151" p-id="972"></path></svg>',handler(t,e){this.toggleAttribute(t,e)},children:{before:{content:t("insB4"),handler(){this.insertParagraph(-1)}},after:{content:t("insAft"),handler(){this.insertParagraph(1)}}}},delete:{content:t("delTable"),icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9 10V44H39V10H9Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round"/><path d="M20 20V33" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M28 20V33" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M4 10H44" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M16 10L19.289 4H28.7771L32 10H16Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round"/></svg>',handler(){this.deleteTable()}}},r={copy:{content:t("copyTable"),icon:'<?xml version="1.0" encoding="UTF-8"?><svg width="16" height="16" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13 12.4316V7.8125C13 6.2592 14.2592 5 15.8125 5H40.1875C41.7408 5 43 6.2592 43 7.8125V32.1875C43 33.7408 41.7408 35 40.1875 35H35.5163" stroke="#333" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M32.1875 13H7.8125C6.2592 13 5 14.2592 5 15.8125V40.1875C5 41.7408 6.2592 43 7.8125 43H32.1875C33.7408 43 35 41.7408 35 40.1875V15.8125C35 14.2592 33.7408 13 32.1875 13Z" fill="none" stroke="#333" stroke-width="4" stroke-linejoin="round"/></svg>',handler(){this.copyTable()}}};return(null==e?void 0:e.length)?Object.values(e).reduce(((t,e)=>(t[e]=Object.assign({},n,r)[e],t)),{}):n}(r,n))){const{content:e,icon:n,children:r,handler:s}=t,o=this.createList(r),l=k(e),a=this.createMenu(n,ee,!!r);a.appendChild(l),o&&a.appendChild(o),i.appendChild(a),a.addEventListener("click",s.bind(this,o,l))}return this.quill.container.appendChild(i),i}deleteColumn(t=!1){const{computeBounds:n,leftTd:r,rightTd:i}=this.getSelectedTdsInfo(),s=this.table.getBoundingClientRect(),o=S(n,this.table,this.quill.container,"column"),l=L(n,this.table,this.quill.container),a=e().find(r).table(),{changeTds:c,delTds:u}=this.getCorrectTds(o,n,r,i);t&&u.length!==this.tableBetter.cellSelection.selectedTds.length||(this.tableBetter.cellSelection.updateSelected("column"),a.deleteColumn(c,u,this.deleteTable.bind(this),l),U(this.table,s,n.left-n.right),this.updateMenus())}deleteRow(t=!1){const n=this.tableBetter.cellSelection.selectedTds,r={};for(const t of n){let n=~~t.getAttribute("rowspan")||1,i=e().find(t.parentElement);if(n>1)for(;i&&n;){const t=i.children.head.domNode.getAttribute("data-row");r[t]||(r[t]=i),i=i.next,n--}else{const e=t.getAttribute("data-row");r[e]||(r[e]=i)}}const i=Object.values(r);t&&i.reduce(((t,e)=>t+e.children.length),0)!==n.length||(this.tableBetter.cellSelection.updateSelected("row"),e().find(n[0]).table().deleteRow(i,this.deleteTable.bind(this)),this.updateMenus())}deleteTable(){const t=e().find(this.table);if(!t)return;const n=t.offset(this.quill.scroll);t.remove(),this.tableBetter.hideTools(),this.quill.setSelection(n-1,0,e().sources.USER)}destroyTablePropertiesForm(){this.tablePropertiesForm&&(this.tablePropertiesForm.removePropertiesForm(),this.tablePropertiesForm=null)}getCellsOffset(t,n,r,i){const s=e().find(this.table).descendants(W),o=Math.max(n.left,t.left),l=Math.min(n.right,t.right),a=new Map,c=new Map,u=new Map;for(const e of s){const{left:r,right:i}=j(e.domNode,this.quill.container);r+2>=o&&i<=l+2?this.setCellsMap(e,a):r+2>=t.left&&i<=n.left+2?this.setCellsMap(e,c):r+2>=n.right&&i<=t.right+2&&this.setCellsMap(e,u)}return this.getDiffOffset(a)||this.getDiffOffset(c,r)+this.getDiffOffset(u,i)}getColsOffset(t,e,n){let r=t.children.head;const i=Math.max(n.left,e.left),s=Math.min(n.right,e.right);let o=null,l=null,a=0;for(;r;){const{width:t}=r.domNode.getBoundingClientRect();if(o||l?(o=l,l+=t):(o=j(r.domNode,this.quill.container).left,l=o+t),o>s)break;o>=i&&l<=s&&a--,r=r.next}return a}getCorrectBounds(t){const e=this.quill.container.getBoundingClientRect(),n=j(t,this.quill.container);return n.width>=e.width?[Object.assign(Object.assign({},n),{left:0,right:e.width}),e]:[n,e]}getCorrectTds(t,n,r,i){const s=[],o=[],l=e().find(r).table().colgroup(),a=~~r.getAttribute("colspan")||1,c=~~i.getAttribute("colspan")||1;if(l)for(const e of t){const t=j(e,this.quill.container);if(t.left+2>=n.left&&t.right<=n.right+2)o.push(e);else{const r=this.getColsOffset(l,n,t);s.push([e,r])}}else for(const e of t){const t=j(e,this.quill.container);if(t.left+2>=n.left&&t.right<=n.right+2)o.push(e);else{const r=this.getCellsOffset(n,t,a,c);s.push([e,r])}}return{changeTds:s,delTds:o}}getDiffOffset(t,e){let n=0;const r=this.getTdsFromMap(t);if(r.length)if(e){for(const t of r)n+=~~t.getAttribute("colspan")||1;n-=e}else for(const t of r)n-=~~t.getAttribute("colspan")||1;return n}getRefInfo(t,e){let n=null;if(!t)return{id:tt(),ref:n};let r=t.children.head;const i=r.domNode.getAttribute("data-row");for(;r;){const{left:t}=r.domNode.getBoundingClientRect();if(Math.abs(t-e)<=2)return{id:i,ref:r};Math.abs(t-e)>=2&&!n&&(n=r),r=r.next}return{id:i,ref:n}}getSelectedTdAttrs(t){const n=function(t){const e="left";let n=null;const r=t.descendants(V),i=t.descendants(v),s=t.descendants(x);function o(t){for(const e of t.domNode.classList)if(/ql-align-/.test(e))return e.split("ql-align-")[1];return e}for(const t of[...r,...i,...s]){const r=o(t);if(null!=(l=n)&&l!==r)return e;n=r}var l;return null!=n?n:e}(e().find(t));return n?Object.assign(Object.assign({},B(t,h)),{"text-align":n}):B(t,h)}getSelectedTdsAttrs(t){const e=new Map;let n=null;for(const r of t){const t=this.getSelectedTdAttrs(r);if(n)for(const r of Object.keys(n))e.has(r)||t[r]!==n[r]&&e.set(r,!1);else n=t}for(const t of Object.keys(n))e.has(t)&&(n[t]=u[t]);return n}getSelectedTdsInfo(){const{startTd:t,endTd:e}=this.tableBetter.cellSelection,n=j(t,this.quill.container),r=j(e,this.quill.container),i=q(n,r);return n.left<=r.left&&n.top<=r.top?{computeBounds:i,leftTd:t,rightTd:e}:{computeBounds:i,leftTd:e,rightTd:t}}getTableAlignment(t){const e=t.getAttribute("align");if(!e){const{[_n.left]:e,[_n.right]:n}=B(t,[_n.left,_n.right]);return"auto"===e?"auto"===n?"center":"right":"left"}return e||"center"}getTdsFromMap(t){return Object.values(Object.fromEntries(t)).reduce(((t,e)=>t.length>e.length?t:e),[])}handleClick(t){const e=t.target.closest("table");if(this.prevList&&this.prevList.classList.add("ql-hidden"),this.prevTooltip&&this.prevTooltip.classList.remove("ql-table-tooltip-hidden"),this.prevList=null,this.prevTooltip=null,!e&&!this.tableBetter.cellSelection.selectedTds.length)return this.hideMenus(),void this.destroyTablePropertiesForm();this.tablePropertiesForm||(this.showMenus(),this.updateMenus(e),(e&&!e.isEqualNode(this.table)||this.scroll)&&this.updateScroll(!1),this.table=e)}hideMenus(){this.root.classList.add("ql-hidden")}insertColumn(t,n){const{left:r,right:i,width:s}=t.getBoundingClientRect(),o=e().find(t).table(),l=t.parentElement.lastChild.isEqualNode(t),a=n>0?i:r;o.insertColumn(a,l,s,n),this.quill.scrollSelectionIntoView()}insertParagraph(t){const n=e().find(this.table),r=this.quill.getIndex(n),i=t>0?n.length():0,o=(new(s())).retain(r+i).insert("\n");this.quill.updateContents(o,e().sources.USER),this.quill.setSelection(r+i,e().sources.SILENT),this.tableBetter.hideTools(),this.quill.scrollSelectionIntoView()}insertRow(t,n){const r=e().find(t),i=r.rowOffset(),s=r.table();if(n>0){const e=~~t.getAttribute("rowspan")||1;s.insertRow(i+n+e-1,n)}else s.insertRow(i+n,n);this.quill.scrollSelectionIntoView()}mergeCells(){var t,n;const{selectedTds:r}=this.tableBetter.cellSelection,{computeBounds:i,leftTd:s}=this.getSelectedTdsInfo(),o=e().find(s),[l,a]=E(o),c=o.children.head,u=o.table().tbody().children,h=o.row(),d=h.children.reduce(((t,e)=>{const n=j(e.domNode,this.quill.container);return n.left>=i.left&&n.right<=i.right&&(t+=~~e.domNode.getAttribute("colspan")||1),t}),0),f=u.reduce(((t,e)=>{const n=j(e.domNode,this.quill.container);if(n.top>=i.top&&n.bottom<=i.bottom){let n=Number.MAX_VALUE;e.children.forEach((t=>{const e=~~t.domNode.getAttribute("rowspan")||1;n=Math.min(n,e)})),t+=n}return t}),0);let p=0;for(const i of r){if(s.isEqualNode(i))continue;const r=e().find(i);r.moveChildren(o),r.remove(),(null===(n=null===(t=r.parent)||void 0===t?void 0:t.children)||void 0===n?void 0:n.length)||p++}p&&h.children.forEach((t=>{if(t.domNode.isEqualNode(s))return;const e=t.domNode.getAttribute("rowspan"),[n]=E(t);t.replaceWith(t.statics.blotName,Object.assign(Object.assign({},n),{rowspan:e-p}))})),o.setChildrenId(a),c.format(o.statics.blotName,Object.assign(Object.assign({},l),{colspan:d,rowspan:f-p})),this.tableBetter.cellSelection.setSelected(c.parent.domNode),this.quill.scrollSelectionIntoView()}setCellsMap(t,e){const n=t.domNode.getAttribute("data-row");e.has(n)?e.set(n,[...e.get(n),t.domNode]):e.set(n,[t.domNode])}showMenus(){this.root.classList.remove("ql-hidden")}splitCell(){const{selectedTds:t}=this.tableBetter.cellSelection,{leftTd:n}=this.getSelectedTdsInfo(),r=e().find(n).children.head;for(const n of t){const t=~~n.getAttribute("colspan")||1,r=~~n.getAttribute("rowspan")||1;if(1===t&&1===r)continue;const i=[],{width:s,right:o}=n.getBoundingClientRect(),l=e().find(n),a=l.table(),c=l.next,u=l.row();if(r>1)if(t>1){let e=u.next;for(let n=1;n<r;n++){const{ref:n,id:r}=this.getRefInfo(e,o);for(let s=0;s<t;s++)i.push([e,r,n]);e&&(e=e.next)}}else{let t=u.next;for(let e=1;e<r;e++){const{ref:e,id:n}=this.getRefInfo(t,o);i.push([t,n,e]),t&&(t=t.next)}}if(t>1){const e=n.getAttribute("data-row");for(let n=1;n<t;n++)i.push([u,e,c])}for(const[t,e,n]of i)a.insertColumnCell(t,e,n);const[h]=E(l);l.replaceWith(l.statics.blotName,Object.assign(Object.assign({},h),{width:~~(s/t),colspan:null,rowspan:null}))}this.tableBetter.cellSelection.setSelected(r.parent.domNode),this.quill.scrollSelectionIntoView()}toggleAttribute(t,e){this.prevList&&!this.prevList.isEqualNode(t)&&(this.prevList.classList.add("ql-hidden"),this.prevTooltip.classList.remove("ql-table-tooltip-hidden")),t&&(t.classList.toggle("ql-hidden"),e.classList.toggle("ql-table-tooltip-hidden"),this.prevList=t,this.prevTooltip=e)}updateMenus(t=this.table){t&&requestAnimationFrame((()=>{this.root.classList.remove("ql-table-triangle-none");const[e,n]=this.getCorrectBounds(t),{left:r,right:i,top:s,bottom:o}=e,{height:l,width:a}=this.root.getBoundingClientRect(),c=this.quill.getModule("toolbar"),u=getComputedStyle(c.container);let h=s-l-10,d=r+i-a>>1;h>-parseInt(u.paddingBottom)?(this.root.classList.add("ql-table-triangle-up"),this.root.classList.remove("ql-table-triangle-down")):(h=o>n.height?n.height+10:o+10,this.root.classList.add("ql-table-triangle-down"),this.root.classList.remove("ql-table-triangle-up")),d<n.left?(d=0,this.root.classList.add("ql-table-triangle-none")):d+a>n.right&&(d=n.right-a,this.root.classList.add("ql-table-triangle-none")),P(this.root,{left:`${d}px`,top:`${h}px`})}))}updateScroll(t){this.scroll=t}updateTable(t){this.table=t}};const Tn=e().import("blots/inline");e().import("ui/icons")["table-better"]=te;class qn extends Tn{}class Ln{constructor(){this.computeChildren=[],this.root=this.createContainer()}clearSelected(t){for(const e of t)e.classList&&e.classList.remove("ql-cell-selected");this.computeChildren=[],this.root&&this.setLabelContent(this.root.lastElementChild,null)}createContainer(){const t=document.createElement("div"),e=document.createElement("div"),n=document.createElement("div"),r=document.createDocumentFragment();for(let t=1;t<=10;t++)for(let e=1;e<=10;e++){const n=document.createElement("span");n.setAttribute("row",`${t}`),n.setAttribute("column",`${e}`),r.appendChild(n)}return n.innerHTML="0 x 0",t.classList.add("ql-table-select-container","ql-hidden"),e.classList.add("ql-table-select-list"),n.classList.add("ql-table-select-label"),e.appendChild(r),t.appendChild(e),t.appendChild(n),t.addEventListener("mousemove",(e=>this.handleMouseMove(e,t))),t}getComputeChildren(t,e){const n=[],{clientX:r,clientY:i}=e;for(const e of t){const{left:t,top:s}=e.getBoundingClientRect();r>=t&&i>=s&&n.push(e)}return n}getSelectAttrs(t){return[~~t.getAttribute("row"),~~t.getAttribute("column")]}handleClick(t,e){this.toggle(this.root);const n=t.target.closest("span[row]");if(n)this.insertTable(n,e);else{const t=this.computeChildren[this.computeChildren.length-1];t&&this.insertTable(t,e)}}handleMouseMove(t,e){const n=e.firstElementChild.children;this.clearSelected(this.computeChildren);const r=this.getComputeChildren(n,t);for(const t of r)t.classList&&t.classList.add("ql-cell-selected");this.computeChildren=r,this.setLabelContent(e.lastElementChild,r[r.length-1])}hide(t){this.clearSelected(this.computeChildren),t&&t.classList.add("ql-hidden")}insertTable(t,e){const[n,r]=this.getSelectAttrs(t);e(n,r),this.hide(this.root)}setLabelContent(t,e){if(e){const[n,r]=this.getSelectAttrs(e);t.innerHTML=`${n} x ${r}`}else t.innerHTML="0 x 0"}show(t){this.clearSelected(this.computeChildren),t&&t.classList.remove("ql-hidden")}toggle(t){this.clearSelected(this.computeChildren),t&&t.classList.toggle("ql-hidden")}}e().import("core/module");const Sn=e().import("blots/container"),On=e().import("modules/toolbar");class jn extends On{attach(t){let e=Array.from(t.classList).find((t=>0===t.indexOf("ql-")));if(!e)return;if(e=e.slice(3),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null==this.handlers[e]&&null==this.quill.scroll.query(e))return void console.warn("ignoring attaching to nonexistent format",e,t);const n="SELECT"===t.tagName?"change":"click";t.addEventListener(n,(n=>{var r;const{cellSelection:i}=this.getTableBetter();(null===(r=null==i?void 0:i.selectedTds)||void 0===r?void 0:r.length)>1?this.cellSelectionAttach(t,e,n,i):this.toolbarAttach(t,e,n)})),this.controls.push([e,t])}cellSelectionAttach(t,e,n,r){if("SELECT"===t.tagName){if(t.selectedIndex<0)return;const n=t.options[t.selectedIndex],i="string"!=typeof(null==n?void 0:n.value)||(null==n?void 0:n.value),s=r.getCorrectValue(e,i);r.setSelectedTdsFormat(e,s)}else{const i=(null==t?void 0:t.value)||!0,s=r.getCorrectValue(e,i);r.setSelectedTdsFormat(e,s),n.preventDefault()}}getTableBetter(){return this.quill.getModule("table-better")}setTableFormat(t,n,r,i,s){let o=null;const{cellSelection:l,tableMenus:a}=this.getTableBetter(),c=function(t,n,r){if(1===n.length){const i=function(t,e=0,n=Number.MAX_VALUE){const r=(t,e,n)=>{let i=[],s=n;return t.children.forEachAt(e,n,((t,e,n)=>{t instanceof Sn&&(i.push(t),i=i.concat(r(t,e,s))),s-=n})),i};return r(t,e,n)}(e().find(n[0]),t.index,t.length);return Bn(i)===Bn(r)}return!!(n.length>1)}(t,n,s);for(const t of s){const e=Mn(n,i,t,c);o=t.format(i,r,e)}if(n.length<2){if(c||1===s.length){const t=M(o);Promise.resolve().then((()=>{t&&this.quill.root.contains(t.domNode)?l.setSelected(t.domNode,!1):l.setSelected(n[0],!1)}))}else l.setSelected(n[0],!1);this.quill.setSelection(t,e().sources.SILENT)}return a.updateMenus(),o}toolbarAttach(t,n,r){let i;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;const e=t.options[t.selectedIndex];i=!e.hasAttribute("selected")&&(e.value||!1)}else i=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),r.preventDefault();this.quill.focus();const[o]=this.quill.selection.getRange();if(null!=this.handlers[n])this.handlers[n].call(this,i);else if(this.quill.scroll.query(n).prototype instanceof zt){if(i=prompt(`Enter ${n}`),!i)return;this.quill.updateContents((new(s())).retain(o.index).delete(o.length).insert({[n]:i}),e().sources.USER)}else this.quill.format(n,i,e().sources.USER);this.update(o)}}function Mn(t,e,n,r){return 1===t.length&&"list"===e&&n.statics.blotName===x.blotName||r}function Bn(t){return t.reduce(((t,e)=>t+e.length()),0)}function Rn(t,e,n,r){const i=this.quill.getSelection();if(!r)if(i.length||1!==e.length)r=this.quill.getLines(i);else{const[t]=this.quill.getLine(i.index);r=[t]}return this.setTableFormat(i,e,t,n,r)}jn.DEFAULTS=nt()({},On.DEFAULTS,{handlers:{header(t,n){const{cellSelection:r}=this.getTableBetter(),i=null==r?void 0:r.selectedTds;if(null==i?void 0:i.length)return Rn.call(this,t,i,"header",n);this.quill.format("header",t,e().sources.USER)},list(t,n){const{cellSelection:r}=this.getTableBetter(),i=null==r?void 0:r.selectedTds;if(null==i?void 0:i.length){if(1===i.length){const e=this.quill.getSelection(!0),n=this.quill.getFormat(e);t=r.getListCorrectValue("list",t,n)}return Rn.call(this,t,i,"list",n)}const s=this.quill.getSelection(!0),o=this.quill.getFormat(s);"check"===t?"checked"===o.list||"unchecked"===o.list?this.quill.format("list",!1,e().sources.USER):this.quill.format("list","unchecked",e().sources.USER):this.quill.format("list",t,e().sources.USER)},"table-better"(){}}});var In=jn;const Dn=["error","warn","log","info"];let Pn="warn";function Un(t){if(Pn&&Dn.indexOf(t)<=Dn.indexOf(Pn)){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];console[t](...n)}}function zn(t){return Dn.reduce(((e,n)=>(e[n]=Un.bind(console,n,t),e)),{})}zn.level=t=>{Pn=t},Un.level=zn.level;var Hn=zn;e().import("core/module");const Fn=e().import("modules/clipboard"),$n=Hn("quill:clipboard");var Vn=class extends Fn{onPaste(t,{text:n,html:r}){const i=this.quill.getFormat(t.index),o=this.getTableDelta({text:n,html:r},i);$n.log("onPaste",o,{text:n,html:r});const l=(new(s())).retain(t.index).delete(t.length).concat(o);this.quill.updateContents(l,e().sources.USER),this.quill.setSelection(l.length()-t.length,e().sources.SILENT),this.quill.scrollSelectionIntoView()}getTableDelta({html:t,text:e},n){var r,i,o;const l=this.convert({text:e,html:t},n);if(n[V.blotName])for(const t of l.ops){if((null==t?void 0:t.attributes)&&(t.attributes[Z.blotName]||t.attributes[V.blotName]))return new(s());((null===(r=null==t?void 0:t.attributes)||void 0===r?void 0:r.header)||(null===(i=null==t?void 0:t.attributes)||void 0===i?void 0:i.list)||!(null===(o=null==t?void 0:t.attributes)||void 0===o?void 0:o[V.blotName]))&&(t.attributes=Object.assign(Object.assign({},t.attributes),n))}return l}};const Wn=e().import("core/module");class Kn extends Wn{constructor(t,e){super(t,e),t.clipboard.addMatcher("td, th",ot),t.clipboard.addMatcher("tr",st),t.clipboard.addMatcher("col",lt),t.clipboard.addMatcher("table",at),this.language=new bt(null==e?void 0:e.language),this.cellSelection=new Qt(t,this),this.operateLine=new Jt(t,this),this.tableMenus=new Cn(t,this),this.tableSelect=new Ln,t.root.addEventListener("keyup",this.handleKeyup.bind(this)),t.root.addEventListener("mousedown",this.handleMousedown.bind(this)),t.root.addEventListener("scroll",this.handleScroll.bind(this)),this.registerToolbarTable(null==e?void 0:e.toolbarTable)}static register(){e().register(V,!0),e().register(W,!0),e().register(K,!0),e().register(G,!0),e().register(Z,!0),e().register(Q,!0),e().register(Y,!0),e().register(X,!0),e().register({"modules/toolbar":In,"modules/clipboard":Vn},!0)}clearHistorySelected(){const[t]=this.getTable();if(!t)return;const e=Array.from(t.domNode.querySelectorAll("td.ql-cell-focused, td.ql-cell-selected"));for(const t of e)t.classList&&t.classList.remove("ql-cell-focused","ql-cell-selected")}deleteTable(){const[t]=this.getTable();if(null==t)return;const n=t.offset();t.remove(),this.hideTools(),this.quill.update(e().sources.USER),this.quill.setSelection(n,e().sources.SILENT)}deleteTableTemporary(t=e().sources.API){const n=this.quill.scroll.descendants(Z);for(const t of n)t.remove();this.hideTools(),this.quill.update(t)}getTable(t=this.quill.getSelection()){if(null==t)return[null,null,null,-1];const[e,n]=this.quill.getLine(t.index);if(null==e||e.statics.blotName!==V.blotName)return[null,null,null,-1];const r=e.parent,i=r.parent;return[i.parent.parent,i,r,n]}handleKeyup(t){this.cellSelection.handleKeyup(t),!t.ctrlKey||"z"!==t.key&&"y"!==t.key||(this.hideTools(),this.clearHistorySelected()),this.updateMenus(t)}handleMousedown(t){var e;if(null===(e=this.tableSelect)||void 0===e||e.hide(this.tableSelect.root),!t.target.closest("table"))return this.hideTools();this.cellSelection.handleMousedown(t),this.cellSelection.setDisabled(!0)}handleScroll(){var t;this.hideTools(),null===(t=this.tableMenus)||void 0===t||t.updateScroll(!0)}hideTools(){var t,e,n,r,i,s,o;null===(t=this.cellSelection)||void 0===t||t.clearSelected(),null===(e=this.cellSelection)||void 0===e||e.setDisabled(!1),null===(n=this.operateLine)||void 0===n||n.hideDragBlock(),null===(r=this.operateLine)||void 0===r||r.hideDragTable(),null===(i=this.operateLine)||void 0===i||i.hideLine(),null===(s=this.tableMenus)||void 0===s||s.hideMenus(),null===(o=this.tableMenus)||void 0===o||o.destroyTablePropertiesForm()}insertTable(t,n){const r=this.quill.getSelection(!0);if(null==r)return;if(this.isTable(r))return;const i=`width: ${72*n}px`,o=this.quill.getFormat(r.index-1),[,l]=this.quill.getLine(r.index),a=!!o[V.blotName]||0!==l,c=a?2:1,u=a?(new(s())).insert("\n"):new(s()),h=(new(s())).retain(r.index).delete(r.length).concat(u).insert("\n",{[Z.blotName]:{style:i}}),d=new Array(t).fill(0).reduce((t=>{const e=tt();return new Array(n).fill("\n").reduce(((t,n)=>t.insert(n,{[V.blotName]:J(),[W.blotName]:{"data-row":e,width:"72"}})),t)}),h);this.quill.updateContents(d,e().sources.USER),this.quill.setSelection(r.index+c,e().sources.SILENT),this.showTools()}isTable(t){return!!this.quill.getFormat(t.index)[V.blotName]}registerToolbarTable(t){if(!t)return;e().register({"formats/table-better":qn},!0);const n=this.quill.getModule("toolbar").container.querySelector("button.ql-table-better");n&&this.tableSelect.root&&(n.appendChild(this.tableSelect.root),n.addEventListener("click",(t=>{this.tableSelect.handleClick(t,this.insertTable.bind(this))})),document.addEventListener("click",(t=>{t.composedPath().includes(n)||this.tableSelect.root.classList.contains("ql-hidden")||this.tableSelect.hide(this.tableSelect.root)})))}showTools(t){const[e,,n]=this.getTable();e&&n&&(this.cellSelection.setDisabled(!0),this.cellSelection.setSelected(n.domNode,t),this.tableMenus.showMenus(),this.tableMenus.updateMenus(e.domNode),this.tableMenus.updateTable(e.domNode))}updateMenus(t){this.cellSelection.selectedTds.length&&("Enter"===t.key||t.ctrlKey&&"v"===t.key)&&this.tableMenus.updateMenus()}}const Gn={"table-cell down":Yn(!1),"table-cell up":Yn(!0),"table-cell-block backspace":Zn("Backspace"),"table-cell-block delete":Zn("Delete"),"table-header backspace":Xn("Backspace"),"table-header delete":Xn("Delete"),"table-header enter":{key:"Enter",collapsed:!0,format:["table-header"],suffix:/^$/,handler(t,n){const[r,i]=this.quill.getLine(t.index),o=(new(s())).retain(t.index).insert("\n",n.format).retain(r.length()-i-1).retain(1,{header:null});this.quill.updateContents(o,e().sources.USER),this.quill.setSelection(t.index+1,e().sources.SILENT),this.quill.scrollSelectionIntoView()}},"table-list backspace":Qn("Backspace"),"table-list delete":Qn("Delete"),"table-list empty enter":{key:"Enter",collapsed:!0,format:["table-list"],empty:!0,handler(t,e){const{line:n}=e,{cellId:r}=n.parent.formats()[n.parent.statics.blotName],i=n.replaceWith(V.blotName,r),s=this.quill.getModule("table-better"),o=M(i);o&&s.cellSelection.setSelected(o.domNode,!1)}}};function Zn(t){return{key:t,format:["table-cell-block"],collapsed:!0,handler(e,n){var r;const[i]=this.quill.getLine(e.index),{offset:s,suffix:o}=n;if(0===s&&!i.prev)return!1;const l=null===(r=i.prev)||void 0===r?void 0:r.statics.blotName;return 0!==s||l!==m.blotName&&l!==V.blotName&&l!==x.blotName?!(0!==s&&!o&&"Delete"===t):Jn.call(this,i,e)}}}function Yn(t){return{key:t?"ArrowUp":"ArrowDown",collapsed:!0,format:["table-cell"],handler:()=>!1}}function Xn(t){return{key:t,format:["table-header"],collapsed:!0,empty:!0,handler(t,e){const[n]=this.quill.getLine(t.index);if(n.prev)return Jn.call(this,n,t);{const t=C(n.formats()[n.statics.blotName]);n.replaceWith(V.blotName,t)}}}}function Qn(t){return{key:t,format:["table-list"],collapsed:!0,empty:!0,handler(t,e){const[n]=this.quill.getLine(t.index),r=C(n.parent.formats()[n.parent.statics.blotName]);n.replaceWith(V.blotName,r)}}}function Jn(t,n){const r=this.quill.getModule("table-better");return t.remove(),null==r||r.tableMenus.updateMenus(),this.quill.setSelection(n.index-1,e().sources.SILENT),!1}Kn.keyboardBindings=Gn;var tr=Kn}(),i.default}())},697:t=>{"use strict";var e=Object.prototype.hasOwnProperty,n="~";function r(){}function i(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function s(t,e,r,s,o){if("function"!=typeof r)throw new TypeError("The listener must be a function");var l=new i(r,s||t,o),a=n?n+e:e;return t._events[a]?t._events[a].fn?t._events[a]=[t._events[a],l]:t._events[a].push(l):(t._events[a]=l,t._eventsCount++),t}function o(t,e){0==--t._eventsCount?t._events=new r:delete t._events[e]}function l(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),l.prototype.eventNames=function(){var t,r,i=[];if(0===this._eventsCount)return i;for(r in t=this._events)e.call(t,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},l.prototype.listeners=function(t){var e=n?n+t:t,r=this._events[e];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,s=r.length,o=new Array(s);i<s;i++)o[i]=r[i].fn;return o},l.prototype.listenerCount=function(t){var e=n?n+t:t,r=this._events[e];return r?r.fn?1:r.length:0},l.prototype.emit=function(t,e,r,i,s,o){var l=n?n+t:t;if(!this._events[l])return!1;var a,c,u=this._events[l],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(t,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,e),!0;case 3:return u.fn.call(u.context,e,r),!0;case 4:return u.fn.call(u.context,e,r,i),!0;case 5:return u.fn.call(u.context,e,r,i,s),!0;case 6:return u.fn.call(u.context,e,r,i,s,o),!0}for(c=1,a=new Array(h-1);c<h;c++)a[c-1]=arguments[c];u.fn.apply(u.context,a)}else{var d,f=u.length;for(c=0;c<f;c++)switch(u[c].once&&this.removeListener(t,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,e);break;case 3:u[c].fn.call(u[c].context,e,r);break;case 4:u[c].fn.call(u[c].context,e,r,i);break;default:if(!a)for(d=1,a=new Array(h-1);d<h;d++)a[d-1]=arguments[d];u[c].fn.apply(u[c].context,a)}}return!0},l.prototype.on=function(t,e,n){return s(this,t,e,n,!1)},l.prototype.once=function(t,e,n){return s(this,t,e,n,!0)},l.prototype.removeListener=function(t,e,r,i){var s=n?n+t:t;if(!this._events[s])return this;if(!e)return o(this,s),this;var l=this._events[s];if(l.fn)l.fn!==e||i&&!l.once||r&&l.context!==r||o(this,s);else{for(var a=0,c=[],u=l.length;a<u;a++)(l[a].fn!==e||i&&!l[a].once||r&&l[a].context!==r)&&c.push(l[a]);c.length?this._events[s]=1===c.length?c[0]:c:o(this,s)}return this},l.prototype.removeAllListeners=function(t){var e;return t?(e=n?n+t:t,this._events[e]&&o(this,e)):(this._events=new r,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=n,l.EventEmitter=l,t.exports=l},371:(t,e,n)=>{var r=n(74);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[t.id,r,""]]),r.locals&&(t.exports=r.locals),(0,n(534).A)("1c500fa1",r,!1,{})},578:(t,e,n)=>{var r=n(625);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[t.id,r,""]]),r.locals&&(t.exports=r.locals),(0,n(534).A)("0816b7e7",r,!1,{})},391:(t,e,n)=>{var r=n(976);r.__esModule&&(r=r.default),"string"==typeof r&&(r=[[t.id,r,""]]),r.locals&&(t.exports=r.locals),(0,n(534).A)("5a60a0a0",r,!1,{})},534:(t,e,n)=>{"use strict";function r(t,e){for(var n=[],r={},i=0;i<e.length;i++){var s=e[i],o=s[0],l={id:t+":"+i,css:s[1],media:s[2],sourceMap:s[3]};r[o]?r[o].parts.push(l):n.push(r[o]={id:o,parts:[l]})}return n}n.d(e,{A:()=>p});var i="undefined"!=typeof document;if("undefined"!=typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var s={},o=i&&(document.head||document.getElementsByTagName("head")[0]),l=null,a=0,c=!1,u=function(){},h=null,d="data-vue-ssr-id",f="undefined"!=typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function p(t,e,n,i){c=n,h=i||{};var o=r(t,e);return g(o),function(e){for(var n=[],i=0;i<o.length;i++){var l=o[i];(a=s[l.id]).refs--,n.push(a)}for(e?g(o=r(t,e)):o=[],i=0;i<n.length;i++){var a;if(0===(a=n[i]).refs){for(var c=0;c<a.parts.length;c++)a.parts[c]();delete s[a.id]}}}}function g(t){for(var e=0;e<t.length;e++){var n=t[e],r=s[n.id];if(r){r.refs++;for(var i=0;i<r.parts.length;i++)r.parts[i](n.parts[i]);for(;i<n.parts.length;i++)r.parts.push(m(n.parts[i]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var o=[];for(i=0;i<n.parts.length;i++)o.push(m(n.parts[i]));s[n.id]={id:n.id,refs:1,parts:o}}}}function b(){var t=document.createElement("style");return t.type="text/css",o.appendChild(t),t}function m(t){var e,n,r=document.querySelector("style["+d+'~="'+t.id+'"]');if(r){if(c)return u;r.parentNode.removeChild(r)}if(f){var i=a++;r=l||(l=b()),e=w.bind(null,r,i,!1),n=w.bind(null,r,i,!0)}else r=b(),e=x.bind(null,r),n=function(){r.parentNode.removeChild(r)};return e(t),function(r){if(r){if(r.css===t.css&&r.media===t.media&&r.sourceMap===t.sourceMap)return;e(t=r)}else n()}}var v,y=(v=[],function(t,e){return v[t]=e,v.filter(Boolean).join("\n")});function w(t,e,n,r){var i=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=y(e,i);else{var s=document.createTextNode(i),o=t.childNodes;o[e]&&t.removeChild(o[e]),o.length?t.insertBefore(s,o[e]):t.appendChild(s)}}function x(t,e){var n=e.css,r=e.media,i=e.sourceMap;if(r&&t.setAttribute("media",r),h.ssrId&&t.setAttribute(d,e.id),i&&(n+="\n/*# sourceURL="+i.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}},690:(t,e,n)=>{"use strict";n.r(e),n.d(e,{AttributeMap:()=>De.AttributeMap,Delta:()=>De,Module:()=>_r,Op:()=>De.Op,OpIterator:()=>De.OpIterator,Parchment:()=>r,Range:()=>pr,default:()=>zs});var r={};n.r(r),n.d(r,{Attributor:()=>ce,AttributorStore:()=>me,BlockBlot:()=>qe,ClassAttributor:()=>pe,ContainerBlot:()=>Se,EmbedBlot:()=>Oe,InlineBlot:()=>Ce,LeafBlot:()=>xe,ParentBlot:()=>_e,Registry:()=>de,Scope:()=>ae,ScrollBlot:()=>Be,StyleAttributor:()=>be,TextBlot:()=>Ie});const i=function(t,e){return t===e||t!=t&&e!=e},s=function(t,e){for(var n=t.length;n--;)if(i(t[n][0],e))return n;return-1};var o=Array.prototype.splice;function l(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}l.prototype.clear=function(){this.__data__=[],this.size=0},l.prototype.delete=function(t){var e=this.__data__,n=s(e,t);return!(n<0||(n==e.length-1?e.pop():o.call(e,n,1),--this.size,0))},l.prototype.get=function(t){var e=this.__data__,n=s(e,t);return n<0?void 0:e[n][1]},l.prototype.has=function(t){return s(this.__data__,t)>-1},l.prototype.set=function(t,e){var n=this.__data__,r=s(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this};const a=l,c="object"==typeof global&&global&&global.Object===Object&&global;var u="object"==typeof self&&self&&self.Object===Object&&self;const h=c||u||Function("return this")(),d=h.Symbol;var f=Object.prototype,p=f.hasOwnProperty,g=f.toString,b=d?d.toStringTag:void 0;var m=Object.prototype.toString;var v=d?d.toStringTag:void 0;const y=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":v&&v in Object(t)?function(t){var e=p.call(t,b),n=t[b];try{t[b]=void 0;var r=!0}catch(t){}var i=g.call(t);return r&&(e?t[b]=n:delete t[b]),i}(t):function(t){return m.call(t)}(t)},w=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)},x=function(t){if(!w(t))return!1;var e=y(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e},N=h["__core-js_shared__"];var k,A=(k=/[^.]+$/.exec(N&&N.keys&&N.keys.IE_PROTO||""))?"Symbol(src)_1."+k:"";var _=Function.prototype.toString;const E=function(t){if(null!=t){try{return _.call(t)}catch(t){}try{return t+""}catch(t){}}return""};var C=/^\[object .+?Constructor\]$/,T=Function.prototype,q=Object.prototype,L=T.toString,S=q.hasOwnProperty,O=RegExp("^"+L.call(S).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const j=function(t){return!(!w(t)||(e=t,A&&A in e))&&(x(t)?O:C).test(E(t));var e},M=function(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return j(n)?n:void 0},B=M(h,"Map"),R=M(Object,"create");var I=Object.prototype.hasOwnProperty;var D=Object.prototype.hasOwnProperty;function P(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}P.prototype.clear=function(){this.__data__=R?R(null):{},this.size=0},P.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},P.prototype.get=function(t){var e=this.__data__;if(R){var n=e[t];return"__lodash_hash_undefined__"===n?void 0:n}return I.call(e,t)?e[t]:void 0},P.prototype.has=function(t){var e=this.__data__;return R?void 0!==e[t]:D.call(e,t)},P.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=R&&void 0===e?"__lodash_hash_undefined__":e,this};const U=P,z=function(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map};function H(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}H.prototype.clear=function(){this.size=0,this.__data__={hash:new U,map:new(B||a),string:new U}},H.prototype.delete=function(t){var e=z(this,t).delete(t);return this.size-=e?1:0,e},H.prototype.get=function(t){return z(this,t).get(t)},H.prototype.has=function(t){return z(this,t).has(t)},H.prototype.set=function(t,e){var n=z(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this};const F=H;function $(t){var e=this.__data__=new a(t);this.size=e.size}$.prototype.clear=function(){this.__data__=new a,this.size=0},$.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},$.prototype.get=function(t){return this.__data__.get(t)},$.prototype.has=function(t){return this.__data__.has(t)},$.prototype.set=function(t,e){var n=this.__data__;if(n instanceof a){var r=n.__data__;if(!B||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new F(r)}return n.set(t,e),this.size=n.size,this};const V=$,W=function(){try{var t=M(Object,"defineProperty");return t({},"",{}),t}catch(t){}}(),K=function(t,e,n){"__proto__"==e&&W?W(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n},G=function(t,e,n){(void 0!==n&&!i(t[e],n)||void 0===n&&!(e in t))&&K(t,e,n)},Z=function(t,e,n){for(var r=-1,i=Object(t),s=n(t),o=s.length;o--;){var l=s[++r];if(!1===e(i[l],l,i))break}return t};var Y="object"==typeof exports&&exports&&!exports.nodeType&&exports,X=Y&&"object"==typeof module&&module&&!module.nodeType&&module,Q=X&&X.exports===Y?h.Buffer:void 0,J=Q?Q.allocUnsafe:void 0;const tt=function(t,e){if(e)return t.slice();var n=t.length,r=J?J(n):new t.constructor(n);return t.copy(r),r},et=h.Uint8Array,nt=function(t){var e=new t.constructor(t.byteLength);return new et(e).set(new et(t)),e},rt=function(t,e){var n=e?nt(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.length)},it=function(t,e){var n=-1,r=t.length;for(e||(e=Array(r));++n<r;)e[n]=t[n];return e};var st=Object.create;const ot=function(){function t(){}return function(e){if(!w(e))return{};if(st)return st(e);t.prototype=e;var n=new t;return t.prototype=void 0,n}}(),lt=function(t,e){return function(n){return t(e(n))}},at=lt(Object.getPrototypeOf,Object);var ct=Object.prototype;const ut=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||ct)},ht=function(t){return"function"!=typeof t.constructor||ut(t)?{}:ot(at(t))},dt=function(t){return null!=t&&"object"==typeof t},ft=function(t){return dt(t)&&"[object Arguments]"==y(t)};var pt=Object.prototype,gt=pt.hasOwnProperty,bt=pt.propertyIsEnumerable;const mt=ft(function(){return arguments}())?ft:function(t){return dt(t)&&gt.call(t,"callee")&&!bt.call(t,"callee")},vt=Array.isArray,yt=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991},wt=function(t){return null!=t&&yt(t.length)&&!x(t)};var xt="object"==typeof exports&&exports&&!exports.nodeType&&exports,Nt=xt&&"object"==typeof module&&module&&!module.nodeType&&module,kt=Nt&&Nt.exports===xt?h.Buffer:void 0;const At=(kt?kt.isBuffer:void 0)||function(){return!1};var _t=Function.prototype,Et=Object.prototype,Ct=_t.toString,Tt=Et.hasOwnProperty,qt=Ct.call(Object);var Lt={};Lt["[object Float32Array]"]=Lt["[object Float64Array]"]=Lt["[object Int8Array]"]=Lt["[object Int16Array]"]=Lt["[object Int32Array]"]=Lt["[object Uint8Array]"]=Lt["[object Uint8ClampedArray]"]=Lt["[object Uint16Array]"]=Lt["[object Uint32Array]"]=!0,Lt["[object Arguments]"]=Lt["[object Array]"]=Lt["[object ArrayBuffer]"]=Lt["[object Boolean]"]=Lt["[object DataView]"]=Lt["[object Date]"]=Lt["[object Error]"]=Lt["[object Function]"]=Lt["[object Map]"]=Lt["[object Number]"]=Lt["[object Object]"]=Lt["[object RegExp]"]=Lt["[object Set]"]=Lt["[object String]"]=Lt["[object WeakMap]"]=!1;const St=function(t){return function(e){return t(e)}};var Ot="object"==typeof exports&&exports&&!exports.nodeType&&exports,jt=Ot&&"object"==typeof module&&module&&!module.nodeType&&module,Mt=jt&&jt.exports===Ot&&c.process;const Bt=function(){try{return jt&&jt.require&&jt.require("util").types||Mt&&Mt.binding&&Mt.binding("util")}catch(t){}}();var Rt=Bt&&Bt.isTypedArray;const It=Rt?St(Rt):function(t){return dt(t)&&yt(t.length)&&!!Lt[y(t)]},Dt=function(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]};var Pt=Object.prototype.hasOwnProperty;const Ut=function(t,e,n){var r=t[e];Pt.call(t,e)&&i(r,n)&&(void 0!==n||e in t)||K(t,e,n)},zt=function(t,e,n,r){var i=!n;n||(n={});for(var s=-1,o=e.length;++s<o;){var l=e[s],a=r?r(n[l],t[l],l,n,t):void 0;void 0===a&&(a=t[l]),i?K(n,l,a):Ut(n,l,a)}return n};var Ht=/^(?:0|[1-9]\d*)$/;const Ft=function(t,e){var n=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==n||"symbol"!=n&&Ht.test(t))&&t>-1&&t%1==0&&t<e};var $t=Object.prototype.hasOwnProperty;const Vt=function(t,e){var n=vt(t),r=!n&&mt(t),i=!n&&!r&&At(t),s=!n&&!r&&!i&&It(t),o=n||r||i||s,l=o?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],a=l.length;for(var c in t)!e&&!$t.call(t,c)||o&&("length"==c||i&&("offset"==c||"parent"==c)||s&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Ft(c,a))||l.push(c);return l};var Wt=Object.prototype.hasOwnProperty;const Kt=function(t){if(!w(t))return function(t){var e=[];if(null!=t)for(var n in Object(t))e.push(n);return e}(t);var e=ut(t),n=[];for(var r in t)("constructor"!=r||!e&&Wt.call(t,r))&&n.push(r);return n},Gt=function(t){return wt(t)?Vt(t,!0):Kt(t)},Zt=function(t,e,n,r,i,s,o){var l,a=Dt(t,n),c=Dt(e,n),u=o.get(c);if(u)G(t,n,u);else{var h=s?s(a,c,n+"",t,e,o):void 0,d=void 0===h;if(d){var f=vt(c),p=!f&&At(c),g=!f&&!p&&It(c);h=c,f||p||g?vt(a)?h=a:dt(l=a)&&wt(l)?h=it(a):p?(d=!1,h=tt(c,!0)):g?(d=!1,h=rt(c,!0)):h=[]:function(t){if(!dt(t)||"[object Object]"!=y(t))return!1;var e=at(t);if(null===e)return!0;var n=Tt.call(e,"constructor")&&e.constructor;return"function"==typeof n&&n instanceof n&&Ct.call(n)==qt}(c)||mt(c)?(h=a,mt(a)?h=function(t){return zt(t,Gt(t))}(a):w(a)&&!x(a)||(h=ht(c))):d=!1}d&&(o.set(c,h),i(h,c,r,s,o),o.delete(c)),G(t,n,h)}},Yt=function t(e,n,r,i,s){e!==n&&Z(n,(function(o,l){if(s||(s=new V),w(o))Zt(e,n,l,r,t,i,s);else{var a=i?i(Dt(e,l),o,l+"",e,n,s):void 0;void 0===a&&(a=o),G(e,l,a)}}),Gt)},Xt=function(t){return t};var Qt=Math.max;const Jt=W?function(t,e){return W(t,"toString",{configurable:!0,enumerable:!1,value:(n=e,function(){return n}),writable:!0});var n}:Xt;var te=Date.now;const ee=(ne=Jt,re=0,ie=0,function(){var t=te(),e=16-(t-ie);if(ie=t,e>0){if(++re>=800)return arguments[0]}else re=0;return ne.apply(void 0,arguments)});var ne,re,ie;const se=function(t,e){return ee(function(t,e,n){return e=Qt(void 0===e?t.length-1:e,0),function(){for(var r=arguments,i=-1,s=Qt(r.length-e,0),o=Array(s);++i<s;)o[i]=r[e+i];i=-1;for(var l=Array(e+1);++i<e;)l[i]=r[i];return l[e]=n(o),function(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}(t,this,l)}}(t,e,Xt),t+"")},oe=(le=function(t,e,n){Yt(t,e,n)},se((function(t,e){var n=-1,r=e.length,s=r>1?e[r-1]:void 0,o=r>2?e[2]:void 0;for(s=le.length>3&&"function"==typeof s?(r--,s):void 0,o&&function(t,e,n){if(!w(n))return!1;var r=typeof e;return!!("number"==r?wt(n)&&Ft(e,n.length):"string"==r&&e in n)&&i(n[e],t)}(e[0],e[1],o)&&(s=r<3?void 0:s,r=1),t=Object(t);++n<r;){var l=e[n];l&&le(t,l,n)}return t})));var le,ae=(t=>(t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY",t))(ae||{});class ce{constructor(t,e,n={}){this.attrName=t,this.keyName=e;const r=ae.TYPE&ae.ATTRIBUTE;this.scope=null!=n.scope?n.scope&ae.LEVEL|r:ae.ATTRIBUTE,null!=n.whitelist&&(this.whitelist=n.whitelist)}static keys(t){return Array.from(t.attributes).map((t=>t.name))}add(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)}canAdd(t,e){return null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1)}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class ue extends Error{constructor(t){super(t="[Parchment] "+t),this.message=t,this.name=this.constructor.name}}const he=class t{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(null==t)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let n=null;try{n=t.parentNode}catch{return null}return this.find(n,e)}return null}create(e,n,r){const i=this.query(n);if(null==i)throw new ue(`Unable to create ${n} blot`);const s=i,o=n instanceof Node||n.nodeType===Node.TEXT_NODE?n:s.create(r),l=new s(e,o,r);return t.blots.set(l.domNode,l),l}find(e,n=!1){return t.find(e,n)}query(t,e=ae.ANY){let n;return"string"==typeof t?n=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?n=this.types.text:"number"==typeof t?t&ae.LEVEL&ae.BLOCK?n=this.types.block:t&ae.LEVEL&ae.INLINE&&(n=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some((t=>(n=this.classes[t],!!n))),n=n||this.tags[t.tagName]),null==n?null:"scope"in n&&e&ae.LEVEL&n.scope&&e&ae.TYPE&n.scope?n:null}register(...t){return t.map((t=>{const e="blotName"in t,n="attrName"in t;if(!e&&!n)throw new ue("Invalid definition");if(e&&"abstract"===t.blotName)throw new ue("Cannot register abstract class");const r=e?t.blotName:n?t.attrName:void 0;return this.types[r]=t,n?"string"==typeof t.keyName&&(this.attributes[t.keyName]=t):e&&(t.className&&(this.classes[t.className]=t),t.tagName&&(Array.isArray(t.tagName)?t.tagName=t.tagName.map((t=>t.toUpperCase())):t.tagName=t.tagName.toUpperCase(),(Array.isArray(t.tagName)?t.tagName:[t.tagName]).forEach((e=>{(null==this.tags[e]||null==t.className)&&(this.tags[e]=t)})))),t}))}};he.blots=new WeakMap;let de=he;function fe(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter((t=>0===t.indexOf(`${e}-`)))}const pe=class extends ce{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map((t=>t.split("-").slice(0,-1).join("-")))}add(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0)}remove(t){fe(t,this.keyName).forEach((e=>{t.classList.remove(e)})),0===t.classList.length&&t.removeAttribute("class")}value(t){const e=(fe(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}};function ge(t){const e=t.split("-"),n=e.slice(1).map((t=>t[0].toUpperCase()+t.slice(1))).join("");return e[0]+n}const be=class extends ce{static keys(t){return(t.getAttribute("style")||"").split(";").map((t=>t.split(":")[0].trim()))}add(t,e){return!!this.canAdd(t,e)&&(t.style[ge(this.keyName)]=e,!0)}remove(t){t.style[ge(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){const e=t.style[ge(this.keyName)];return this.canAdd(t,e)?e:""}},me=class{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=de.find(this.domNode);if(null==t)return;const e=ce.keys(this.domNode),n=pe.keys(this.domNode),r=be.keys(this.domNode);e.concat(n).concat(r).forEach((e=>{const n=t.scroll.query(e,ae.ATTRIBUTE);n instanceof ce&&(this.attributes[n.attrName]=n)}))}copy(t){Object.keys(this.attributes).forEach((e=>{const n=this.attributes[e].value(this.domNode);t.format(e,n)}))}move(t){this.copy(t),Object.keys(this.attributes).forEach((t=>{this.attributes[t].remove(this.domNode)})),this.attributes={}}values(){return Object.keys(this.attributes).reduce(((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t)),{})}},ve=class{constructor(t,e){this.scroll=t,this.domNode=e,de.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(null==this.tagName)throw new ue("Blot definition missing tagName");let e,n;return Array.isArray(this.tagName)?("string"==typeof t?(n=t.toUpperCase(),parseInt(n,10).toString()===n&&(n=parseInt(n,10))):"number"==typeof t&&(n=t),e="number"==typeof n?document.createElement(this.tagName[n-1]):n&&this.tagName.indexOf(n)>-1?document.createElement(n):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){null!=this.parent&&this.parent.removeChild(this),de.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,n,r){const i=this.isolate(t,e);if(null!=this.scroll.query(n,ae.BLOT)&&r)i.wrap(n,r);else if(null!=this.scroll.query(n,ae.ATTRIBUTE)){const t=this.scroll.create(this.statics.scope);i.wrap(t),t.format(n,r)}}insertAt(t,e,n){const r=null==n?this.scroll.create("text",e):this.scroll.create(e,n),i=this.split(t);this.parent.insertBefore(r,i||void 0)}isolate(t,e){const n=this.split(t);if(null==n)throw new Error("Attempt to isolate at end");return n.split(e),n}length(){return 1}offset(t=this.parent){return null==this.parent||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const n="string"==typeof t?this.scroll.create(t,e):t;return null!=this.parent&&(this.parent.insertBefore(n,this.next||void 0),this.remove()),n}split(t,e){return 0===t?this:this.next}update(t,e){}wrap(t,e){const n="string"==typeof t?this.scroll.create(t,e):t;if(null!=this.parent&&this.parent.insertBefore(n,this.next||void 0),"function"!=typeof n.appendChild)throw new ue(`Cannot wrap ${t}`);return n.appendChild(this),n}};ve.blotName="abstract";let ye=ve;const we=class extends ye{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let n=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(n+=1),[this.parent.domNode,n]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};we.scope=ae.INLINE_BLOT;const xe=we;class Ne{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let n=e();for(;n&&t>0;)t-=1,n=e();return n}contains(t){const e=this.iterator();let n=e();for(;n;){if(n===t)return!0;n=e()}return!1}indexOf(t){const e=this.iterator();let n=e(),r=0;for(;n;){if(n===t)return r;r+=1,n=e()}return-1}insertBefore(t,e){null!=t&&(this.remove(t),t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,n=this.head;for(;null!=n;){if(n===t)return e;e+=n.length(),n=n.next}return-1}remove(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return null!=t&&(t=t.next),e}}find(t,e=!1){const n=this.iterator();let r=n();for(;r;){const i=r.length();if(t<i||e&&t===i&&(null==r.next||0!==r.next.length()))return[r,t];t-=i,r=n()}return[null,0]}forEach(t){const e=this.iterator();let n=e();for(;n;)t(n),n=e()}forEachAt(t,e,n){if(e<=0)return;const[r,i]=this.find(t);let s=t-i;const o=this.iterator(r);let l=o();for(;l&&s<t+e;){const r=l.length();t>s?n(l,t-s,Math.min(e,s+r-t)):n(l,0,Math.min(r,t+e-s)),s+=r,l=o()}}map(t){return this.reduce(((e,n)=>(e.push(t(n)),e)),[])}reduce(t,e){const n=this.iterator();let r=n();for(;r;)e=t(e,r),r=n();return e}}function ke(t,e){const n=e.find(t);if(n)return n;try{return e.create(t)}catch{const n=e.create(ae.INLINE);return Array.from(t.childNodes).forEach((t=>{n.domNode.appendChild(t)})),t.parentNode&&t.parentNode.replaceChild(n.domNode,t),n.attach(),n}}const Ae=class t extends ye{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach((t=>{t.attach()}))}attachUI(e){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=e,t.uiClass&&this.uiNode.classList.add(t.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new Ne,Array.from(this.domNode.childNodes).filter((t=>t!==this.uiNode)).reverse().forEach((t=>{try{const e=ke(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(t){if(t instanceof ue)return;throw t}}))}deleteAt(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,((t,e,n)=>{t.deleteAt(e,n)}))}descendant(e,n=0){const[r,i]=this.children.find(n);return null==e.blotName&&e(r)||null!=e.blotName&&r instanceof e?[r,i]:r instanceof t?r.descendant(e,i):[null,-1]}descendants(e,n=0,r=Number.MAX_VALUE){let i=[],s=r;return this.children.forEachAt(n,r,((n,r,o)=>{(null==e.blotName&&e(n)||null!=e.blotName&&n instanceof e)&&i.push(n),n instanceof t&&(i=i.concat(n.descendants(e,r,s))),s-=o})),i}detach(){this.children.forEach((t=>{t.detach()})),super.detach()}enforceAllowedChildren(){let e=!1;this.children.forEach((n=>{e||this.statics.allowedChildren.some((t=>n instanceof t))||(n.statics.scope===ae.BLOCK_BLOT?(null!=n.next&&this.splitAfter(n),null!=n.prev&&this.splitAfter(n.prev),n.parent.unwrap(),e=!0):n instanceof t?n.unwrap():n.remove())}))}formatAt(t,e,n,r){this.children.forEachAt(t,e,((t,e,i)=>{t.formatAt(e,i,n,r)}))}insertAt(t,e,n){const[r,i]=this.children.find(t);if(r)r.insertAt(i,e,n);else{const t=null==n?this.scroll.create("text",e):this.scroll.create(e,n);this.appendChild(t)}}insertBefore(t,e){null!=t.parent&&t.parent.children.remove(t);let n=null;this.children.insertBefore(t,e||null),t.parent=this,null!=e&&(n=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==n)&&this.domNode.insertBefore(t.domNode,n),t.attach()}length(){return this.children.reduce(((t,e)=>t+e.length()),0)}moveChildren(t,e){this.children.forEach((n=>{t.insertBefore(n,e)}))}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){const t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t)}else this.remove()}path(e,n=!1){const[r,i]=this.children.find(e,n),s=[[this,e]];return r instanceof t?s.concat(r.path(i,n)):(null!=r&&s.push([r,i]),s)}removeChild(t){this.children.remove(t)}replaceWith(e,n){const r="string"==typeof e?this.scroll.create(e,n):e;return r instanceof t&&this.moveChildren(r),super.replaceWith(r)}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}const n=this.clone();return this.parent&&this.parent.insertBefore(n,this.next||void 0),this.children.forEachAt(t,this.length(),((t,r,i)=>{const s=t.split(r,e);null!=s&&n.appendChild(s)})),n}splitAfter(t){const e=this.clone();for(;null!=t.next;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const n=[],r=[];t.forEach((t=>{t.target===this.domNode&&"childList"===t.type&&(n.push(...t.addedNodes),r.push(...t.removedNodes))})),r.forEach((t=>{if(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const e=this.scroll.find(t);null!=e&&(null==e.domNode.parentNode||e.domNode.parentNode===this.domNode)&&e.detach()})),n.filter((t=>t.parentNode===this.domNode&&t!==this.uiNode)).sort(((t,e)=>t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1)).forEach((t=>{let e=null;null!=t.nextSibling&&(e=this.scroll.find(t.nextSibling));const n=ke(t,this.scroll);(n.next!==e||null==n.next)&&(null!=n.parent&&n.parent.removeChild(this),this.insertBefore(n,e||void 0))})),this.enforceAllowedChildren()}};Ae.uiClass="";const _e=Ae,Ee=class t extends _e{static create(t){return super.create(t)}static formats(e,n){const r=n.query(t.blotName);if(null==r||e.tagName!==r.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new me(this.domNode)}format(e,n){if(e!==this.statics.blotName||n){const t=this.scroll.query(e,ae.INLINE);if(null==t)return;t instanceof ce?this.attributes.attribute(t,n):n&&(e!==this.statics.blotName||this.formats()[e]!==n)&&this.replaceWith(e,n)}else this.children.forEach((e=>{e instanceof t||(e=e.wrap(t.blotName,!0)),this.attributes.copy(e)})),this.unwrap()}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,r){null!=this.formats()[n]||this.scroll.query(n,ae.ATTRIBUTE)?this.isolate(t,e).format(n,r):super.formatAt(t,e,n,r)}optimize(e){super.optimize(e);const n=this.formats();if(0===Object.keys(n).length)return this.unwrap();const r=this.next;r instanceof t&&r.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const n in t)if(t[n]!==e[n])return!1;return!0}(n,r.formats())&&(r.moveChildren(this),r.remove())}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some((t=>t.target===this.domNode&&"attributes"===t.type))&&this.attributes.build()}wrap(e,n){const r=super.wrap(e,n);return r instanceof t&&this.attributes.move(r),r}};Ee.allowedChildren=[Ee,xe],Ee.blotName="inline",Ee.scope=ae.INLINE_BLOT,Ee.tagName="SPAN";const Ce=Ee,Te=class t extends _e{static create(t){return super.create(t)}static formats(e,n){const r=n.query(t.blotName);if(null==r||e.tagName!==r.tagName){if("string"==typeof this.tagName)return!0;if(Array.isArray(this.tagName))return e.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new me(this.domNode)}format(e,n){const r=this.scroll.query(e,ae.BLOCK);null!=r&&(r instanceof ce?this.attributes.attribute(r,n):e!==this.statics.blotName||n?n&&(e!==this.statics.blotName||this.formats()[e]!==n)&&this.replaceWith(e,n):this.replaceWith(t.blotName))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t}formatAt(t,e,n,r){null!=this.scroll.query(n,ae.BLOCK)?this.format(n,r):super.formatAt(t,e,n,r)}insertAt(t,e,n){if(null==n||null!=this.scroll.query(e,ae.INLINE))super.insertAt(t,e,n);else{const r=this.split(t);if(null==r)throw new Error("Attempt to insertAt after block boundaries");{const t=this.scroll.create(e,n);r.parent.insertBefore(t,r)}}}replaceWith(t,e){const n=super.replaceWith(t,e);return this.attributes.copy(n),n}update(t,e){super.update(t,e),t.some((t=>t.target===this.domNode&&"attributes"===t.type))&&this.attributes.build()}};Te.blotName="block",Te.scope=ae.BLOCK_BLOT,Te.tagName="P",Te.allowedChildren=[Ce,Te,xe];const qe=Te,Le=class extends _e{checkMerge(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,n,r){super.formatAt(t,e,n,r),this.enforceAllowedChildren()}insertAt(t,e,n){super.insertAt(t,e,n),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};Le.blotName="container",Le.scope=ae.BLOCK_BLOT;const Se=Le,Oe=class extends xe{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,n,r){0===t&&e===this.length()?this.format(n,r):super.formatAt(t,e,n,r)}formats(){return this.statics.formats(this.domNode,this.scroll)}},je={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Me=class extends _e{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver((t=>{this.update(t)})),this.observer.observe(this.domNode,je),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const n=this.registry.find(t,e);return n?n.scroll===this?n:e?this.find(n.scroll.domNode.parentNode,!0):null:null}query(t,e=ae.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){null!=this.scroll&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),0===t&&e===this.length()?this.children.forEach((t=>{t.remove()})):super.deleteAt(t,e)}formatAt(t,e,n,r){this.update(),super.formatAt(t,e,n,r)}insertAt(t,e,n){this.update(),super.insertAt(t,e,n)}optimize(t=[],e={}){super.optimize(e);const n=e.mutationsMap||new WeakMap;let r=Array.from(this.observer.takeRecords());for(;r.length>0;)t.push(r.pop());const i=(t,e=!0)=>{null==t||t===this||null!=t.domNode.parentNode&&(n.has(t.domNode)||n.set(t.domNode,[]),e&&i(t.parent))},s=t=>{n.has(t.domNode)&&(t instanceof _e&&t.children.forEach(s),n.delete(t.domNode),t.optimize(e))};let o=t;for(let e=0;o.length>0;e+=1){if(e>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(o.forEach((t=>{const e=this.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(i(this.find(t.previousSibling,!1)),Array.from(t.addedNodes).forEach((t=>{const e=this.find(t,!1);i(e,!1),e instanceof _e&&e.children.forEach((t=>{i(t,!1)}))}))):"attributes"===t.type&&i(e.prev)),i(e))})),this.children.forEach(s),o=Array.from(this.observer.takeRecords()),r=o.slice();r.length>0;)t.push(r.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const n=new WeakMap;t.map((t=>{const e=this.find(t.target,!0);return null==e?null:n.has(e.domNode)?(n.get(e.domNode).push(t),null):(n.set(e.domNode,[t]),e)})).forEach((t=>{null!=t&&t!==this&&n.has(t.domNode)&&t.update(n.get(t.domNode)||[],e)})),e.mutationsMap=n,n.has(this.domNode)&&super.update(n.get(this.domNode),e),this.optimize(t,e)}};Me.blotName="scroll",Me.defaultChild=qe,Me.allowedChildren=[qe,Se],Me.scope=ae.BLOCK_BLOT,Me.tagName="DIV";const Be=Me,Re=class t extends xe{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,n){null==n?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,n)}length(){return this.text.length}optimize(e){super.optimize(e),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof t&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(0===t)return this;if(t===this.length())return this.next}const n=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(n,this.next||void 0),this.text=this.statics.value(this.domNode),n}update(t,e){t.some((t=>"characterData"===t.type&&t.target===this.domNode))&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};Re.blotName="text",Re.scope=ae.INLINE_BLOT;const Ie=Re;var De=n(660);const Pe=lt(Object.keys,Object);var Ue=Object.prototype.hasOwnProperty;const ze=function(t){return wt(t)?Vt(t):function(t){if(!ut(t))return Pe(t);var e=[];for(var n in Object(t))Ue.call(t,n)&&"constructor"!=n&&e.push(n);return e}(t)},He=function(){return[]};var Fe=Object.prototype.propertyIsEnumerable,$e=Object.getOwnPropertySymbols;const Ve=$e?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var n=-1,r=null==t?0:t.length,i=0,s=[];++n<r;){var o=t[n];e(o,n,t)&&(s[i++]=o)}return s}($e(t),(function(e){return Fe.call(t,e)})))}:He,We=function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t},Ke=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)We(e,Ve(t)),t=at(t);return e}:He,Ge=function(t,e,n){var r=e(t);return vt(t)?r:We(r,n(t))},Ze=function(t){return Ge(t,ze,Ve)},Ye=function(t){return Ge(t,Gt,Ke)},Xe=M(h,"DataView"),Qe=M(h,"Promise"),Je=M(h,"Set"),tn=M(h,"WeakMap");var en="[object Map]",nn="[object Promise]",rn="[object Set]",sn="[object WeakMap]",on="[object DataView]",ln=E(Xe),an=E(B),cn=E(Qe),un=E(Je),hn=E(tn),dn=y;(Xe&&dn(new Xe(new ArrayBuffer(1)))!=on||B&&dn(new B)!=en||Qe&&dn(Qe.resolve())!=nn||Je&&dn(new Je)!=rn||tn&&dn(new tn)!=sn)&&(dn=function(t){var e=y(t),n="[object Object]"==e?t.constructor:void 0,r=n?E(n):"";if(r)switch(r){case ln:return on;case an:return en;case cn:return nn;case un:return rn;case hn:return sn}return e});const fn=dn;var pn=Object.prototype.hasOwnProperty;var gn=/\w*$/;var bn=d?d.prototype:void 0,mn=bn?bn.valueOf:void 0;const vn=function(t,e,n){var r,i,s,o=t.constructor;switch(e){case"[object ArrayBuffer]":return nt(t);case"[object Boolean]":case"[object Date]":return new o(+t);case"[object DataView]":return function(t,e){var n=e?nt(t.buffer):t.buffer;return new t.constructor(n,t.byteOffset,t.byteLength)}(t,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return rt(t,n);case"[object Map]":case"[object Set]":return new o;case"[object Number]":case"[object String]":return new o(t);case"[object RegExp]":return(s=new(i=t).constructor(i.source,gn.exec(i))).lastIndex=i.lastIndex,s;case"[object Symbol]":return r=t,mn?Object(mn.call(r)):{}}};var yn=Bt&&Bt.isMap;const wn=yn?St(yn):function(t){return dt(t)&&"[object Map]"==fn(t)};var xn=Bt&&Bt.isSet;const Nn=xn?St(xn):function(t){return dt(t)&&"[object Set]"==fn(t)};var kn="[object Arguments]",An="[object Function]",_n="[object Object]",En={};En[kn]=En["[object Array]"]=En["[object ArrayBuffer]"]=En["[object DataView]"]=En["[object Boolean]"]=En["[object Date]"]=En["[object Float32Array]"]=En["[object Float64Array]"]=En["[object Int8Array]"]=En["[object Int16Array]"]=En["[object Int32Array]"]=En["[object Map]"]=En["[object Number]"]=En[_n]=En["[object RegExp]"]=En["[object Set]"]=En["[object String]"]=En["[object Symbol]"]=En["[object Uint8Array]"]=En["[object Uint8ClampedArray]"]=En["[object Uint16Array]"]=En["[object Uint32Array]"]=!0,En["[object Error]"]=En[An]=En["[object WeakMap]"]=!1;const Cn=function t(e,n,r,i,s,o){var l,a=1&n,c=2&n,u=4&n;if(r&&(l=s?r(e,i,s,o):r(e)),void 0!==l)return l;if(!w(e))return e;var h=vt(e);if(h){if(l=function(t){var e=t.length,n=new t.constructor(e);return e&&"string"==typeof t[0]&&pn.call(t,"index")&&(n.index=t.index,n.input=t.input),n}(e),!a)return it(e,l)}else{var d=fn(e),f=d==An||"[object GeneratorFunction]"==d;if(At(e))return tt(e,a);if(d==_n||d==kn||f&&!s){if(l=c||f?{}:ht(e),!a)return c?function(t,e){return zt(t,Ke(t),e)}(e,function(t,e){return t&&zt(e,Gt(e),t)}(l,e)):function(t,e){return zt(t,Ve(t),e)}(e,function(t,e){return t&&zt(e,ze(e),t)}(l,e))}else{if(!En[d])return s?e:{};l=vn(e,d,a)}}o||(o=new V);var p=o.get(e);if(p)return p;o.set(e,l),Nn(e)?e.forEach((function(i){l.add(t(i,n,r,i,e,o))})):wn(e)&&e.forEach((function(i,s){l.set(s,t(i,n,r,s,e,o))}));var g=h?void 0:(u?c?Ye:Ze:c?Gt:ze)(e);return function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r&&!1!==e(t[n],n,t););}(g||e,(function(i,s){g&&(i=e[s=i]),Ut(l,s,t(i,n,r,s,e,o))})),l},Tn=function(t){return Cn(t,5)};function qn(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new F;++e<n;)this.add(t[e])}qn.prototype.add=qn.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},qn.prototype.has=function(t){return this.__data__.has(t)};const Ln=qn,Sn=function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1},On=function(t,e,n,r,i,s){var o=1&n,l=t.length,a=e.length;if(l!=a&&!(o&&a>l))return!1;var c=s.get(t),u=s.get(e);if(c&&u)return c==e&&u==t;var h=-1,d=!0,f=2&n?new Ln:void 0;for(s.set(t,e),s.set(e,t);++h<l;){var p=t[h],g=e[h];if(r)var b=o?r(g,p,h,e,t,s):r(p,g,h,t,e,s);if(void 0!==b){if(b)continue;d=!1;break}if(f){if(!Sn(e,(function(t,e){if(o=e,!f.has(o)&&(p===t||i(p,t,n,r,s)))return f.push(e);var o}))){d=!1;break}}else if(p!==g&&!i(p,g,n,r,s)){d=!1;break}}return s.delete(t),s.delete(e),d},jn=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n},Mn=function(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n};var Bn=d?d.prototype:void 0,Rn=Bn?Bn.valueOf:void 0;var In=Object.prototype.hasOwnProperty;var Dn="[object Arguments]",Pn="[object Array]",Un="[object Object]",zn=Object.prototype.hasOwnProperty;const Hn=function(t,e,n,r,s,o){var l=vt(t),a=vt(e),c=l?Pn:fn(t),u=a?Pn:fn(e),h=(c=c==Dn?Un:c)==Un,d=(u=u==Dn?Un:u)==Un,f=c==u;if(f&&At(t)){if(!At(e))return!1;l=!0,h=!1}if(f&&!h)return o||(o=new V),l||It(t)?On(t,e,n,r,s,o):function(t,e,n,r,s,o,l){switch(n){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!o(new et(t),new et(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var a=jn;case"[object Set]":var c=1&r;if(a||(a=Mn),t.size!=e.size&&!c)return!1;var u=l.get(t);if(u)return u==e;r|=2,l.set(t,e);var h=On(a(t),a(e),r,s,o,l);return l.delete(t),h;case"[object Symbol]":if(Rn)return Rn.call(t)==Rn.call(e)}return!1}(t,e,c,n,r,s,o);if(!(1&n)){var p=h&&zn.call(t,"__wrapped__"),g=d&&zn.call(e,"__wrapped__");if(p||g){var b=p?t.value():t,m=g?e.value():e;return o||(o=new V),s(b,m,n,r,o)}}return!!f&&(o||(o=new V),function(t,e,n,r,i,s){var o=1&n,l=Ze(t),a=l.length;if(a!=Ze(e).length&&!o)return!1;for(var c=a;c--;){var u=l[c];if(!(o?u in e:In.call(e,u)))return!1}var h=s.get(t),d=s.get(e);if(h&&d)return h==e&&d==t;var f=!0;s.set(t,e),s.set(e,t);for(var p=o;++c<a;){var g=t[u=l[c]],b=e[u];if(r)var m=o?r(b,g,u,e,t,s):r(g,b,u,t,e,s);if(!(void 0===m?g===b||i(g,b,n,r,s):m)){f=!1;break}p||(p="constructor"==u)}if(f&&!p){var v=t.constructor,y=e.constructor;v==y||!("constructor"in t)||!("constructor"in e)||"function"==typeof v&&v instanceof v&&"function"==typeof y&&y instanceof y||(f=!1)}return s.delete(t),s.delete(e),f}(t,e,n,r,s,o))},Fn=function t(e,n,r,i,s){return e===n||(null==e||null==n||!dt(e)&&!dt(n)?e!=e&&n!=n:Hn(e,n,r,i,t,s))},$n=function(t,e){return Fn(t,e)};class Vn extends Oe{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}Vn.blotName="break",Vn.tagName="BR";const Wn=Vn;class Kn extends Ie{}const Gn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Zn(t){return t.replace(/[&<>"']/g,(t=>Gn[t]))}class Yn extends Ce{static allowedChildren=[Yn,Wn,Oe,Kn];static order=["cursor","inline","link","underline","strike","italic","bold","script","code"];static compare(t,e){const n=Yn.order.indexOf(t),r=Yn.order.indexOf(e);return n>=0||r>=0?n-r:t===e?0:t<e?-1:1}formatAt(t,e,n,r){if(Yn.compare(this.statics.blotName,n)<0&&this.scroll.query(n,ae.BLOT)){const i=this.isolate(t,e);r&&i.wrap(n,r)}else super.formatAt(t,e,n,r)}optimize(t){if(super.optimize(t),this.parent instanceof Yn&&Yn.compare(this.statics.blotName,this.parent.statics.blotName)>0){const t=this.parent.isolate(this.offset(),this.length());this.moveChildren(t),t.wrap(this)}}}const Xn=Yn;class Qn extends qe{cache={};delta(){return null==this.cache.delta&&(this.cache.delta=tr(this)),this.cache.delta}deleteAt(t,e){super.deleteAt(t,e),this.cache={}}formatAt(t,e,n,r){e<=0||(this.scroll.query(n,ae.BLOCK)?t+e===this.length()&&this.format(n,r):super.formatAt(t,Math.min(e,this.length()-t-1),n,r),this.cache={})}insertAt(t,e,n){if(null!=n)return super.insertAt(t,e,n),void(this.cache={});if(0===e.length)return;const r=e.split("\n"),i=r.shift();i.length>0&&(t<this.length()-1||null==this.children.tail?super.insertAt(Math.min(t,this.length()-1),i):this.children.tail.insertAt(this.children.tail.length(),i),this.cache={});let s=this;r.reduce(((t,e)=>(s=s.split(t,!0),s.insertAt(0,e),e.length)),t+i.length)}insertBefore(t,e){const{head:n}=this.children;super.insertBefore(t,e),n instanceof Wn&&n.remove(),this.cache={}}length(){return null==this.cache.length&&(this.cache.length=super.length()+1),this.cache.length}moveChildren(t,e){super.moveChildren(t,e),this.cache={}}optimize(t){super.optimize(t),this.cache={}}path(t){return super.path(t,!0)}removeChild(t){super.removeChild(t),this.cache={}}split(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e&&(0===t||t>=this.length()-1)){const e=this.clone();return 0===t?(this.parent.insertBefore(e,this),this):(this.parent.insertBefore(e,this.next),e)}const n=super.split(t,e);return this.cache={},n}}Qn.blotName="block",Qn.tagName="P",Qn.defaultChild=Wn,Qn.allowedChildren=[Wn,Xn,Oe,Kn];class Jn extends Oe{attach(){super.attach(),this.attributes=new me(this.domNode)}delta(){return(new De).insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const n=this.scroll.query(t,ae.BLOCK_ATTRIBUTE);null!=n&&this.attributes.attribute(n,e)}formatAt(t,e,n,r){this.format(n,r)}insertAt(t,e,n){if(null!=n)return void super.insertAt(t,e,n);const r=e.split("\n"),i=r.pop(),s=r.map((t=>{const e=this.scroll.create(Qn.blotName);return e.insertAt(0,t),e})),o=this.split(t);s.forEach((t=>{this.parent.insertBefore(t,o)})),i&&this.parent.insertBefore(this.scroll.create("text",i),o)}}function tr(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return t.descendants(xe).reduce(((t,n)=>0===n.length()?t:t.insert(n.value(),er(n,{},e))),new De).insert("\n",er(t))}function er(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return null==t?e:("formats"in t&&"function"==typeof t.formats&&(e={...e,...t.formats()},n&&delete e["code-token"]),null==t.parent||"scroll"===t.parent.statics.blotName||t.parent.statics.scope!==t.statics.scope?e:er(t.parent,e,n))}Jn.scope=ae.BLOCK_BLOT;class nr extends Oe{static blotName="cursor";static className="ql-cursor";static tagName="span";static CONTENTS="\ufeff";static value(){}constructor(t,e,n){super(t,e),this.selection=n,this.textNode=document.createTextNode(nr.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){null!=this.parent&&this.parent.removeChild(this)}format(t,e){if(0!==this.savedLength)return void super.format(t,e);let n=this,r=0;for(;null!=n&&n.statics.scope!==ae.BLOCK_BLOT;)r+=n.offset(n.parent),n=n.parent;null!=n&&(this.savedLength=nr.CONTENTS.length,n.optimize(),n.formatAt(r,nr.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||null==this.parent)return null;const t=this.selection.getNativeRange();for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof Kn?this.prev:null,n=e?e.length():0,r=this.next instanceof Kn?this.next:null,i=r?r.text:"",{textNode:s}=this,o=s.data.split(nr.CONTENTS).join("");let l;if(s.data=nr.CONTENTS,e)l=e,(o||r)&&(e.insertAt(e.length(),o+i),r&&r.remove());else if(r)l=r,r.insertAt(0,o);else{const t=document.createTextNode(o);l=this.scroll.create(t),this.parent.insertBefore(l,this)}if(this.remove(),t){const i=(t,i)=>e&&t===e.domNode?i:t===s?n+i-1:r&&t===r.domNode?n+o.length+i:null,a=i(t.start.node,t.start.offset),c=i(t.end.node,t.end.offset);if(null!==a&&null!==c)return{startNode:l.domNode,startOffset:a,endNode:l.domNode,endOffset:c}}return null}update(t,e){if(t.some((t=>"characterData"===t.type&&t.target===this.textNode))){const t=this.restore();t&&(e.range=t)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if("A"===e.domNode.tagName){this.savedLength=nr.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}}const rr=nr;var ir=n(697);const sr=new WeakMap,or=["error","warn","log","info"];let lr="warn";function ar(t){if(lr&&or.indexOf(t)<=or.indexOf(lr)){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];console[t](...n)}}function cr(t){return or.reduce(((e,n)=>(e[n]=ar.bind(console,n,t),e)),{})}cr.level=t=>{lr=t},ar.level=cr.level;const ur=cr,hr=ur("quill:events");["selectionchange","mousedown","mouseup","click"].forEach((t=>{document.addEventListener(t,(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];Array.from(document.querySelectorAll(".ql-container")).forEach((t=>{const n=sr.get(t);n&&n.emitter&&n.emitter.handleDOM(...e)}))}))}));const dr=class extends ir{static events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"};static sources={API:"api",SILENT:"silent",USER:"user"};constructor(){super(),this.domListeners={},this.on("error",hr.error)}emit(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return hr.log.call(hr,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];(this.domListeners[t.type]||[]).forEach((e=>{let{node:r,handler:i}=e;(t.target===r||r.contains(t.target))&&i(t,...n)}))}listenDOM(t,e,n){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:n})}},fr=ur("quill:selection");class pr{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.index=t,this.length=e}}function gr(t,e){try{e.parentNode}catch(t){return!1}return t.contains(e)}const br=class{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new pr(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,(()=>{this.mouseDown||this.composing||setTimeout(this.update.bind(this,dr.sources.USER),1)})),this.emitter.on(dr.events.SCROLL_BEFORE_UPDATE,(()=>{if(!this.hasFocus())return;const t=this.getNativeRange();null!=t&&t.start.node!==this.cursor.textNode&&this.emitter.once(dr.events.SCROLL_UPDATE,((e,n)=>{try{this.root.contains(t.start.node)&&this.root.contains(t.end.node)&&this.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset);const r=n.some((t=>"characterData"===t.type||"childList"===t.type||"attributes"===t.type&&t.target===this.root));this.update(r?dr.sources.SILENT:e)}catch(t){}}))})),this.emitter.on(dr.events.SCROLL_OPTIMIZE,((t,e)=>{if(e.range){const{startNode:t,startOffset:n,endNode:r,endOffset:i}=e.range;this.setNativeRange(t,n,r,i),this.update(dr.sources.SILENT)}})),this.update(dr.sources.SILENT)}handleComposition(){this.emitter.on(dr.events.COMPOSITION_BEFORE_START,(()=>{this.composing=!0})),this.emitter.on(dr.events.COMPOSITION_END,(()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout((()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)}),1)}}))}handleDragging(){this.emitter.listenDOM("mousedown",document.body,(()=>{this.mouseDown=!0})),this.emitter.listenDOM("mouseup",document.body,(()=>{this.mouseDown=!1,this.update(dr.sources.USER)}))}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const n=this.getNativeRange();if(null!=n&&n.native.collapsed&&!this.scroll.query(t,ae.BLOCK)){if(n.start.node!==this.cursor.textNode){const t=this.scroll.find(n.start.node,!1);if(null==t)return;if(t instanceof xe){const e=t.split(n.start.offset);t.parent.insertBefore(this.cursor,e)}else t.insertBefore(this.cursor,n.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const n=this.scroll.length();let r;t=Math.min(t,n-1),e=Math.min(t+e,n-1)-t;let[i,s]=this.scroll.leaf(t);if(null==i)return null;if(e>0&&s===i.length()){const[e]=this.scroll.leaf(t+1);if(e){const[n]=this.scroll.line(t),[r]=this.scroll.line(t+1);n===r&&(i=e,s=0)}}[r,s]=i.position(s,!0);const o=document.createRange();if(e>0)return o.setStart(r,s),[i,s]=this.scroll.leaf(t+e),null==i?null:([r,s]=i.position(s,!0),o.setEnd(r,s),o.getBoundingClientRect());let l,a="left";if(r instanceof Text){if(!r.data.length)return null;s<r.data.length?(o.setStart(r,s),o.setEnd(r,s+1)):(o.setStart(r,s-1),o.setEnd(r,s),a="right"),l=o.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;l=i.domNode.getBoundingClientRect(),s>0&&(a="right")}return{bottom:l.top+l.height,height:l.height,left:l[a],right:l[a],top:l.top,width:0}}getNativeRange(){const t=document.getSelection();if(null==t||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(null==e)return null;const n=this.normalizeNative(e);return fr.info("getNativeRange",n),n}getRange(){const t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return null==e?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||null!=document.activeElement&&gr(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const n=e.map((t=>{const[e,n]=t,r=this.scroll.find(e,!0),i=r.offset(this.scroll);return 0===n?i:r instanceof xe?i+r.index(e,n):i+r.length()})),r=Math.min(Math.max(...n),this.scroll.length()-1),i=Math.min(r,...n);return new pr(i,r-i)}normalizeNative(t){if(!gr(this.root,t.startContainer)||!t.collapsed&&!gr(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach((t=>{let{node:e,offset:n}=t;for(;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>n)e=e.childNodes[n],n=0;else{if(e.childNodes.length!==n)break;e=e.lastChild,n=e instanceof Text?e.data.length:e.childNodes.length>0?e.childNodes.length:e.childNodes.length+1}t.node=e,t.offset=n})),e}rangeToNative(t){const e=this.scroll.length(),n=(t,n)=>{t=Math.min(e-1,t);const[r,i]=this.scroll.leaf(t);return r?r.position(i,n):[null,-1]};return[...n(t.index,!1),...n(t.index+t.length,!0)]}setNativeRange(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(fr.info("setNativeRange",t,e,n,r),null!=t&&(null==this.root.parentNode||null==t.parentNode||null==n.parentNode))return;const s=document.getSelection();if(null!=s)if(null!=t){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:o}=this.getNativeRange()||{};if(null==o||i||t!==o.startContainer||e!==o.startOffset||n!==o.endContainer||r!==o.endOffset){t instanceof Element&&"BR"===t.tagName&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),n instanceof Element&&"BR"===n.tagName&&(r=Array.from(n.parentNode.childNodes).indexOf(n),n=n.parentNode);const i=document.createRange();i.setStart(t,e),i.setEnd(n,r),s.removeAllRanges(),s.addRange(i)}}else s.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:dr.sources.API;if("string"==typeof e&&(n=e,e=!1),fr.info("setRange",t),null!=t){const n=this.rangeToNative(t);this.setNativeRange(...n,e)}else this.setNativeRange(null);this.update(n)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:dr.sources.USER;const e=this.lastRange,[n,r]=this.getRange();if(this.lastRange=n,this.lastNative=r,null!=this.lastRange&&(this.savedRange=this.lastRange),!$n(e,this.lastRange)){if(!this.composing&&null!=r&&r.native.collapsed&&r.start.node!==this.cursor.textNode){const t=this.cursor.restore();t&&this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)}const n=[dr.events.SELECTION_CHANGE,Tn(this.lastRange),Tn(e),t];this.emitter.emit(dr.events.EDITOR_CHANGE,...n),t!==dr.sources.SILENT&&this.emitter.emit(...n)}}},mr=/^[ -~]*$/;function vr(t,e,n){if(0===t.length){const[t]=xr(n.pop());return e<=0?`</li></${t}>`:`</li></${t}>${vr([],e-1,n)}`}const[{child:r,offset:i,length:s,indent:o,type:l},...a]=t,[c,u]=xr(l);if(o>e)return n.push(l),o===e+1?`<${c}><li${u}>${yr(r,i,s)}${vr(a,o,n)}`:`<${c}><li>${vr(t,e+1,n)}`;const h=n[n.length-1];if(o===e&&l===h)return`</li><li${u}>${yr(r,i,s)}${vr(a,o,n)}`;const[d]=xr(n.pop());return`</li></${d}>${vr(t,e-1,n)}`}function yr(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if("html"in t&&"function"==typeof t.html)return t.html(e,n);if(t instanceof Kn)return Zn(t.value().slice(e,e+n)).replaceAll(" ","&nbsp;");if(t instanceof _e){if("list-container"===t.statics.blotName){const r=[];return t.children.forEachAt(e,n,((t,e,n)=>{const i="formats"in t&&"function"==typeof t.formats?t.formats():{};r.push({child:t,offset:e,length:n,indent:i.indent||0,type:i.list})})),vr(r,-1,[])}const i=[];if(t.children.forEachAt(e,n,((t,e,n)=>{i.push(yr(t,e,n))})),r||"list"===t.statics.blotName)return i.join("");const{outerHTML:s,innerHTML:o}=t.domNode,[l,a]=s.split(`>${o}<`);return"<table"===l?`<table style="border: 1px solid #000;">${i.join("")}<${a}`:`${l}>${i.join("")}<${a}`}return t.domNode instanceof Element?t.domNode.outerHTML:""}function wr(t,e){return Object.keys(e).reduce(((n,r)=>{if(null==t[r])return n;const i=e[r];return i===t[r]?n[r]=i:Array.isArray(i)?i.indexOf(t[r])<0?n[r]=i.concat([t[r]]):n[r]=i:n[r]=[i,t[r]],n}),{})}function xr(t){const e="ordered"===t?"ol":"ul";switch(t){case"checked":return[e,' data-list="checked"'];case"unchecked":return[e,' data-list="unchecked"'];default:return[e,""]}}function Nr(t){return t.reduce(((t,e)=>{if("string"==typeof e.insert){const n=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(n,e.attributes)}return t.push(e)}),new De)}function kr(t,e){let{index:n,length:r}=t;return new pr(n+e,r)}const Ar=class{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const n=Nr(t),r=new De;return function(t){const e=[];return t.forEach((t=>{"string"==typeof t.insert?t.insert.split("\n").forEach(((n,r)=>{r&&e.push({insert:"\n",attributes:t.attributes}),n&&e.push({insert:n,attributes:t.attributes})})):e.push(t)})),e}(n.ops.slice()).reduce(((t,n)=>{const i=De.Op.length(n);let s=n.attributes||{},o=!1,l=!1;if(null!=n.insert){if(r.retain(i),"string"==typeof n.insert){const r=n.insert;l=!r.endsWith("\n")&&(e<=t||!!this.scroll.descendant(Jn,t)[0]),this.scroll.insertAt(t,r);const[i,o]=this.scroll.line(t);let a=oe({},er(i));if(i instanceof Qn){const[t]=i.descendant(xe,o);t&&(a=oe(a,er(t)))}s=De.AttributeMap.diff(a,s)||{}}else if("object"==typeof n.insert){const r=Object.keys(n.insert)[0];if(null==r)return t;const i=null!=this.scroll.query(r,ae.INLINE);if(i)(e<=t||this.scroll.descendant(Jn,t)[0])&&(l=!0);else if(t>0){const[e,n]=this.scroll.descendant(xe,t-1);e instanceof Kn?"\n"!==e.value()[n]&&(o=!0):e instanceof Oe&&e.statics.scope===ae.INLINE_BLOT&&(o=!0)}if(this.scroll.insertAt(t,r,n.insert[r]),i){const[e]=this.scroll.descendant(xe,t);if(e){const t=oe({},er(e));s=De.AttributeMap.diff(t,s)||{}}}}e+=i}else if(r.push(n),null!==n.retain&&"object"==typeof n.retain){const e=Object.keys(n.retain)[0];if(null==e)return t;this.scroll.updateEmbedAt(t,e,n.retain[e])}Object.keys(s).forEach((e=>{this.scroll.formatAt(t,i,e,s[e])}));const a=o?1:0,c=l?1:0;return e+=a+c,r.retain(a),r.delete(c),t+i+a+c}),0),r.reduce(((t,e)=>"number"==typeof e.delete?(this.scroll.deleteAt(t,e.delete),t):t+De.Op.length(e)),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(n)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update((new De).retain(t).delete(e))}formatLine(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update(),Object.keys(n).forEach((r=>{this.scroll.lines(t,Math.max(e,1)).forEach((t=>{t.format(r,n[r])}))})),this.scroll.optimize();const r=(new De).retain(t).retain(e,Tn(n));return this.update(r)}formatText(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Object.keys(n).forEach((r=>{this.scroll.formatAt(t,e,r,n[r])}));const r=(new De).retain(t).retain(e,Tn(n));return this.update(r)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce(((t,e)=>t.concat(e.delta())),new De)}getFormat(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=[],r=[];0===e?this.scroll.path(t).forEach((t=>{const[e]=t;e instanceof Qn?n.push(e):e instanceof xe&&r.push(e)})):(n=this.scroll.lines(t,e),r=this.scroll.descendants(xe,t,e));const[i,s]=[n,r].map((t=>{const e=t.shift();if(null==e)return{};let n=er(e);for(;Object.keys(n).length>0;){const e=t.shift();if(null==e)return n;n=wr(er(e),n)}return n}));return{...i,...s}}getHTML(t,e){const[n,r]=this.scroll.line(t);if(n){const i=n.length();return n.length()>=r+e&&(0!==r||e!==i)?yr(n,r,e,!0):yr(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter((t=>"string"==typeof t.insert)).map((t=>t.insert)).join("")}insertContents(t,e){const n=Nr(e),r=(new De).retain(t).concat(n);return this.scroll.insertContents(t,n),this.update(r)}insertEmbed(t,e,n){return this.scroll.insertAt(t,e,n),this.update((new De).retain(t).insert({[e]:n}))}insertText(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(n).forEach((r=>{this.scroll.formatAt(t,e.length,r,n[r])})),this.update((new De).retain(t).insert(e,Tn(n)))}isBlank(){if(0===this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if(t?.statics.blotName!==Qn.blotName)return!1;const e=t;return!(e.children.length>1)&&e.children.head instanceof Wn}removeFormat(t,e){const n=this.getText(t,e),[r,i]=this.scroll.line(t+e);let s=0,o=new De;null!=r&&(s=r.length()-i,o=r.delta().slice(i,i+s-1).insert("\n"));const l=this.getContents(t,e+s).diff((new De).insert(n).concat(o)),a=(new De).retain(t).concat(l);return this.applyDelta(a)}update(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;const r=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(mr)&&this.scroll.find(e[0].target)){const i=this.scroll.find(e[0].target),s=er(i),o=i.offset(this.scroll),l=e[0].oldValue.replace(rr.CONTENTS,""),a=(new De).insert(l),c=(new De).insert(i.value()),u=n&&{oldRange:kr(n.oldRange,-o),newRange:kr(n.newRange,-o)};t=(new De).retain(o).concat(a.diff(c,u)).reduce(((t,e)=>e.insert?t.insert(e.insert,s):t.push(e)),new De),this.delta=r.compose(t)}else this.delta=this.getDelta(),t&&$n(r.compose(t),this.delta)||(t=r.diff(this.delta,n));return t}},_r=class{static DEFAULTS={};constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.quill=t,this.options=e}},Er="\ufeff",Cr=class extends Oe{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach((t=>{this.contentNode.appendChild(t)})),this.leftGuard=document.createTextNode(Er),this.rightGuard=document.createTextNode(Er),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e,n=null;const r=t.data.split(Er).join("");if(t===this.leftGuard)if(this.prev instanceof Kn){const t=this.prev.length();this.prev.insertAt(t,r),n={startNode:this.prev.domNode,startOffset:t+r.length}}else e=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(e),this),n={startNode:e,startOffset:r.length};else t===this.rightGuard&&(this.next instanceof Kn?(this.next.insertAt(0,r),n={startNode:this.next.domNode,startOffset:r.length}):(e=document.createTextNode(r),this.parent.insertBefore(this.scroll.create(e),this.next),n={startNode:e,startOffset:r.length}));return t.data=Er,n}update(t,e){t.forEach((t=>{if("characterData"===t.type&&(t.target===this.leftGuard||t.target===this.rightGuard)){const n=this.restore(t.target);n&&(e.range=n)}}))}},Tr=class{isComposing=!1;constructor(t,e){this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",(t=>{this.isComposing||this.handleCompositionStart(t)})),this.scroll.domNode.addEventListener("compositionend",(t=>{this.isComposing&&queueMicrotask((()=>{this.handleCompositionEnd(t)}))}))}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;!e||e instanceof Cr||(this.emitter.emit(dr.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(dr.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(dr.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(dr.events.COMPOSITION_END,t),this.isComposing=!1}};class qr{static DEFAULTS={modules:{}};static themes={default:qr};modules={};constructor(t,e){this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach((t=>{null==this.modules[t]&&this.addModule(t)}))}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}const Lr=qr,Sr=t=>{const e=t.getBoundingClientRect(),n="offsetWidth"in t&&Math.abs(e.width)/t.offsetWidth||1,r="offsetHeight"in t&&Math.abs(e.height)/t.offsetHeight||1;return{top:e.top,right:e.left+t.clientWidth*n,bottom:e.top+t.clientHeight*r,left:e.left}},Or=t=>{const e=parseInt(t,10);return Number.isNaN(e)?0:e},jr=(t,e,n,r,i,s)=>t<n&&e>r?0:t<n?-(n-t+i):e>r?e-t>r-n?t+i-n:e-r+s:0,Mr=["block","break","cursor","inline","scroll","text"],Br=ur("quill"),Rr=new de;_e.uiClass="ql-ui";class Ir{static DEFAULTS={bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:Rr,theme:"default"};static events=dr.events;static sources=dr.sources;static version="2.0.3";static imports={delta:De,parchment:r,"core/module":_r,"core/theme":Lr};static debug(t){!0===t&&(t="log"),ur.level(t)}static find(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return sr.get(t)||Rr.find(t,e)}static import(t){return null==this.imports[t]&&Br.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if("string"!=typeof(arguments.length<=0?void 0:arguments[0])){const t=arguments.length<=0?void 0:arguments[0],e=!!(arguments.length<=1?void 0:arguments[1]),n="attrName"in t?t.attrName:t.blotName;"string"==typeof n?this.register(`formats/${n}`,t,e):Object.keys(t).forEach((n=>{this.register(n,t[n],e)}))}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],n=!!(arguments.length<=2?void 0:arguments[2]);null==this.imports[t]||n||Br.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&"boolean"!=typeof e&&"abstract"!==e.blotName&&Rr.register(e),"function"==typeof e.register&&e.register(Rr)}}constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.options=function(t,e){const n=Dr(t);if(!n)throw new Error("Invalid Quill container");const r=!e.theme||e.theme===Ir.DEFAULTS.theme?Lr:Ir.import(`themes/${e.theme}`);if(!r)throw new Error(`Invalid theme ${e.theme}. Did you register it?`);const{modules:i,...s}=Ir.DEFAULTS,{modules:o,...l}=r.DEFAULTS;let a=Pr(e.modules);null!=a&&a.toolbar&&a.toolbar.constructor!==Object&&(a={...a,toolbar:{container:a.toolbar}});const c=oe({},Pr(i),Pr(o),a),u={...s,...Ur(l),...Ur(e)};let h=e.registry;return h?e.formats&&Br.warn('Ignoring "formats" option because "registry" is specified'):h=e.formats?((t,e,n)=>{const r=new de;return Mr.forEach((t=>{const n=e.query(t);n&&r.register(n)})),t.forEach((t=>{let i=e.query(t);i||n.error(`Cannot register "${t}" specified in "formats" config. Are you sure it was registered?`);let s=0;for(;i;)if(r.register(i),i="blotName"in i?i.requiredContainer??null:null,s+=1,s>100){n.error(`Cycle detected in registering blot requiredContainer: "${t}"`);break}})),r})(e.formats,u.registry,Br):u.registry,{...u,registry:h,container:n,theme:r,modules:Object.entries(c).reduce(((t,e)=>{let[n,r]=e;if(!r)return t;const i=Ir.import(`modules/${n}`);return null==i?(Br.error(`Cannot load ${n} module. Are you sure you registered it?`),t):{...t,[n]:oe({},i.DEFAULTS||{},r)}}),{}),bounds:Dr(u.bounds)}}(t,e),this.container=this.options.container,null==this.container)return void Br.error("Invalid Quill container",t);this.options.debug&&Ir.debug(this.options.debug);const n=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",sr.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new dr;const r=Be.blotName,i=this.options.registry.query(r);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${r}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new Ar(this.scroll),this.selection=new br(this.scroll,this.emitter),this.composition=new Tr(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(dr.events.EDITOR_CHANGE,(t=>{t===dr.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())})),this.emitter.on(dr.events.SCROLL_UPDATE,((t,e)=>{const n=this.selection.lastRange,[r]=this.selection.getRange(),i=n&&r?{oldRange:n,newRange:r}:void 0;zr.call(this,(()=>this.editor.update(null,e,i)),t)})),this.emitter.on(dr.events.SCROLL_EMBED_UPDATE,((t,e)=>{const n=this.selection.lastRange,[r]=this.selection.getRange(),i=n&&r?{oldRange:n,newRange:r}:void 0;zr.call(this,(()=>{const n=(new De).retain(t.offset(this)).retain({[t.statics.blotName]:e});return this.editor.update(n,[],i)}),Ir.sources.USER)})),n){const t=this.clipboard.convert({html:`${n}<p><br></p>`,text:"\n"});this.setContents(t)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof t){const e=t;(t=document.createElement("div")).classList.add(e)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,n){return[t,e,,n]=Hr(t,e,n),zr.call(this,(()=>this.editor.deleteText(t,e)),n,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:dr.sources.API;return zr.call(this,(()=>{const n=this.getSelection(!0);let r=new De;if(null==n)return r;if(this.scroll.query(t,ae.BLOCK))r=this.editor.formatLine(n.index,n.length,{[t]:e});else{if(0===n.length)return this.selection.format(t,e),r;r=this.editor.formatText(n.index,n.length,{[t]:e})}return this.setSelection(n,dr.sources.SILENT),r}),n)}formatLine(t,e,n,r,i){let s;return[t,e,s,i]=Hr(t,e,n,r,i),zr.call(this,(()=>this.editor.formatLine(t,e,s)),i,t,0)}formatText(t,e,n,r,i){let s;return[t,e,s,i]=Hr(t,e,n,r,i),zr.call(this,(()=>this.editor.formatText(t,e,s)),i,t,0)}getBounds(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=null;if(n="number"==typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length),!n)return null;const r=this.container.getBoundingClientRect();return{bottom:n.bottom-r.top,height:n.height,left:n.left-r.left,right:n.right-r.left,top:n.top-r.top,width:n.width}}getContents(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t;return[t,e]=Hr(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return"number"==typeof t&&(e=e??this.getLength()-t),[t,e]=Hr(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return"number"==typeof t&&(e=e??this.getLength()-t),[t,e]=Hr(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Ir.sources.API;return zr.call(this,(()=>this.editor.insertEmbed(t,e,n)),r,t)}insertText(t,e,n,r,i){let s;return[t,,s,i]=Hr(t,0,n,r,i),zr.call(this,(()=>this.editor.insertText(t,e,s)),i,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,n){return[t,e,,n]=Hr(t,e,n),zr.call(this,(()=>this.editor.removeFormat(t,e)),n,t)}scrollRectIntoView(t){((t,e)=>{const n=t.ownerDocument;let r=e,i=t;for(;i;){const t=i===n.body,e=t?{top:0,right:window.visualViewport?.width??n.documentElement.clientWidth,bottom:window.visualViewport?.height??n.documentElement.clientHeight,left:0}:Sr(i),o=getComputedStyle(i),l=jr(r.left,r.right,e.left,e.right,Or(o.scrollPaddingLeft),Or(o.scrollPaddingRight)),a=jr(r.top,r.bottom,e.top,e.bottom,Or(o.scrollPaddingTop),Or(o.scrollPaddingBottom));if(l||a)if(t)n.defaultView?.scrollBy(l,a);else{const{scrollLeft:t,scrollTop:e}=i;a&&(i.scrollTop+=a),l&&(i.scrollLeft+=l);const n=i.scrollLeft-t,s=i.scrollTop-e;r={left:r.left-n,top:r.top-s,right:r.right-n,bottom:r.bottom-s}}i=t||"fixed"===o.position?null:(s=i).parentElement||s.getRootNode().host||null}var s})(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:dr.sources.API;return zr.call(this,(()=>{t=new De(t);const e=this.getLength(),n=this.editor.deleteText(0,e),r=this.editor.insertContents(0,t),i=this.editor.deleteText(this.getLength()-1,1);return n.compose(r).compose(i)}),e)}setSelection(t,e,n){null==t?this.selection.setRange(null,e||Ir.sources.API):([t,e,,n]=Hr(t,e,n),this.selection.setRange(new pr(Math.max(0,t),e),n),n!==dr.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:dr.sources.API;const n=(new De).insert(t);return this.setContents(n,e)}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:dr.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:dr.sources.API;return zr.call(this,(()=>(t=new De(t),this.editor.applyDelta(t))),e,!0)}}function Dr(t){return"string"==typeof t?document.querySelector(t):t}function Pr(t){return Object.entries(t??{}).reduce(((t,e)=>{let[n,r]=e;return{...t,[n]:!0===r?{}:r}}),{})}function Ur(t){return Object.fromEntries(Object.entries(t).filter((t=>void 0!==t[1])))}function zr(t,e,n,r){if(!this.isEnabled()&&e===dr.sources.USER&&!this.allowReadOnlyEdits)return new De;let i=null==n?null:this.getSelection();const s=this.editor.delta,o=t();if(null!=i&&(!0===n&&(n=i.index),null==r?i=Fr(i,o,e):0!==r&&(i=Fr(i,n,r,e)),this.setSelection(i,dr.sources.SILENT)),o.length()>0){const t=[dr.events.TEXT_CHANGE,o,s,e];this.emitter.emit(dr.events.EDITOR_CHANGE,...t),e!==dr.sources.SILENT&&this.emitter.emit(...t)}return o}function Hr(t,e,n,r,i){let s={};return"number"==typeof t.index&&"number"==typeof t.length?"number"!=typeof e?(i=r,r=n,n=e,e=t.length,t=t.index):(e=t.length,t=t.index):"number"!=typeof e&&(i=r,r=n,n=e,e=0),"object"==typeof n?(s=n,i=r):"string"==typeof n&&(null!=r?s[n]=r:i=n),[t,e,s,i=i||dr.sources.API]}function Fr(t,e,n,r){const i="number"==typeof n?n:0;if(null==t)return null;let s,o;return e&&"function"==typeof e.transformPosition?[s,o]=[t.index,t.index+t.length].map((t=>e.transformPosition(t,r!==dr.sources.USER))):[s,o]=[t.index,t.index+t.length].map((t=>t<e||t===e&&r===dr.sources.USER?t:i>=0?t+i:Math.max(e,t+i))),new pr(s,o-s)}const $r=class extends Se{};function Vr(t){return t instanceof Qn||t instanceof Jn}function Wr(t){return"function"==typeof t.updateContent}function Kr(t,e,n){n.reduce(((e,n)=>{const r=De.Op.length(n);let i=n.attributes||{};if(null!=n.insert)if("string"==typeof n.insert){const r=n.insert;t.insertAt(e,r);const[s]=t.descendant(xe,e),o=er(s);i=De.AttributeMap.diff(o,i)||{}}else if("object"==typeof n.insert){const r=Object.keys(n.insert)[0];if(null==r)return e;if(t.insertAt(e,r,n.insert[r]),null!=t.scroll.query(r,ae.INLINE)){const[n]=t.descendant(xe,e),r=er(n);i=De.AttributeMap.diff(r,i)||{}}}return Object.keys(i).forEach((n=>{t.formatAt(e,r,n,i[n])})),e+r}),e)}const Gr={scope:ae.BLOCK,whitelist:["right","center","justify"]},Zr=new ce("align","align",Gr),Yr=new pe("align","ql-align",Gr),Xr=new be("align","text-align",Gr);class Qr extends be{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map((t=>`00${parseInt(t,10).toString(16)}`.slice(-2))).join("")}`):e}}const Jr=new pe("color","ql-color",{scope:ae.INLINE}),ti=new Qr("color","color",{scope:ae.INLINE}),ei=new pe("background","ql-bg",{scope:ae.INLINE}),ni=new Qr("background","background-color",{scope:ae.INLINE});class ri extends $r{static create(t){const e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map((t=>t.length()<=1?"":t.domNode.innerText)).join("\n").slice(t,t+e)}html(t,e){return`<pre>\n${Zn(this.code(t,e))}\n</pre>`}}class ii extends Qn{static TAB="  ";static register(){Ir.register(ri)}}class si extends Xn{}si.blotName="code",si.tagName="CODE",ii.blotName="code-block",ii.className="ql-code-block",ii.tagName="DIV",ri.blotName="code-block-container",ri.className="ql-code-block-container",ri.tagName="DIV",ri.allowedChildren=[ii],ii.allowedChildren=[Kn,Wn,rr],ii.requiredContainer=ri;const oi={scope:ae.BLOCK,whitelist:["rtl"]},li=new ce("direction","dir",oi),ai=new pe("direction","ql-direction",oi),ci=new be("direction","direction",oi),ui={scope:ae.INLINE,whitelist:["serif","monospace"]},hi=new pe("font","ql-font",ui),di=new class extends be{value(t){return super.value(t).replace(/["']/g,"")}}("font","font-family",ui),fi=new pe("size","ql-size",{scope:ae.INLINE,whitelist:["small","large","huge"]}),pi=new be("size","font-size",{scope:ae.INLINE,whitelist:["10px","18px","32px"]}),gi=ur("quill:keyboard"),bi=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class mi extends _r{static match(t,e){return!["altKey","ctrlKey","metaKey","shiftKey"].some((n=>!!e[n]!==t[n]&&null!==e[n]))&&(e.key===t.key||e.key===t.which)}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach((t=>{this.options.bindings[t]&&this.addBinding(this.options.bindings[t])})),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},(()=>{})),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=function(t){if("string"==typeof t||"number"==typeof t)t={key:t};else{if("object"!=typeof t)return null;t=Tn(t)}return t.shortKey&&(t[bi]=t.shortKey,delete t.shortKey),t}(t);null!=r?("function"==typeof e&&(e={handler:e}),"function"==typeof n&&(n={handler:n}),(Array.isArray(r.key)?r.key:[r.key]).forEach((t=>{const i={...r,key:t,...e,...n};this.bindings[i.key]=this.bindings[i.key]||[],this.bindings[i.key].push(i)}))):gi.warn("Attempted to add invalid keyboard binding",r)}listen(){this.quill.root.addEventListener("keydown",(t=>{if(t.defaultPrevented||t.isComposing)return;if(229===t.keyCode&&("Enter"===t.key||"Backspace"===t.key))return;const e=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter((e=>mi.match(t,e)));if(0===e.length)return;const n=Ir.find(t.target,!0);if(n&&n.scroll!==this.quill.scroll)return;const r=this.quill.getSelection();if(null==r||!this.quill.hasFocus())return;const[i,s]=this.quill.getLine(r.index),[o,l]=this.quill.getLeaf(r.index),[a,c]=0===r.length?[o,l]:this.quill.getLeaf(r.index+r.length),u=o instanceof Ie?o.value().slice(0,l):"",h=a instanceof Ie?a.value().slice(c):"",d={collapsed:0===r.length,empty:0===r.length&&i.length()<=1,format:this.quill.getFormat(r),line:i,offset:s,prefix:u,suffix:h,event:t};e.some((t=>{if(null!=t.collapsed&&t.collapsed!==d.collapsed)return!1;if(null!=t.empty&&t.empty!==d.empty)return!1;if(null!=t.offset&&t.offset!==d.offset)return!1;if(Array.isArray(t.format)){if(t.format.every((t=>null==d.format[t])))return!1}else if("object"==typeof t.format&&!Object.keys(t.format).every((e=>!0===t.format[e]?null!=d.format[e]:!1===t.format[e]?null==d.format[e]:$n(t.format[e],d.format[e]))))return!1;return!(null!=t.prefix&&!t.prefix.test(d.prefix)||null!=t.suffix&&!t.suffix.test(d.suffix)||!0===t.handler.call(this,r,d,t))}))&&t.preventDefault()}))}handleBackspace(t,e){const n=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(0===t.index||this.quill.getLength()<=1)return;let r={};const[i]=this.quill.getLine(t.index);let s=(new De).retain(t.index-n).delete(n);if(0===e.offset){const[e]=this.quill.getLine(t.index-1);if(e&&!("block"===e.statics.blotName&&e.length()<=1)){const e=i.formats(),n=this.quill.getFormat(t.index-1,1);if(r=De.AttributeMap.diff(e,n)||{},Object.keys(r).length>0){const e=(new De).retain(t.index+i.length()-2).retain(1,r);s=s.compose(e)}}}this.quill.updateContents(s,Ir.sources.USER),this.quill.focus()}handleDelete(t,e){const n=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-n)return;let r={};const[i]=this.quill.getLine(t.index);let s=(new De).retain(t.index).delete(n);if(e.offset>=i.length()-1){const[e]=this.quill.getLine(t.index+1);if(e){const n=i.formats(),o=this.quill.getFormat(t.index,1);r=De.AttributeMap.diff(n,o)||{},Object.keys(r).length>0&&(s=s.retain(e.length()-1).retain(1,r))}}this.quill.updateContents(s,Ir.sources.USER),this.quill.focus()}handleDeleteRange(t){ki({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const n=Object.keys(e.format).reduce(((t,n)=>(this.quill.scroll.query(n,ae.BLOCK)&&!Array.isArray(e.format[n])&&(t[n]=e.format[n]),t)),{}),r=(new De).retain(t.index).delete(t.length).insert("\n",n);this.quill.updateContents(r,Ir.sources.USER),this.quill.setSelection(t.index+1,Ir.sources.SILENT),this.quill.focus()}}const vi={bindings:{bold:xi("bold"),italic:xi("italic"),underline:xi("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","+1",Ir.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","-1",Ir.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(t,e){null!=e.format.indent?this.quill.format("indent","-1",Ir.sources.USER):null!=e.format.list&&this.quill.format("list",!1,Ir.sources.USER)}},"indent code-block":yi(!0),"outdent code-block":yi(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(t){this.quill.deleteText(t.index-1,1,Ir.sources.USER)}},tab:{key:"Tab",handler(t,e){if(e.format.table)return!0;this.quill.history.cutoff();const n=(new De).retain(t.index).delete(t.length).insert("\t");return this.quill.updateContents(n,Ir.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,Ir.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,Ir.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(t,e){const n={list:!1};e.format.indent&&(n.indent=!1),this.quill.formatLine(t.index,t.length,n,Ir.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(t){const[e,n]=this.quill.getLine(t.index),r={...e.formats(),list:"checked"},i=(new De).retain(t.index).insert("\n",r).retain(e.length()-n-1).retain(1,{list:"unchecked"});this.quill.updateContents(i,Ir.sources.USER),this.quill.setSelection(t.index+1,Ir.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(t,e){const[n,r]=this.quill.getLine(t.index),i=(new De).retain(t.index).insert("\n",e.format).retain(n.length()-r-1).retain(1,{header:null});this.quill.updateContents(i,Ir.sources.USER),this.quill.setSelection(t.index+1,Ir.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(t){const e=this.quill.getModule("table");if(e){const[n,r,i,s]=e.getTable(t),o=function(t,e,n,r){return null==e.prev&&null==e.next?null==n.prev&&null==n.next?0===r?-1:1:null==n.prev?-1:1:null==e.prev?-1:null==e.next?1:null}(0,r,i,s);if(null==o)return;let l=n.offset();if(o<0){const e=(new De).retain(l).insert("\n");this.quill.updateContents(e,Ir.sources.USER),this.quill.setSelection(t.index+1,t.length,Ir.sources.SILENT)}else if(o>0){l+=n.length();const t=(new De).retain(l).insert("\n");this.quill.updateContents(t,Ir.sources.USER),this.quill.setSelection(l,Ir.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(t,e){const{event:n,line:r}=e,i=r.offset(this.quill.scroll);n.shiftKey?this.quill.setSelection(i-1,Ir.sources.USER):this.quill.setSelection(i+r.length(),Ir.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(t,e){if(null==this.quill.scroll.query("list"))return!0;const{length:n}=e.prefix,[r,i]=this.quill.getLine(t.index);if(i>n)return!0;let s;switch(e.prefix.trim()){case"[]":case"[ ]":s="unchecked";break;case"[x]":s="checked";break;case"-":case"*":s="bullet";break;default:s="ordered"}this.quill.insertText(t.index," ",Ir.sources.USER),this.quill.history.cutoff();const o=(new De).retain(t.index-i).delete(n+1).retain(r.length()-2-i).retain(1,{list:s});return this.quill.updateContents(o,Ir.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-n,Ir.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(t){const[e,n]=this.quill.getLine(t.index);let r=2,i=e;for(;null!=i&&i.length()<=1&&i.formats()["code-block"];)if(i=i.prev,r-=1,r<=0){const r=(new De).retain(t.index+e.length()-n-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(r,Ir.sources.USER),this.quill.setSelection(t.index-1,Ir.sources.SILENT),!1}return!0}},"embed left":wi("ArrowLeft",!1),"embed left shift":wi("ArrowLeft",!0),"embed right":wi("ArrowRight",!1),"embed right shift":wi("ArrowRight",!0),"table down":Ni(!1),"table up":Ni(!0)}};function yi(t){return{key:"Tab",shiftKey:!t,format:{"code-block":!0},handler(e,n){let{event:r}=n;const i=this.quill.scroll.query("code-block"),{TAB:s}=i;if(0===e.length&&!r.shiftKey)return this.quill.insertText(e.index,s,Ir.sources.USER),void this.quill.setSelection(e.index+s.length,Ir.sources.SILENT);const o=0===e.length?this.quill.getLines(e.index,1):this.quill.getLines(e);let{index:l,length:a}=e;o.forEach(((e,n)=>{t?(e.insertAt(0,s),0===n?l+=s.length:a+=s.length):e.domNode.textContent.startsWith(s)&&(e.deleteAt(0,s.length),0===n?l-=s.length:a-=s.length)})),this.quill.update(Ir.sources.USER),this.quill.setSelection(l,a,Ir.sources.SILENT)}}}function wi(t,e){const n="ArrowLeft"===t?"prefix":"suffix";return{key:t,shiftKey:e,altKey:null,[n]:/^$/,handler(n){let{index:r}=n;"ArrowRight"===t&&(r+=n.length+1);const[i]=this.quill.getLeaf(r);return!(i instanceof Oe&&("ArrowLeft"===t?e?this.quill.setSelection(n.index-1,n.length+1,Ir.sources.USER):this.quill.setSelection(n.index-1,Ir.sources.USER):e?this.quill.setSelection(n.index,n.length+1,Ir.sources.USER):this.quill.setSelection(n.index+n.length+1,Ir.sources.USER),1))}}}function xi(t){return{key:t[0],shortKey:!0,handler(e,n){this.quill.format(t,!n.format[t],Ir.sources.USER)}}}function Ni(t){return{key:t?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(e,n){const r=t?"prev":"next",i=n.line,s=i.parent[r];if(null!=s){if("table-row"===s.statics.blotName){let t=s.children.head,e=i;for(;null!=e.prev;)e=e.prev,t=t.next;const r=t.offset(this.quill.scroll)+Math.min(n.offset,t.length()-1);this.quill.setSelection(r,0,Ir.sources.USER)}}else{const e=i.table()[r];null!=e&&(t?this.quill.setSelection(e.offset(this.quill.scroll)+e.length()-1,0,Ir.sources.USER):this.quill.setSelection(e.offset(this.quill.scroll),0,Ir.sources.USER))}return!1}}}function ki(t){let{quill:e,range:n}=t;const r=e.getLines(n);let i={};if(r.length>1){const t=r[0].formats(),e=r[r.length-1].formats();i=De.AttributeMap.diff(e,t)||{}}e.deleteText(n,Ir.sources.USER),Object.keys(i).length>0&&e.formatLine(n.index,1,i,Ir.sources.USER),e.setSelection(n.index,Ir.sources.SILENT)}mi.DEFAULTS=vi;const Ai=/font-weight:\s*normal/,_i=["P","OL","UL"],Ei=t=>t&&_i.includes(t.tagName),Ci=/\bmso-list:[^;]*ignore/i,Ti=/\bmso-list:[^;]*\bl(\d+)/i,qi=/\bmso-list:[^;]*\blevel(\d+)/i,Li=[function(t){"urn:schemas-microsoft-com:office:word"===t.documentElement.getAttribute("xmlns:w")&&(t=>{const e=Array.from(t.querySelectorAll("[style*=mso-list]")),n=[],r=[];e.forEach((t=>{(t.getAttribute("style")||"").match(Ci)?n.push(t):r.push(t)})),n.forEach((t=>t.parentNode?.removeChild(t)));const i=t.documentElement.innerHTML,s=r.map((t=>((t,e)=>{const n=t.getAttribute("style"),r=n?.match(Ti);if(!r)return null;const i=Number(r[1]),s=n?.match(qi),o=s?Number(s[1]):1,l=new RegExp(`@list l${i}:level${o}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),a=e.match(l);return{id:i,indent:o,type:a&&"bullet"===a[1]?"bullet":"ordered",element:t}})(t,i))).filter((t=>t));for(;s.length;){const t=[];let e=s.shift();for(;e;)t.push(e),e=s.length&&s[0]?.element===e.element.nextElementSibling&&s[0].id===e.id?s.shift():null;const n=document.createElement("ul");t.forEach((t=>{const e=document.createElement("li");e.setAttribute("data-list",t.type),t.indent>1&&e.setAttribute("class","ql-indent-"+(t.indent-1)),e.innerHTML=t.element.innerHTML,n.appendChild(e)}));const r=t[0]?.element,{parentNode:i}=r??{};r&&i?.replaceChild(n,r),t.slice(1).forEach((t=>{let{element:e}=t;i?.removeChild(e)}))}})(t)},function(t){t.querySelector('[id^="docs-internal-guid-"]')&&((t=>{Array.from(t.querySelectorAll('b[style*="font-weight"]')).filter((t=>t.getAttribute("style")?.match(Ai))).forEach((e=>{const n=t.createDocumentFragment();n.append(...e.childNodes),e.parentNode?.replaceChild(n,e)}))})(t),(t=>{Array.from(t.querySelectorAll("br")).filter((t=>Ei(t.previousElementSibling)&&Ei(t.nextElementSibling))).forEach((t=>{t.parentNode?.removeChild(t)}))})(t))}],Si=ur("quill:clipboard"),Oi=[[Node.TEXT_NODE,function(t,e,n){let r=t.data;if("O:P"===t.parentElement?.tagName)return e.insert(r.trim());if(!Pi(t)){if(0===r.trim().length&&r.includes("\n")&&!function(t,e){return t.previousElementSibling&&t.nextElementSibling&&!Ii(t.previousElementSibling,e)&&!Ii(t.nextElementSibling,e)}(t,n))return e;r=r.replace(/[^\S\u00a0]/g," "),r=r.replace(/ {2,}/g," "),(null==t.previousSibling&&null!=t.parentElement&&Ii(t.parentElement,n)||t.previousSibling instanceof Element&&Ii(t.previousSibling,n))&&(r=r.replace(/^ /,"")),(null==t.nextSibling&&null!=t.parentElement&&Ii(t.parentElement,n)||t.nextSibling instanceof Element&&Ii(t.nextSibling,n))&&(r=r.replace(/ $/,"")),r=r.replaceAll(" "," ")}return e.insert(r)}],[Node.TEXT_NODE,Hi],["br",function(t,e){return Ri(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,Hi],[Node.ELEMENT_NODE,function(t,e,n){const r=n.query(t);if(null==r)return e;if(r.prototype instanceof Oe){const e={},i=r.value(t);if(null!=i)return e[r.blotName]=i,(new De).insert(e,r.formats(t,n))}else if(r.prototype instanceof qe&&!Ri(e,"\n")&&e.insert("\n"),"blotName"in r&&"formats"in r&&"function"==typeof r.formats)return Bi(e,r.blotName,r.formats(t,n),n);return e}],[Node.ELEMENT_NODE,function(t,e,n){const r=ce.keys(t),i=pe.keys(t),s=be.keys(t),o={};return r.concat(i).concat(s).forEach((e=>{let r=n.query(e,ae.ATTRIBUTE);null!=r&&(o[r.attrName]=r.value(t),o[r.attrName])||(r=ji[e],null==r||r.attrName!==e&&r.keyName!==e||(o[r.attrName]=r.value(t)||void 0),r=Mi[e],null==r||r.attrName!==e&&r.keyName!==e||(r=Mi[e],o[r.attrName]=r.value(t)||void 0))})),Object.entries(o).reduce(((t,e)=>{let[r,i]=e;return Bi(t,r,i,n)}),e)}],[Node.ELEMENT_NODE,function(t,e,n){const r={},i=t.style||{};return"italic"===i.fontStyle&&(r.italic=!0),"underline"===i.textDecoration&&(r.underline=!0),"line-through"===i.textDecoration&&(r.strike=!0),(i.fontWeight?.startsWith("bold")||parseInt(i.fontWeight,10)>=700)&&(r.bold=!0),e=Object.entries(r).reduce(((t,e)=>{let[r,i]=e;return Bi(t,r,i,n)}),e),parseFloat(i.textIndent||0)>0?(new De).insert("\t").concat(e):e}],["li",function(t,e,n){const r=n.query(t);if(null==r||"list"!==r.blotName||!Ri(e,"\n"))return e;let i=-1,s=t.parentNode;for(;null!=s;)["OL","UL"].includes(s.tagName)&&(i+=1),s=s.parentNode;return i<=0?e:e.reduce(((t,e)=>e.insert?e.attributes&&"number"==typeof e.attributes.indent?t.push(e):t.insert(e.insert,{indent:i,...e.attributes||{}}):t),new De)}],["ol, ul",function(t,e,n){const r=t;let i="OL"===r.tagName?"ordered":"bullet";const s=r.getAttribute("data-checked");return s&&(i="true"===s?"checked":"unchecked"),Bi(e,"list",i,n)}],["pre",function(t,e,n){const r=n.query("code-block");return Bi(e,"code-block",!r||!("formats"in r)||"function"!=typeof r.formats||r.formats(t,n),n)}],["tr",function(t,e,n){const r="TABLE"===t.parentElement?.tagName?t.parentElement:t.parentElement?.parentElement;return null!=r?Bi(e,"table",Array.from(r.querySelectorAll("tr")).indexOf(t)+1,n):e}],["b",zi("bold")],["i",zi("italic")],["strike",zi("strike")],["style",function(){return new De}]],ji=[Zr,li].reduce(((t,e)=>(t[e.keyName]=e,t)),{}),Mi=[Xr,ni,ti,ci,di,pi].reduce(((t,e)=>(t[e.keyName]=e,t)),{});function Bi(t,e,n,r){return r.query(e)?t.reduce(((t,r)=>{if(!r.insert)return t;if(r.attributes&&r.attributes[e])return t.push(r);const i=n?{[e]:n}:{};return t.insert(r.insert,{...i,...r.attributes})}),new De):t}function Ri(t,e){let n="";for(let r=t.ops.length-1;r>=0&&n.length<e.length;--r){const e=t.ops[r];if("string"!=typeof e.insert)break;n=e.insert+n}return n.slice(-1*e.length)===e}function Ii(t,e){if(!(t instanceof Element))return!1;const n=e.query(t);return!(n&&n.prototype instanceof Oe)&&["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(t.tagName.toLowerCase())}const Di=new WeakMap;function Pi(t){return null!=t&&(Di.has(t)||("PRE"===t.tagName?Di.set(t,!0):Di.set(t,Pi(t.parentNode))),Di.get(t))}function Ui(t,e,n,r,i){return e.nodeType===e.TEXT_NODE?r.reduce(((n,r)=>r(e,n,t)),new De):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce(((s,o)=>{let l=Ui(t,o,n,r,i);return o.nodeType===e.ELEMENT_NODE&&(l=n.reduce(((e,n)=>n(o,e,t)),l),l=(i.get(o)||[]).reduce(((e,n)=>n(o,e,t)),l)),s.concat(l)}),new De):new De}function zi(t){return(e,n,r)=>Bi(n,t,!0,r)}function Hi(t,e,n){if(!Ri(e,"\n")){if(Ii(t,n)&&(t.childNodes.length>0||t instanceof HTMLParagraphElement))return e.insert("\n");if(e.length()>0&&t.nextSibling){let r=t.nextSibling;for(;null!=r;){if(Ii(r,n))return e.insert("\n");const t=n.query(r);if(t&&t.prototype instanceof Jn)return e.insert("\n");r=r.firstChild}}}return e}function Fi(t,e){let n=e;for(let e=t.length-1;e>=0;e-=1){const r=t[e];t[e]={delta:n.transform(r.delta,!0),range:r.range&&$i(r.range,n)},n=r.delta.transform(n),0===t[e].delta.length()&&t.splice(e,1)}}function $i(t,e){if(!t)return t;const n=e.transformPosition(t.index);return{index:n,length:e.transformPosition(t.index+t.length)-n}}class Vi extends _r{constructor(t,e){super(t,e),t.root.addEventListener("drop",(e=>{e.preventDefault();let n=null;if(document.caretRangeFromPoint)n=document.caretRangeFromPoint(e.clientX,e.clientY);else if(document.caretPositionFromPoint){const t=document.caretPositionFromPoint(e.clientX,e.clientY);n=document.createRange(),n.setStart(t.offsetNode,t.offset),n.setEnd(t.offsetNode,t.offset)}const r=n&&t.selection.normalizeNative(n);if(r){const n=t.selection.normalizedToRange(r);e.dataTransfer?.files&&this.upload(n,e.dataTransfer.files)}}))}upload(t,e){const n=[];Array.from(e).forEach((t=>{t&&this.options.mimetypes?.includes(t.type)&&n.push(t)})),n.length>0&&this.options.handler.call(this,t,n)}}Vi.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(t,e){if(!this.quill.scroll.query("image"))return;const n=e.map((t=>new Promise((e=>{const n=new FileReader;n.onload=()=>{e(n.result)},n.readAsDataURL(t)}))));Promise.all(n).then((e=>{const n=e.reduce(((t,e)=>t.insert({image:e})),(new De).retain(t.index).delete(t.length));this.quill.updateContents(n,dr.sources.USER),this.quill.setSelection(t.index+e.length,dr.sources.SILENT)}))}};const Wi=Vi,Ki=["insertText","insertReplacementText"],Gi=/Mac/i.test(navigator.platform);Ir.register({"blots/block":Qn,"blots/block/embed":Jn,"blots/break":Wn,"blots/container":$r,"blots/cursor":rr,"blots/embed":Cr,"blots/inline":Xn,"blots/scroll":class extends Be{static blotName="scroll";static className="ql-editor";static tagName="DIV";static defaultChild=Qn;static allowedChildren=[Qn,Jn,$r];constructor(t,e,n){let{emitter:r}=n;super(t,e),this.emitter=r,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",(t=>this.handleDragStart(t)))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(dr.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(dr.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(dr.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[n,r]=this.line(t),[i]=this.line(t+e);if(super.deleteAt(t,e),null!=i&&n!==i&&r>0){if(n instanceof Jn||i instanceof Jn)return void this.optimize();const t=i.children.head instanceof Wn?null:i.children.head;n.moveChildren(i,t),n.remove()}this.optimize()}enable(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,n,r){super.formatAt(t,e,n,r),this.optimize()}insertAt(t,e,n){if(t>=this.length())if(null==n||null==this.scroll.query(e,ae.BLOCK)){const t=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(t),null==n&&e.endsWith("\n")?t.insertAt(0,e.slice(0,-1),n):t.insertAt(0,e,n)}else{const t=this.scroll.create(e,n);this.appendChild(t)}else super.insertAt(t,e,n);this.optimize()}insertBefore(t,e){if(t.statics.scope===ae.INLINE_BLOT){const n=this.scroll.create(this.statics.defaultChild.blotName);n.appendChild(t),super.insertBefore(n,e)}else super.insertBefore(t,e)}insertContents(t,e){const n=this.deltaToRenderBlocks(e.concat((new De).insert("\n"))),r=n.pop();if(null==r)return;this.batchStart();const i=n.shift();if(i){const e="block"===i.type&&(0===i.delta.length()||!this.descendant(Jn,t)[0]&&t<this.length()),n="block"===i.type?i.delta:(new De).insert({[i.key]:i.value});Kr(this,t,n);const r="block"===i.type?1:0,s=t+n.length()+r;e&&this.insertAt(s-1,"\n");const o=er(this.line(t)[0]),l=De.AttributeMap.diff(o,i.attributes)||{};Object.keys(l).forEach((t=>{this.formatAt(s-1,1,t,l[t])})),t=s}let[s,o]=this.children.find(t);n.length&&(s&&(s=s.split(o),o=0),n.forEach((t=>{if("block"===t.type)Kr(this.createBlock(t.attributes,s||void 0),0,t.delta);else{const e=this.create(t.key,t.value);this.insertBefore(e,s||void 0),Object.keys(t.attributes).forEach((n=>{e.format(n,t.attributes[n])}))}}))),"block"===r.type&&r.delta.length()&&Kr(this,s?s.offset(s.scroll)+o:this.length(),r.delta),this.batchEnd(),this.optimize()}isEnabled(){return"true"===this.domNode.getAttribute("contenteditable")}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[n,r]=e;return n instanceof xe?[n,r]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(Vr,t)}lines(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;const n=(t,e,r)=>{let i=[],s=r;return t.children.forEachAt(e,r,((t,e,r)=>{Vr(t)?i.push(t):t instanceof Se&&(i=i.concat(n(t,e,s))),s-=r})),i};return n(this,t,e)}optimize(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit(dr.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch)return void(Array.isArray(t)&&(this.batch=this.batch.concat(t)));let e=dr.sources.USER;"string"==typeof t&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),(t=t.filter((t=>{let{target:e}=t;const n=this.find(e,!0);return n&&!Wr(n)}))).length>0&&this.emitter.emit(dr.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(dr.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,n){const[r]=this.descendant((t=>t instanceof Jn),t);r&&r.statics.blotName===e&&Wr(r)&&r.updateContent(n)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let n=new De;return t.forEach((t=>{const r=t?.insert;if(r)if("string"==typeof r){const i=r.split("\n");i.slice(0,-1).forEach((r=>{n.insert(r,t.attributes),e.push({type:"block",delta:n,attributes:t.attributes??{}}),n=new De}));const s=i[i.length-1];s&&n.insert(s,t.attributes)}else{const i=Object.keys(r)[0];if(!i)return;this.query(i,ae.INLINE)?n.push(t):(n.length()&&e.push({type:"block",delta:n,attributes:{}}),n=new De,e.push({type:"blockEmbed",key:i,value:r[i],attributes:t.attributes??{}}))}})),n.length()&&e.push({type:"block",delta:n,attributes:{}}),e}createBlock(t,e){let n;const r={};Object.entries(t).forEach((t=>{let[e,i]=t;null!=this.query(e,ae.BLOCK&ae.BLOT)?n=e:r[e]=i}));const i=this.create(n||this.statics.defaultChild.blotName,n?t[n]:void 0);this.insertBefore(i,e||void 0);const s=i.length();return Object.entries(r).forEach((t=>{let[e,n]=t;i.formatAt(0,s,e,n)})),i}},"blots/text":Kn,"modules/clipboard":class extends _r{static DEFAULTS={matchers:[]};constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",(t=>this.onCaptureCopy(t,!1))),this.quill.root.addEventListener("cut",(t=>this.onCaptureCopy(t,!0))),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Oi.concat(this.options.matchers??[]).forEach((t=>{let[e,n]=t;this.addMatcher(e,n)}))}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:n}=t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(r[ii.blotName])return(new De).insert(n||"",{[ii.blotName]:r[ii.blotName]});if(!e)return(new De).insert(n||"",r);const i=this.convertHTML(e);return Ri(i,"\n")&&(null==i.ops[i.ops.length-1].attributes||r.table)?i.compose((new De).retain(i.length()-1).delete(1)):i}normalizeHTML(t){(t=>{t.documentElement&&Li.forEach((e=>{e(t)}))})(t)}convertHTML(t){const e=(new DOMParser).parseFromString(t,"text/html");this.normalizeHTML(e);const n=e.body,r=new WeakMap,[i,s]=this.prepareMatching(n,r);return Ui(this.quill.scroll,n,i,s,r)}dangerouslyPasteHTML(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Ir.sources.API;if("string"==typeof t){const n=this.convert({html:t,text:""});this.quill.setContents(n,e),this.quill.setSelection(0,Ir.sources.SILENT)}else{const r=this.convert({html:e,text:""});this.quill.updateContents((new De).retain(t).concat(r),n),this.quill.setSelection(t+r.length(),Ir.sources.SILENT)}}onCaptureCopy(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.defaultPrevented)return;t.preventDefault();const[n]=this.quill.selection.getRange();if(null==n)return;const{html:r,text:i}=this.onCopy(n,e);t.clipboardData?.setData("text/plain",i),t.clipboardData?.setData("text/html",r),e&&ki({range:n,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter((t=>"#"!==t[0])).join("\n")}onCapturePaste(t){if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(null==e)return;const n=t.clipboardData?.getData("text/html");let r=t.clipboardData?.getData("text/plain");if(!n&&!r){const e=t.clipboardData?.getData("text/uri-list");e&&(r=this.normalizeURIList(e))}const i=Array.from(t.clipboardData?.files||[]);if(!n&&i.length>0)this.quill.uploader.upload(e,i);else{if(n&&i.length>0){const t=(new DOMParser).parseFromString(n,"text/html");if(1===t.body.childElementCount&&"IMG"===t.body.firstElementChild?.tagName)return void this.quill.uploader.upload(e,i)}this.onPaste(e,{html:n,text:r})}}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:n,html:r}=e;const i=this.quill.getFormat(t.index),s=this.convert({text:n,html:r},i);Si.log("onPaste",s,{text:n,html:r});const o=(new De).retain(t.index).delete(t.length).concat(s);this.quill.updateContents(o,Ir.sources.USER),this.quill.setSelection(o.length()-t.length,Ir.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const n=[],r=[];return this.matchers.forEach((i=>{const[s,o]=i;switch(s){case Node.TEXT_NODE:r.push(o);break;case Node.ELEMENT_NODE:n.push(o);break;default:Array.from(t.querySelectorAll(s)).forEach((t=>{if(e.has(t)){const n=e.get(t);n?.push(o)}else e.set(t,[o])}))}})),[n,r]}},"modules/history":class extends _r{static DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};lastRecorded=0;ignoreChange=!1;stack={undo:[],redo:[]};currentRange=null;constructor(t,e){super(t,e),this.quill.on(Ir.events.EDITOR_CHANGE,((t,e,n,r)=>{t===Ir.events.SELECTION_CHANGE?e&&r!==Ir.sources.SILENT&&(this.currentRange=e):t===Ir.events.TEXT_CHANGE&&(this.ignoreChange||(this.options.userOnly&&r!==Ir.sources.USER?this.transform(e):this.record(e,n)),this.currentRange=$i(this.currentRange,e))})),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",(t=>{"historyUndo"===t.inputType?(this.undo(),t.preventDefault()):"historyRedo"===t.inputType&&(this.redo(),t.preventDefault())}))}change(t,e){if(0===this.stack[t].length)return;const n=this.stack[t].pop();if(!n)return;const r=this.quill.getContents(),i=n.delta.invert(r);this.stack[e].push({delta:i,range:$i(n.range,i)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(n.delta,Ir.sources.USER),this.ignoreChange=!1,this.restoreSelection(n)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(t,e){if(0===t.ops.length)return;this.stack.redo=[];let n=t.invert(e),r=this.currentRange;const i=Date.now();if(this.lastRecorded+this.options.delay>i&&this.stack.undo.length>0){const t=this.stack.undo.pop();t&&(n=n.compose(t.delta),r=t.range)}else this.lastRecorded=i;0!==n.length()&&(this.stack.undo.push({delta:n,range:r}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(t){Fi(this.stack.undo,t),Fi(this.stack.redo,t)}undo(){this.change("undo","redo")}restoreSelection(t){if(t.range)this.quill.setSelection(t.range,Ir.sources.USER);else{const e=function(t,e){const n=e.reduce(((t,e)=>t+(e.delete||0)),0);let r=e.length()-n;return function(t,e){const n=e.ops[e.ops.length-1];return null!=n&&(null!=n.insert?"string"==typeof n.insert&&n.insert.endsWith("\n"):null!=n.attributes&&Object.keys(n.attributes).some((e=>null!=t.query(e,ae.BLOCK))))}(t,e)&&(r-=1),r}(this.quill.scroll,t.delta);this.quill.setSelection(e,Ir.sources.USER)}}},"modules/keyboard":mi,"modules/uploader":Wi,"modules/input":class extends _r{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",(t=>{this.handleBeforeInput(t)})),/Android/i.test(navigator.userAgent)||t.on(Ir.events.COMPOSITION_BEFORE_START,(()=>{this.handleCompositionStart()}))}deleteRange(t){ki({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(0===t.length)return!1;if(e){const n=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents((new De).retain(t.index).insert(e,n),Ir.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,Ir.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!Ki.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||!0===e.collapsed)return;const n=function(t){return"string"==typeof t.data?t.data:t.dataTransfer?.types.includes("text/plain")?t.dataTransfer.getData("text/plain"):null}(t);if(null==n)return;const r=this.quill.selection.normalizeNative(e),i=r?this.quill.selection.normalizedToRange(r):null;i&&this.replaceText(i,n)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}},"modules/uiNode":class extends _r{isListening=!1;selectionChangeDeadline=0;constructor(t,e){super(t,e),this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(t,e){let{line:n,event:r}=e;if(!(n instanceof _e&&n.uiNode))return!0;const i="rtl"===getComputedStyle(n.domNode).direction;return!!(i&&"ArrowRight"!==r.key||!i&&"ArrowLeft"!==r.key)||(this.quill.setSelection(t.index-1,t.length+(r.shiftKey?1:0),Ir.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",(t=>{!t.defaultPrevented&&(t=>"ArrowLeft"===t.key||"ArrowRight"===t.key||"ArrowUp"===t.key||"ArrowDown"===t.key||"Home"===t.key||!(!Gi||"a"!==t.key||!0!==t.ctrlKey))(t)&&this.ensureListeningToSelectionChange()}))}ensureListeningToSelectionChange(){this.selectionChangeDeadline=Date.now()+100,this.isListening||(this.isListening=!0,document.addEventListener("selectionchange",(()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()}),{once:!0}))}handleSelectionChange(){const t=document.getSelection();if(!t)return;const e=t.getRangeAt(0);if(!0!==e.collapsed||0!==e.startOffset)return;const n=this.quill.scroll.find(e.startContainer);if(!(n instanceof _e&&n.uiNode))return;const r=document.createRange();r.setStartAfter(n.uiNode),r.setEndAfter(n.uiNode),t.removeAllRanges(),t.addRange(r)}}});const Zi=Ir,Yi=new class extends pe{add(t,e){let n=0;if("+1"===e||"-1"===e){const r=this.value(t)||0;n="+1"===e?r+1:r-1}else"number"==typeof e&&(n=e);return 0===n?(this.remove(t),!0):super.add(t,n.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}("indent","ql-indent",{scope:ae.BLOCK,whitelist:[1,2,3,4,5,6,7,8]}),Xi=Yi;class Qi extends $r{}Qi.blotName="list-container",Qi.tagName="OL";class Ji extends Qn{static create(t){const e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){Ir.register(Qi)}constructor(t,e){super(t,e);const n=e.ownerDocument.createElement("span"),r=n=>{if(!t.isEnabled())return;const r=this.statics.formats(e,t);"checked"===r?(this.format("list","unchecked"),n.preventDefault()):"unchecked"===r&&(this.format("list","checked"),n.preventDefault())};n.addEventListener("mousedown",r),n.addEventListener("touchstart",r),this.attachUI(n)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}Ji.blotName="list",Ji.tagName="LI",Qi.allowedChildren=[Ji],Ji.requiredContainer=Qi;const ts=class extends Xn{static blotName="bold";static tagName=["STRONG","B"];static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}};class es extends Xn{static blotName="link";static tagName="A";static SANITIZED_URL="about:blank";static PROTOCOL_WHITELIST=["http","https","mailto","tel","sms"];static create(t){const e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return ns(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("href",this.constructor.sanitize(e)):super.format(t,e)}}function ns(t,e){const n=document.createElement("a");n.href=t;const r=n.href.slice(0,n.href.indexOf(":"));return e.indexOf(r)>-1}const rs=["alt","height","width"],is=["height","width"],ss=new pe("code-token","hljs",{scope:ae.INLINE});class os extends Xn{static formats(t,e){for(;null!=t&&t!==e.domNode;){if(t.classList&&t.classList.contains(ii.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,n){super(t,e,n),ss.add(this.domNode,n)}format(t,e){t!==os.blotName?super.format(t,e):e?ss.add(this.domNode,e):(ss.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),ss.value(this.domNode)||this.unwrap()}}os.blotName="code-token",os.className="ql-token";class ls extends ii{static create(t){const e=super.create(t);return"string"==typeof t&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),os.blotName,!1),super.replaceWith(t,e)}}class as extends ri{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===ls.blotName&&(this.forceNext=!0,this.children.forEach((n=>{n.format(t,e)})))}formatAt(t,e,n,r){n===ls.blotName&&(this.forceNext=!0),super.formatAt(t,e,n,r)}highlight(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==this.children.head)return;const n=`${Array.from(this.domNode.childNodes).filter((t=>t!==this.uiNode)).map((t=>t.textContent)).join("\n")}\n`,r=ls.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==n){if(n.trim().length>0||null==this.cachedText){const e=this.children.reduce(((t,e)=>t.concat(tr(e,!1))),new De),i=t(n,r);e.diff(i).reduce(((t,e)=>{let{retain:n,attributes:r}=e;return n?(r&&Object.keys(r).forEach((e=>{[ls.blotName,os.blotName].includes(e)&&this.formatAt(t,n,e,r[e])})),t+n):t}),0)}this.cachedText=n,this.forceNext=!1}}html(t,e){const[n]=this.children.find(t);return`<pre data-language="${n?ls.formats(n.domNode):"plain"}">\n${Zn(this.code(t,e))}\n</pre>`}optimize(t){if(super.optimize(t),null!=this.parent&&null!=this.children.head&&null!=this.uiNode){const t=ls.formats(this.children.head.domNode);t!==this.uiNode.value&&(this.uiNode.value=t)}}}as.allowedChildren=[ls],ls.requiredContainer=as,ls.allowedChildren=[os,rr,Kn,Wn];class cs extends _r{static register(){Ir.register(os,!0),Ir.register(ls,!0),Ir.register(as,!0)}constructor(t,e){if(super(t,e),null==this.options.hljs)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce(((t,e)=>{let{key:n}=e;return t[n]=!0,t}),{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(Ir.events.SCROLL_BLOT_MOUNT,(t=>{if(!(t instanceof as))return;const e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach((t=>{let{key:n,label:r}=t;const i=e.ownerDocument.createElement("option");i.textContent=r,i.setAttribute("value",n),e.appendChild(i)})),e.addEventListener("change",(()=>{t.format(ls.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)})),null==t.uiNode&&(t.attachUI(e),t.children.head&&(e.value=ls.formats(t.children.head.domNode)))}))}initTimer(){let t=null;this.quill.on(Ir.events.SCROLL_OPTIMIZE,(()=>{t&&clearTimeout(t),t=setTimeout((()=>{this.highlight(),t=null}),this.options.interval)}))}highlight(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this.quill.selection.composing)return;this.quill.update(Ir.sources.USER);const n=this.quill.getSelection();(null==t?this.quill.scroll.descendants(as):[t]).forEach((t=>{t.highlight(this.highlightBlot,e)})),this.quill.update(Ir.sources.SILENT),null!=n&&this.quill.setSelection(n,Ir.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"plain";if(e=this.languages[e]?e:"plain","plain"===e)return Zn(t).split("\n").reduce(((t,n,r)=>(0!==r&&t.insert("\n",{[ii.blotName]:e}),t.insert(n))),new De);const n=this.quill.root.ownerDocument.createElement("div");return n.classList.add(ii.className),n.innerHTML=((t,e,n)=>{if("string"==typeof t.versionString){const r=t.versionString.split(".")[0];if(parseInt(r,10)>=11)return t.highlight(n,{language:e}).value}return t.highlight(e,n).value})(this.options.hljs,e,t),Ui(this.quill.scroll,n,[(t,e)=>{const n=ss.value(t);return n?e.compose((new De).retain(e.length(),{[os.blotName]:n})):e}],[(t,n)=>t.data.split("\n").reduce(((t,n,r)=>(0!==r&&t.insert("\n",{[ii.blotName]:e}),t.insert(n))),n)],new WeakMap)}}cs.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};class us extends Qn{static blotName="table";static tagName="TD";static create(t){const e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",ps()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===us.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}}class hs extends $r{static blotName="table-row";static tagName="TR";checkMerge(){if(super.checkMerge()&&null!=this.next.children.head){const t=this.children.head.formats(),e=this.children.tail.formats(),n=this.next.children.head.formats(),r=this.next.children.tail.formats();return t.table===e.table&&t.table===n.table&&t.table===r.table}return!1}optimize(t){super.optimize(t),this.children.forEach((t=>{if(null==t.next)return;const e=t.formats(),n=t.next.formats();if(e.table!==n.table){const e=this.splitAfter(t);e&&e.optimize(),this.prev&&this.prev.optimize()}}))}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}class ds extends $r{static blotName="table-body";static tagName="TBODY"}class fs extends $r{static blotName="table-container";static tagName="TABLE";balanceCells(){const t=this.descendants(hs),e=t.reduce(((t,e)=>Math.max(e.children.length,t)),0);t.forEach((t=>{new Array(e-t.children.length).fill(0).forEach((()=>{let e;null!=t.children.head&&(e=us.formats(t.children.head.domNode));const n=this.scroll.create(us.blotName,e);t.appendChild(n),n.optimize()}))}))}cells(t){return this.rows().map((e=>e.children.at(t)))}deleteColumn(t){const[e]=this.descendant(ds);null!=e&&null!=e.children.head&&e.children.forEach((e=>{const n=e.children.at(t);null!=n&&n.remove()}))}insertColumn(t){const[e]=this.descendant(ds);null!=e&&null!=e.children.head&&e.children.forEach((e=>{const n=e.children.at(t),r=us.formats(e.children.head.domNode),i=this.scroll.create(us.blotName,r);e.insertBefore(i,n)}))}insertRow(t){const[e]=this.descendant(ds);if(null==e||null==e.children.head)return;const n=ps(),r=this.scroll.create(hs.blotName);e.children.head.children.forEach((()=>{const t=this.scroll.create(us.blotName,n);r.appendChild(t)}));const i=e.children.at(t);e.insertBefore(r,i)}rows(){const t=this.children.head;return null==t?[]:t.children.map((t=>t))}}function ps(){return`row-${Math.random().toString(36).slice(2,6)}`}fs.allowedChildren=[ds],ds.requiredContainer=fs,ds.allowedChildren=[hs],hs.requiredContainer=ds,hs.allowedChildren=[us],us.requiredContainer=hs;const gs=ur("quill:toolbar");class bs extends _r{constructor(t,e){if(super(t,e),Array.isArray(this.options.container)){const e=document.createElement("div");e.setAttribute("role","toolbar"),function(t,e){Array.isArray(e[0])||(e=[e]),e.forEach((e=>{const n=document.createElement("span");n.classList.add("ql-formats"),e.forEach((t=>{if("string"==typeof t)ms(n,t);else{const e=Object.keys(t)[0],r=t[e];Array.isArray(r)?function(t,e,n){const r=document.createElement("select");r.classList.add(`ql-${e}`),n.forEach((t=>{const e=document.createElement("option");!1!==t?e.setAttribute("value",String(t)):e.setAttribute("selected","selected"),r.appendChild(e)})),t.appendChild(r)}(n,e,r):ms(n,e,r)}})),t.appendChild(n)}))}(e,this.options.container),t.container?.parentNode?.insertBefore(e,t.container),this.container=e}else"string"==typeof this.options.container?this.container=document.querySelector(this.options.container):this.container=this.options.container;this.container instanceof HTMLElement?(this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach((t=>{const e=this.options.handlers?.[t];e&&this.addHandler(t,e)})),Array.from(this.container.querySelectorAll("button, select")).forEach((t=>{this.attach(t)})),this.quill.on(Ir.events.EDITOR_CHANGE,(()=>{const[t]=this.quill.selection.getRange();this.update(t)}))):gs.error("Container required for toolbar",this.options)}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find((t=>0===t.indexOf("ql-")));if(!e)return;if(e=e.slice(3),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null==this.handlers[e]&&null==this.quill.scroll.query(e))return void gs.warn("ignoring attaching to nonexistent format",e,t);const n="SELECT"===t.tagName?"change":"click";t.addEventListener(n,(n=>{let r;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;const e=t.options[t.selectedIndex];r=!e.hasAttribute("selected")&&(e.value||!1)}else r=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),n.preventDefault();this.quill.focus();const[i]=this.quill.selection.getRange();if(null!=this.handlers[e])this.handlers[e].call(this,r);else if(this.quill.scroll.query(e).prototype instanceof Oe){if(r=prompt(`Enter ${e}`),!r)return;this.quill.updateContents((new De).retain(i.index).delete(i.length).insert({[e]:r}),Ir.sources.USER)}else this.quill.format(e,r,Ir.sources.USER);this.update(i)})),this.controls.push([e,t])}update(t){const e=null==t?{}:this.quill.getFormat(t);this.controls.forEach((n=>{const[r,i]=n;if("SELECT"===i.tagName){let n=null;if(null==t)n=null;else if(null==e[r])n=i.querySelector("option[selected]");else if(!Array.isArray(e[r])){let t=e[r];"string"==typeof t&&(t=t.replace(/"/g,'\\"')),n=i.querySelector(`option[value="${t}"]`)}null==n?(i.value="",i.selectedIndex=-1):n.selected=!0}else if(null==t)i.classList.remove("ql-active"),i.setAttribute("aria-pressed","false");else if(i.hasAttribute("value")){const t=e[r],n=t===i.getAttribute("value")||null!=t&&t.toString()===i.getAttribute("value")||null==t&&!i.getAttribute("value");i.classList.toggle("ql-active",n),i.setAttribute("aria-pressed",n.toString())}else{const t=null!=e[r];i.classList.toggle("ql-active",t),i.setAttribute("aria-pressed",t.toString())}}))}}function ms(t,e,n){const r=document.createElement("button");r.setAttribute("type","button"),r.classList.add(`ql-${e}`),r.setAttribute("aria-pressed","false"),null!=n?(r.value=n,r.setAttribute("aria-label",`${e}: ${n}`)):r.setAttribute("aria-label",e),t.appendChild(r)}bs.DEFAULTS={},bs.DEFAULTS={container:null,handlers:{clean(){const t=this.quill.getSelection();if(null!=t)if(0===t.length){const t=this.quill.getFormat();Object.keys(t).forEach((t=>{null!=this.quill.scroll.query(t,ae.INLINE)&&this.quill.format(t,!1,Ir.sources.USER)}))}else this.quill.removeFormat(t.index,t.length,Ir.sources.USER)},direction(t){const{align:e}=this.quill.getFormat();"rtl"===t&&null==e?this.quill.format("align","right",Ir.sources.USER):t||"right"!==e||this.quill.format("align",!1,Ir.sources.USER),this.quill.format("direction",t,Ir.sources.USER)},indent(t){const e=this.quill.getSelection(),n=this.quill.getFormat(e),r=parseInt(n.indent||0,10);if("+1"===t||"-1"===t){let e="+1"===t?1:-1;"rtl"===n.direction&&(e*=-1),this.quill.format("indent",r+e,Ir.sources.USER)}},link(t){!0===t&&(t=prompt("Enter link URL:")),this.quill.format("link",t,Ir.sources.USER)},list(t){const e=this.quill.getSelection(),n=this.quill.getFormat(e);"check"===t?"checked"===n.list||"unchecked"===n.list?this.quill.format("list",!1,Ir.sources.USER):this.quill.format("list","unchecked",Ir.sources.USER):this.quill.format("list",t,Ir.sources.USER)}}};const vs='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',ys={align:{"":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',center:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',right:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',justify:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>'},background:'<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',blockquote:'<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',bold:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',clean:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',code:vs,"code-block":vs,color:'<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',direction:{"":'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',rtl:'<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>'},formula:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',header:{1:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',2:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',3:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',4:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',5:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',6:'<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>'},italic:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',image:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',indent:{"+1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',"-1":'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>'},link:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',list:{bullet:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',check:'<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',ordered:'<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>'},script:{sub:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',super:'<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>'},strike:'<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',table:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',underline:'<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',video:'<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>'};let ws=0;function xs(t,e){t.setAttribute(e,`${!("true"===t.getAttribute(e))}`)}const Ns=class{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",(()=>{this.togglePicker()})),this.label.addEventListener("keydown",(t=>{switch(t.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),t.preventDefault()}})),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),xs(this.label,"aria-expanded"),xs(this.options,"aria-hidden")}buildItem(t){const e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");const n=t.getAttribute("value");return n&&e.setAttribute("data-value",n),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",(()=>{this.selectItem(e,!0)})),e.addEventListener("keydown",(t=>{switch(t.key){case"Enter":this.selectItem(e,!0),t.preventDefault();break;case"Escape":this.escape(),t.preventDefault()}})),e}buildLabel(){const t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>',t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${ws}`,ws+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach((e=>{const n=this.buildItem(e);t.appendChild(n),!0===e.selected&&this.selectItem(n)})),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach((t=>{this.container.setAttribute(t.name,t.value)})),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout((()=>this.label.focus()),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.container.querySelector(".ql-selected");t!==n&&(null!=n&&n.classList.remove("ql-selected"),null!=t&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const e=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(e)}else this.selectItem(null);const e=null!=t&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}},ks=class extends Ns{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach((t=>{t.classList.add("ql-primary")}))}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);const n=this.label.querySelector(".ql-color-label"),r=t&&t.getAttribute("data-value")||"";n&&("line"===n.tagName?n.style.stroke=r:n.style.fill=r)}},As=class extends Ns{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach((t=>{t.innerHTML=e[t.getAttribute("data-value")||""]})),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const n=t||this.defaultItem;if(null!=n){if(this.label.innerHTML===n.innerHTML)return;this.label.innerHTML=n.innerHTML}}},_s=class{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,(t=>{const{overflowY:e}=getComputedStyle(t,null);return"visible"!==e&&"clip"!==e})(this.quill.root)&&this.quill.root.addEventListener("scroll",(()=>{this.root.style.marginTop=-1*this.quill.root.scrollTop+"px"})),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,n=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${n}px`,this.root.classList.remove("ql-flip");const r=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect();let s=0;if(i.right>r.right&&(s=r.right-i.right,this.root.style.left=`${e+s}px`),i.left<r.left&&(s=r.left-i.left,this.root.style.left=`${e+s}px`),i.bottom>r.bottom){const e=i.bottom-i.top,r=t.bottom-t.top+e;this.root.style.top=n-r+"px",this.root.classList.add("ql-flip")}return s}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}},Es=[!1,"center","right","justify"],Cs=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],Ts=[!1,"serif","monospace"],qs=["1","2","3",!1],Ls=["small",!1,"large","huge"];class Ss extends Lr{constructor(t,e){super(t,e);const n=e=>{document.body.contains(t.root)?(null==this.tooltip||this.tooltip.root.contains(e.target)||document.activeElement===this.tooltip.textbox||this.quill.hasFocus()||this.tooltip.hide(),null!=this.pickers&&this.pickers.forEach((t=>{t.container.contains(e.target)||t.close()}))):document.body.removeEventListener("click",n)};t.emitter.listenDOM("click",document.body,n)}addModule(t){const e=super.addModule(t);return"toolbar"===t&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach((t=>{(t.getAttribute("class")||"").split(/\s+/).forEach((n=>{if(n.startsWith("ql-")&&(n=n.slice(3),null!=e[n]))if("direction"===n)t.innerHTML=e[n][""]+e[n].rtl;else if("string"==typeof e[n])t.innerHTML=e[n];else{const r=t.value||"";null!=r&&e[n][r]&&(t.innerHTML=e[n][r])}}))}))}buildPickers(t,e){this.pickers=Array.from(t).map((t=>{if(t.classList.contains("ql-align")&&(null==t.querySelector("option")&&js(t,Es),"object"==typeof e.align))return new As(t,e.align);if(t.classList.contains("ql-background")||t.classList.contains("ql-color")){const n=t.classList.contains("ql-background")?"background":"color";return null==t.querySelector("option")&&js(t,Cs,"background"===n?"#ffffff":"#000000"),new ks(t,e[n])}return null==t.querySelector("option")&&(t.classList.contains("ql-font")?js(t,Ts):t.classList.contains("ql-header")?js(t,qs):t.classList.contains("ql-size")&&js(t,Ls)),new Ns(t)})),this.quill.on(dr.events.EDITOR_CHANGE,(()=>{this.pickers.forEach((t=>{t.update()}))}))}}Ss.DEFAULTS=oe({},Lr.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let t=this.container.querySelector("input.ql-image[type=file]");null==t&&(t=document.createElement("input"),t.setAttribute("type","file"),t.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),t.classList.add("ql-image"),t.addEventListener("change",(()=>{const e=this.quill.getSelection(!0);this.quill.uploader.upload(e,t.files),t.value=""})),this.container.appendChild(t)),t.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class Os extends _s{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",(t=>{"Enter"===t.key?(this.save(),t.preventDefault()):"Escape"===t.key&&(this.cancel(),t.preventDefault())}))}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null==this.textbox)return;null!=e?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const n=this.quill.getBounds(this.quill.selection.savedRange);null!=n&&this.position(n),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,dr.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,dr.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=function(t){let e=t.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||t.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return e?`${e[1]||"https"}://www.youtube.com/embed/${e[2]}?showinfo=0`:(e=t.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${e[1]||"https"}://player.vimeo.com/video/${e[2]}/`:t}(t);case"formula":{if(!t)break;const e=this.quill.getSelection(!0);if(null!=e){const n=e.index+e.length;this.quill.insertEmbed(n,this.root.getAttribute("data-mode"),t,dr.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(n+1," ",dr.sources.USER),this.quill.setSelection(n+2,dr.sources.USER)}break}}this.textbox.value="",this.hide()}}function js(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach((e=>{const r=document.createElement("option");e===n?r.setAttribute("selected","selected"):r.setAttribute("value",String(e)),t.appendChild(r)}))}const Ms=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class Bs extends Os{static TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join("");constructor(t,e){super(t,e),this.quill.on(dr.events.EDITOR_CHANGE,((t,e,n,r)=>{if(t===dr.events.SELECTION_CHANGE)if(null!=e&&e.length>0&&r===dr.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const t=this.quill.getLines(e.index,e.length);if(1===t.length){const t=this.quill.getBounds(e);null!=t&&this.position(t)}else{const n=t[t.length-1],r=this.quill.getIndex(n),i=Math.min(n.length()-1,e.index+e.length-r),s=this.quill.getBounds(new pr(r,i));null!=s&&this.position(s)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()}))}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",(()=>{this.root.classList.remove("ql-editing")})),this.quill.on(dr.events.SCROLL_OPTIMIZE,(()=>{setTimeout((()=>{if(this.root.classList.contains("ql-hidden"))return;const t=this.quill.getSelection();if(null!=t){const e=this.quill.getBounds(t);null!=e&&this.position(e)}}),1)}))}cancel(){this.show()}position(t){const e=super.position(t),n=this.root.querySelector(".ql-tooltip-arrow");return n.style.marginLeft="",0!==e&&(n.style.marginLeft=-1*e-n.offsetWidth/2+"px"),e}}class Rs extends Ss{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=Ms),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new Bs(this.quill,this.options.bounds),null!=t.container&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),ys),this.buildPickers(t.container.querySelectorAll("select"),ys))}}Rs.DEFAULTS=oe({},Ss.DEFAULTS,{modules:{toolbar:{handlers:{link(t){t?this.quill.theme.tooltip.edit():this.quill.format("link",!1,Ir.sources.USER)}}}}});const Is=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class Ds extends Os{static TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join("");preview=this.root.querySelector("a.ql-preview");listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",(t=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),t.preventDefault()})),this.root.querySelector("a.ql-remove").addEventListener("click",(t=>{if(null!=this.linkRange){const t=this.linkRange;this.restoreFocus(),this.quill.formatText(t,"link",!1,dr.sources.USER),delete this.linkRange}t.preventDefault(),this.hide()})),this.quill.on(dr.events.SELECTION_CHANGE,((t,e,n)=>{if(null!=t){if(0===t.length&&n===dr.sources.USER){const[e,n]=this.quill.scroll.descendant(es,t.index);if(null!=e){this.linkRange=new pr(t.index-n,e.length());const r=es.formats(e.domNode);this.preview.textContent=r,this.preview.setAttribute("href",r),this.show();const i=this.quill.getBounds(this.linkRange);return void(null!=i&&this.position(i))}}else delete this.linkRange;this.hide()}}))}show(){super.show(),this.root.removeAttribute("data-mode")}}class Ps extends Ss{constructor(t,e){null!=e.modules.toolbar&&null==e.modules.toolbar.container&&(e.modules.toolbar.container=Is),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){null!=t.container&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),ys),this.buildPickers(t.container.querySelectorAll("select"),ys),this.tooltip=new Ds(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},((e,n)=>{t.handlers.link.call(t,!n.format.link)})))}}Ps.DEFAULTS=oe({},Ss.DEFAULTS,{modules:{toolbar:{handlers:{link(t){if(t){const t=this.quill.getSelection();if(null==t||0===t.length)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&0!==e.indexOf("mailto:")&&(e=`mailto:${e}`);const{tooltip:n}=this.quill.theme;n.edit("link",e)}else this.quill.format("link",!1,Ir.sources.USER)}}}}});const Us=Ps;Zi.register({"attributors/attribute/direction":li,"attributors/class/align":Yr,"attributors/class/background":ei,"attributors/class/color":Jr,"attributors/class/direction":ai,"attributors/class/font":hi,"attributors/class/size":fi,"attributors/style/align":Xr,"attributors/style/background":ni,"attributors/style/color":ti,"attributors/style/direction":ci,"attributors/style/font":di,"attributors/style/size":pi},!0),Zi.register({"formats/align":Yr,"formats/direction":ai,"formats/indent":Xi,"formats/background":ni,"formats/color":ti,"formats/font":hi,"formats/size":fi,"formats/blockquote":class extends Qn{static blotName="blockquote";static tagName="blockquote"},"formats/code-block":ii,"formats/header":class extends Qn{static blotName="header";static tagName=["H1","H2","H3","H4","H5","H6"];static formats(t){return this.tagName.indexOf(t.tagName)+1}},"formats/list":Ji,"formats/bold":ts,"formats/code":si,"formats/italic":class extends ts{static blotName="italic";static tagName=["EM","I"]},"formats/link":es,"formats/script":class extends Xn{static blotName="script";static tagName=["SUB","SUP"];static create(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):super.create(t)}static formats(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}},"formats/strike":class extends ts{static blotName="strike";static tagName=["S","STRIKE"]},"formats/underline":class extends Xn{static blotName="underline";static tagName="U"},"formats/formula":class extends Cr{static blotName="formula";static className="ql-formula";static tagName="SPAN";static create(t){if(null==window.katex)throw new Error("Formula module requires KaTeX.");const e=super.create(t);return"string"==typeof t&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}},"formats/image":class extends Oe{static blotName="image";static tagName="IMG";static create(t){const e=super.create(t);return"string"==typeof t&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return rs.reduce(((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e)),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return ns(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){rs.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}},"formats/video":class extends Jn{static blotName="video";static className="ql-video";static tagName="IFRAME";static create(t){const e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return is.reduce(((e,n)=>(t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e)),{})}static sanitize(t){return es.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){is.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}},"modules/syntax":cs,"modules/table":class extends _r{static register(){Ir.register(us),Ir.register(hs),Ir.register(ds),Ir.register(fs)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(fs).forEach((t=>{t.balanceCells()}))}deleteColumn(){const[t,,e]=this.getTable();null!=e&&(t.deleteColumn(e.cellOffset()),this.quill.update(Ir.sources.USER))}deleteRow(){const[,t]=this.getTable();null!=t&&(t.remove(),this.quill.update(Ir.sources.USER))}deleteTable(){const[t]=this.getTable();if(null==t)return;const e=t.offset();t.remove(),this.quill.update(Ir.sources.USER),this.quill.setSelection(e,Ir.sources.SILENT)}getTable(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.quill.getSelection();if(null==t)return[null,null,null,-1];const[e,n]=this.quill.getLine(t.index);if(null==e||e.statics.blotName!==us.blotName)return[null,null,null,-1];const r=e.parent;return[r.parent.parent,r,e,n]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[n,r,i]=this.getTable(e);if(null==i)return;const s=i.cellOffset();n.insertColumn(s+t),this.quill.update(Ir.sources.USER);let o=r.rowOffset();0===t&&(o+=1),this.quill.setSelection(e.index+o,e.length,Ir.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[n,r,i]=this.getTable(e);if(null==i)return;const s=r.rowOffset();n.insertRow(s+t),this.quill.update(Ir.sources.USER),t>0?this.quill.setSelection(e,Ir.sources.SILENT):this.quill.setSelection(e.index+r.children.length,e.length,Ir.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const n=this.quill.getSelection();if(null==n)return;const r=new Array(t).fill(0).reduce((t=>{const n=new Array(e).fill("\n").join("");return t.insert(n,{table:ps()})}),(new De).retain(n.index));this.quill.updateContents(r,Ir.sources.USER),this.quill.setSelection(n.index,Ir.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(Ir.events.SCROLL_OPTIMIZE,(t=>{t.some((t=>!!["TD","TR","TBODY","TABLE"].includes(t.target.tagName)&&(this.quill.once(Ir.events.TEXT_CHANGE,((t,e,n)=>{n===Ir.sources.USER&&this.balanceTables()})),!0)))}))}},"modules/toolbar":bs,"themes/bubble":Rs,"themes/snow":Us,"ui/icons":ys,"ui/picker":Ns,"ui/icon-picker":As,"ui/color-picker":ks,"ui/tooltip":_s},!0);const zs=Zi}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var s=e[r]={id:r,loaded:!1,exports:{}};return t[r](s,s.exports,n),s.loaded=!0,s.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nmd=t=>(t.paths=[],t.children||(t.children=[]),t),(()=>{"use strict";var t=function(){var t=this._self._c;return t("div",{staticClass:"quill-editor-container"},[t("div",{ref:"editor",style:{minHeight:this.editorHeight}})])};t._withStripped=!0;var e=n(690),r=(n(578),n(568)),i=n.n(r);n(371);const s={name:"QuillEditor",props:{value:{type:String,default:""}},data:()=>({quill:null,editorHeight:"60px"}),watch:{value(t){this.quill&&t!==this.quill.root.innerHTML&&(this.quill.root.innerHTML=t||"<p><br></p>")}},mounted(){this.initQuill()},methods:{initQuill(){e.default.register({"modules/table-better":i()},!0),this.quill=new e.default(this.$refs.editor,{theme:"snow",placeholder:"请输入",modules:{table:!1,"table-better":{language:"zh_CN",menus:["column","row","merge","table","cell","wrap","delete"],toolbarTable:!0},keyboard:{bindings:i().keyboardBindings},toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],["table-better"],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["clean"],["link","image","video"]]}}),this.quill.root.innerHTML=this.value||"<p><br></p>",this.quill.on("text-change",(()=>{const t=this.quill.root.innerHTML;this.$emit("input",t)}))}}};n(391);var o=function(t,e,n,r,i,s,o,l){var a,c="function"==typeof t?t.options:t;if(e&&(c.render=e,c.staticRenderFns=[],c._compiled=!0),s&&(c._scopeId="data-v-"+s),a)if(c.functional){c._injectStyles=a;var u=c.render;c.render=function(t,e){return a.call(e),u(t,e)}}else{var h=c.beforeCreate;c.beforeCreate=h?[].concat(h,a):[a]}return{exports:t,options:c}}(s,t,0,0,0,"f5d53694");const l=o.exports;window.vueQuill={install(t,e){t.component("vue-quill",l)}}})()})();