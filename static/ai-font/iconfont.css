@font-face {
  font-family: "iconfont"; /* Project id 4859952 */
  src: url('iconfont.woff2?t=1742178559928') format('woff2'),
       url('iconfont.woff?t=1742178559928') format('woff'),
       url('iconfont.ttf?t=1742178559928') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-bucket:before {
  content: "\e6c1";
}

.icon-neicun1:before {
  content: "\e600";
}

.icon-fuzhi1:before {
  content: "\e910f";
}

.icon-unlike-o:before {
  content: "\e96e";
}

.icon-zhishiku:before {
  content: "\e608";
}

.icon-GPUjiedian:before {
  content: "\ed25";
}

.icon-xiancun:before {
  content: "\ed24";
}

.icon-vGPU:before {
  content: "\ed23";
}

.icon-suanli:before {
  content: "\ed22";
}

.icon-xianka:before {
  content: "\ed21";
}

.icon-table-list:before {
  content: "\f609";
}

.icon-card-list:before {
  content: "\f608";
}

