<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="/global/static/global-css/red-theme.css">
  <link rel="stylesheet" href="/global/static/global-css/index.css">
  <link rel="stylesheet" href="/global/static/cloud-icon/iconfont.css">
  <link rel="stylesheet" href="/global/static/plugins/font-sugon/iconfont.css">
  <link rel="stylesheet" href="/global/static/iconfont2/iconfont.css">
  <link rel="stylesheet" href="/global/static/iconfont/iconfont.css">
  <link rel="stylesheet" href="/global/static/theme-chalk/index.css">
  <link rel="stylesheet" href="/global/static/css/index.css">
  <link rel="stylesheet" href="/global/static/css/cloudScss.css">
  <link rel="stylesheet" href="./static/ai-font/iconfont.css">
  <link rel="stylesheet" href="./static//quill.css">
  <title></title>
</head>

<body>
  <script src="/global/static/plugins/init.js"></script>
  <script src="/global/static/plugins/jsonp.js"></script>
  <script src="/global/static/plugins/axios.js"></script>
  <script src="/global/static/ecs-plugin/cloud-base-http.js"></script>
  <script src="/global/static/variable/global.js"></script>
  <script src="/global/static/plugins/echarts.min.js"></script>
  <script src="/global/static/js/index.js"></script>
  <script src="./static/quill.js"></script>
  <!-- <script>
    initVue.cliInit('', './global/projectConfig.json')
  </script> -->
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>