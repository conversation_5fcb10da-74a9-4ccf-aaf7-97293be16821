{"name": "sugon-workweb", "version": "1.0.0", "description": "A Vue.js project", "author": "liwuyao <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "e2e": "node test/e2e/runner.js", "test": "npm run e2e", "build": "node build/build.js", "template": "node ./dev-tool-web/index.js"}, "dependencies": {"@highlightjs/vue-plugin": "^2.1.0", "@microsoft/fetch-event-source": "^2.0.1", "art-template": "^4.13.2", "axios": "^1.7.9", "body-parser": "^1.20.0", "codemirror": "^5.65.19", "cookie-parser": "^1.4.6", "diff-match-patch": "^1.0.0", "express": "^4.18.1", "highlight.js": "^11.11.1", "jsencrypt": "^3.1.0", "lib-flexible": "^0.3.2", "markdown-it": "^12.3.2", "markdown-it-abbr": "^1.0.4", "markdown-it-anchor": "^8.4.1", "markdown-it-deflist": "^2.1.0", "markdown-it-emoji": "^2.0.0", "markdown-it-footnote": "^3.0.3", "markdown-it-highlightjs": "^4.2.0", "markdown-it-ins": "^3.0.1", "markdown-it-katex": "^2.0.3", "markdown-it-mark": "^3.0.1", "markdown-it-sub": "^1.0.0", "markdown-it-sup": "^1.0.0", "markdown-it-task-lists": "^2.1.1", "markdown-it-toc-done-right": "^4.2.0", "moment": "^2.29.1", "multer": "^1.4.5-lts.1", "quill": "^2.0.3", "quill-table": "^1.0.0", "quill-table-ui": "^1.0.7", "tar": "^7.4.3", "vue": "^2.5.2", "vue-codemirror": "^4.0.6", "vue-highlightjs": "^1.3.3", "vue-json-viewer": "^2.2.22", "vuex": "^3.6.2"}, "devDependencies": {"address": "^1.1.2", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.0.1", "chromedriver": "^2.27.2", "copy-webpack-plugin": "^4.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "nightwatch": "^0.9.12", "node-notifier": "^5.1.2", "node-sass": "^4.13.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "px2rem-loader": "^0.1.9", "rimraf": "^2.6.0", "sass-loader": "^7.1.0", "sass-resources-loader": "^2.0.1", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}