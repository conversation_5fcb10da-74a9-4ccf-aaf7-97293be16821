
# node 版本
运行使用 14.21.3

# 主题色，涉及到主题色的都需要用变量，因为有变换主题功能

color: var(--color-theme);

# 模板生成工具地址,需要成都vpn

http://172.22.5.142:8600/

使用的时候先导入swagger的json数据。可以从页面版的swagger请求地址中寻找，也可以通过apifox导出。需要2.0版本。设计模式为dom树结构插入，所以需要先选择标签插入的标签。生成的代码可能需要一点代码修改。主要是列表的。列表目前操作生成了字符串，应该是jsx，先自行修改吧目前没时间去改工具，另外列表需要加上no-data事件，参考项目template下的page-list.vue。

# 登录账号密码

admin/keystone_sugon

# 代码提交
先build项目；
先git提交，git提交完成后，执行node publish
jenkins http://172.22.5.34:9997/job/sugoncloud-all-web-develop/    账号admin/admin

# 原型
https://ig10se.axshare.com/#id=yjiu94&p=%E6%95%B0%E6%8D%AE%E9%9B%86%E8%AF%A6%E6%83%85

# 接口mock地址

https://app.apifox.com/invite?token=gNutUnnlufiiqega-8OSg

# swwagger
http://172.22.1.57:30502/doc.html#/sugoncloud-xiaolian-api/%E6%A8%A1%E5%9E%8B%E5%B9%BF%E5%9C%BA/getModelListUsingGET

# ui设计图

https://www.figma.com/design/OjCBIF9CAS3OolwESuDWv0/%E6%9B%99%E5%85%89%E4%BA%91%E7%B3%BB%E7%BB%9F-%E4%BA%A7%E5%93%81%E5%90%88%E9%9B%86?node-id=1574-634&t=r0ffA6giEYk3xMRV-1

# 字典说明
参数为对象{
    type:''，//传字典type,返回同类数组
    code:'',//传字典code,返回对应对象。用在文字翻译
}
this.$getCode({type:'manufacturer'});

