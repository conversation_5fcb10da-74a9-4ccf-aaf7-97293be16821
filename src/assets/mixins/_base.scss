$inline-block-alignment: middle !default;//设置一个默认的对齐方式 
// 提供一个跨浏览器的方法来实现`display:inline-block` 
@mixin inline-block($alignment: $inline-block-alignment) { 
    display: inline-block; 
    @if $alignment and $alignment != none { 
        vertical-align: $alignment; 
    }
}
//水平居中
@mixin horizontal-center { 
    margin-left: auto;
    margin-right: auto; 
}
//水平居中（用于具有相同属性的代码合并）
%horizontal-center {
    @include horizontal-center;
}
//浮动与重置浮动
@mixin float($side:left){
    float: unquote($side);
}
@mixin reset-float($display:block){
    float: none;
    display: $display;
}
//隐藏元素

//1.浏览器和屏幕阅读器都隐藏元素
@mixin hidden {
    display: none !important;
    visibility: hidden;
}
%hidden {
    @include hidden;
} 

//2.仅浏览器隐藏，屏幕阅读器不被隐藏
@mixin visuallyhidden {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px; margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    
    ////扩展了`.visuallyhidden`，充许`.focusable`元素通过键盘获取
    &.focusable:active, &.focusable:focus {
        clip: auto;
        height: auto;
        margin: 0;
        overflow: visible;
        position: static;
        width: auto;
    }
}
%visuallyhidden {
    @include visuallyhidden;
}

//3.隐藏元素，但布局中占有空间 
@mixin invisible {
    visibility: hidden;
}
%invisible {
    @include invisible;
}

//截取文本
@mixin singleline-ellipsis($substract:0){//`$substract`单位为%
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100% - $substract;
}
%singleline-ellipsis {
    @include singleline-ellipsis;
}

//透明度
@mixin opacity($opacity:50) {
    opacity: $opacity / 100;
}

//禁用样式
@mixin disabled($bgColor: #e6e6e6,$textColor:#ababab){
    background-color: $bgColor !important;
    color: $textColor !important;
    cursor: not-allowed !important;
}

//最小高度
@mixin min-height($height){
    min-height: $height;
    height: auto !important;
}

//三角箭头
@mixin arrow($direction,$size,$color) {
    width: 0;
    height: 0;
    line-height: 0;
    font-size: 0;
    overflow: hidden;
    border-width: $size;
    cursor: pointer;
    @if $direction == top {
        border-style: dashed dashed solid dashed;
        border-color: transparent transparent $color transparent;
        border-top: none;
    }
    @else if $direction == bottom {
        border-style: solid dashed dashed dashed;
        border-color: $color transparent transparent transparent;
        border-bottom: none;
    }
    @else if $direction == right {
        border-style: dashed dashed dashed solid;
        border-color: transparent transparent transparent $color;
        border-right: none;
    }
    @else if $direction == left {
        border-style: dashed solid dashed dashed;
        border-color: transparent $color transparent transparent;
        border-left: none;
    }
}

//浏览器前缀
@mixin css3($property, $value) {
    @each $prefix in -webkit-, -moz-, -ms-, -o-, '' {
        #{$prefix}#{$property}: $value;
    }
}

//Retina图片
@mixin image-2x($image, $width, $height) {
    @media (min--moz-device-pixel-ratio: 1.3),
           (-o-min-device-pixel-ratio: 2.6/2),
           (-webkit-min-device-pixel-ratio: 1.3),
           (min-device-pixel-ratio: 1.3),
           (min-resolution: 1.3dppx) {
                background-image: url($image);
                background-size: $width $height;
            }
}

//绝对定位
@mixin abs-pos ($top: auto, $right: auto, $bottom: auto, $left: auto) {
    top: $top;
    right: $right;
    bottom: $bottom;
    left: $left;
    position: absolute;
}