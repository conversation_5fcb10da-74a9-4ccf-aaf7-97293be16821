html, body {
  height: 100%;
  width: 100%;
  margin: 0;
}
body {
  font-size: 12px;
}
a {
  color: #A5AAAE;
}

.row-center{
  display: flex;
  justify-content: center;
  align-items: center;
}
.topbar{
  background-color: #2B303A;
  height: 50px;
  width: 100%;
  left: 0;
  top: 0;
  position: fixed;
  z-index: 999!important;
}
.sidebar{
  /* position: relative; */
  /* padding-top: 50px; */
  height: 100%;
  width: 50px;
  z-index: 998!important;
  /*background-color: #252a2f;*/
  background-color: #2b303a;
}
.sidebar .sugon-sidebar-wrapper{
  overflow: hidden;
  position: relative;
  z-index: 997;
  width: 50px; /* 控制宽度 */
  top: 0;
  bottom: 0;
  background-color: #2B303A;
  -webkit-transition: all .3s cubic-bezier(0,.2,1);
  transition: all .3s cubic-bezier(0,0,.2,1);
}
.sidebar .sugon-sidebar-wrapper:hover{
  cursor: pointer;
  width: 230px;
}
.sidebar .product-all {
  padding: 4px 0;
  border-top: 1px solid hsla(0,0%,100%,.1);
  border-bottom: 1px solid hsla(0,0%,100%,.1);
  width: 100%;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
}
.sidebar .product-all .product-all-wrapper {
  line-height: 40px;
  height: 40px;
  font-size: 0;
}
.sidebar .product-all:hover .product-all-wrapper {
  /*background: #00c1de;*/
  /*background: #142337;*/
}
/*.sidebar .product-all:hover .sidebar-icon {
  color: #fff;
}*/
.sidebar .product-all .product-all-icon-box {
  width: 50px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  color: #A5AAAE;
}
.sidebar .product-all .product-all-name {
  color: #A5AAAE;
  font-size: 14px;
  display: inline-block;
  vertical-align: middle;
  width: 178px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.sidebar .sidebar-products {
  height: calc(100% - 48px);
  width: calc(100% + 20px);
  overflow-y: auto;
  position: relative;
}
.sidebar .product-item {
  color: #A5AAAE;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  -webkit-transition: all .2s ease-out 0s;
  transition: all .2s ease-out 0s;
  width: 100%;
  height: 40px;
  line-height: 40px;
}
.sidebar .product-item.product-item-active {
  background: #262A33;
}
/*.sidebar-icon{
  color: hsla(0,0%,100%,.65);
}*/
.sidebar .product-item:hover i {
  /*color: #3AADFC;*/
}
.sidebar .product-item:hover .product-item-name {
  color: #3AADFC;
}
.sidebar .product-item .product-item-icon-box {
  width: 50px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
}
.sidebar .product-item.product-item-active .product-item-icon-box i{
  color: #3AADFC!important;
}
.sidebar .product-item-link .product-item-name {
  display: inline-block;
  vertical-align: middle;
  width: 178px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
  padding-right: 8px;
}
.sidebar .sugon-sidebar-toolbar {
  width: 50px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: hsla(0,0%,100%,.65);
  margin-left: -50px;
  font-size: 12px;
}
.sidebar .sugon-sidebar-toolbar .sidebar-icon-box {
  display: inline-block;
  width: 25px;
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-size: 12px;
}
.sidebar .product-item.product-item-active .product-item-name {
  color: #3AADFC!important;
}
.viewFramework-body {
  position: absolute;
  width: 100%;
  top: 50px;
  bottom: 0px;
  background-color: #000;
  /*z-index: 100;*/
}
.viewFramework-product {
  width: auto;
  position: absolute;
  top: 0px;
  left: 0px;
  bottom: 0px;
  right: 0px;
  overflow: hidden;
  background: #F0F2F5;
}
.viewFramework-product-body {
  position: absolute;
  width: auto;
  top: 0px;
  bottom: 0px;
  left: 180px; /*  控制 */
  right: 0px;
  overflow: hidden;
  overflow-y: auto;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
}
.viewFramework-product-navbar {
  width: 180px; /* 控制宽度 */
  float: left;
  background: #323743;
  position: absolute;
  top: 0px;
  bottom: 0px;
  z-index: 2;
  overflow: hidden;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
}
.viewFramework-product-navbar .product-nav-stage {
  width: 180px;
  overflow: hidden;
  position: absolute;
  top: 0px;
  bottom: 0px;
  right: 0px;
}
.viewFramework-product-navbar .product-nav-stage-main .product-nav-main-scene {
  left: 0px;
}
.viewFramework-product-navbar .product-nav-stage .product-nav-scene {
  width: 180px;
  position: absolute;
  top: 0px;
  bottom: 0px;
  -webkit-transition: position,.2s,linear;
  -moz-transition: position,.2s,linear;
}
.viewFramework-product-navbar .product-nav-main-scene .product-nav-title {
  font-weight: bold;
  text-indent: 20px;
}
.viewFramework-product-navbar .product-nav-scene .product-nav-title {
  width: 180px;
  height: 70px;
  line-height: 80px;
  background: #323743;
  color: #A5AAAE;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.viewFramework-product-navbar .product-nav-list {
  position: absolute;
  top: 70px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  overflow-y: scroll;
  overflow-x: hidden;
}
.product-nav-list::-webkit-scrollbar {
  display:none
}
.viewFramework-product-navbar .product-nav-list ul {
  list-style: none;
  padding: 0px;
  margin: 0px;
}
.viewFramework-product-navbar .product-nav-list li a {
  width: 180px;
  height: 30px;
  line-height: 30px;
  display: block;
  color: #333;
}
.viewFramework-product-navbar .product-nav-list li.active a {
  background-color: #FFF;
}
.viewFramework-product-navbar .product-nav-list .nav-icon {
  width: 30px;
  height: 30px;
  float: left;
  text-align: center;
  font-size: 16px;
  color: #333;
}
.viewFramework-product-navbar .product-nav-list .nav-title {
  width: 138px;
  float: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.viewFramework-product-navbar .product-nav-list li a:hover {
  background-color: #F4F6F8;
  cursor: pointer;
}

.viewFramework-product-navbar-collapse {
  position: absolute;
  left: 165px; /* 控制位置 */
  top: 50%;
  width: 15px;
  height: 50px;
  z-index: 3;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -webkit-transition: all 0.2s ease;
}
.viewFramework-product-navbar-collapse .product-navbar-collapse-inner {
  top: -50%;
  position: relative;
  overflow: hidden;
}
.viewFramework-product-navbar-collapse .product-navbar-collapse-bg {
  right: 0;
  left: auto;
  /*border-bottom: 9px solid transparent;*/
  border-left: none;
  border-right: 15px solid #f7f7f7;
  /*border-top: 9px solid transparent;*/
}
.viewFramework-product-navbar-collapse .product-navbar-collapse-bg {
  width: 0;
  height: 50px;
  position: absolute;
  /* top: 0; */
  /* left: 0; */
  /* border-bottom: 9px solid transparent */
  /* border-left: 13px solid #D9DEE4; */
  /* border-top: 9px solid transparent; */
  -o-transition: all 0.1s ease,0.1s ease;
  -ms-transition: all 0.1s ease,0.1s ease;
  -moz-transition: all 0.1s ease,0.1s ease;
  /* -webkit-transition: all 0.1s ease,0.1s ease; */
}
.viewFramework-product-navbar-collapse .product-navbar-collapse {
  right: 0px;
  left: auto;
}
.viewFramework-product-navbar-collapse .product-navbar-collapse {
  height: 50px;
  position: relative;
  left: 0px;
  text-align: center;
  cursor: pointer;
  -o-transition: all 0.1s ease,0.1s ease;
  -ms-transition: all 0.1s ease,0.1s ease;
  -moz-transition: all 0.1s ease,0.1s ease;
  -webkit-transition: all 0.1s ease,0.1s ease;
}
.viewFramework-product-navbar-collapse .icon-collapse-left {
  display: inline;
}
.viewFramework-product-navbar-collapse .product-navbar-collapse>span {
  font-size: 15px;
  line-height: 32px;
  vertical-align: text-top;
}


/*拖动条样式*/
::-webkit-scrollbar {
  width: 10px;
  height: 8px;
  background-color: #e1e5ea
}

::-webkit-scrollbar-thumb {
  background-color: #c3cad4;
  border-radius: 10px;
  border: 2px solid #e1e5ea;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #aab1bc;
}

::-webkit-scrollbar-thumb:active {
  border: 0;
  border-radius: 0;
  background-color: #AAAAAA
}

::-webkit-scrollbar-thumb:window-inactive {
  background-color: #666666
}




.pull-left {
  float: left!important;
}
.pull-right {
  float: right!important;
}
.ivu-tabs-bar{
  border-bottom: 0!important;

}
.margin-bottom{
  margin-bottom: 8px !important;
}
.inline-block {
  display: inline-block;
  vertical-align: middle;
}


/* Element样式修改 */
/* .el-dialog__body {
  padding: 0;
}
.el-dialog__header {
  height: 50px;
  line-height: 26px;
  padding: 14px 16px;
  border-bottom: 1px solid #e8eaec;
  background: #EBEEF5;
  text-align: center;
  border-radius: 2px;
}
.el-dialog__headerbtn{
  line-height: 0;
}
.el-dialog__title{
  color: #333333;
}
.el-dialog__headerbtn .el-dialog__close{
  color: #333333;
}
.el-dialog__footer {
  border-top: 1px solid #e8eaec;
  padding: 10px 20px 15px;
} */
.el-pagination {
  padding: 2px 0px;
}
.el-pagination__jump {
  float: right;
  margin-right: 5px;
}
/* .el-dialog__headerbtn:focus .el-dialog__close, .el-dialog__headerbtn:hover .el-dialog__close{
  color: #333333;
} */
.btn-prev {
  float: right;
}
.btn-next {
  float: right;
}
.el-pager {
  float: right;
}
.el-submenu__title{
  height: 40px;
  line-height: 40px;
  color: #808183;
}
.el-menu{
  background: none;
  border-right: none;
}
.el-menu-item-group__title{
  padding: 0;
}
.el-menu-item{
  height: 40px!important;
  line-height: 40px!important;
  color: #A5AAAE;
}
.el-submenu .el-menu-item{
  padding-left: 50px!important;
  /*color: #808183;*/
}
.el-menu-item-group>ul{
  background: #262A33;
}
.el-submenu__title:hover{
  background: none;
}
/* .el-dialog--center .el-dialog__footer{
  text-align: right;
} */
.el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
  background: none;
  color: #3F8BEE;
}
.el-tabs__item{
  color: #707886;
}
.el-slider__input{
  margin-top: 0;
}
.el-drawer__header{
  margin-bottom: 0;
  padding: 16px 30px 0 16px;
}
.el-drawer__body{
  padding: 0 30px;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

/* iView样式修改 */
.ivu-dropdown-menu {
  min-width: 100px;
  max-width: 100px;
}
.ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu) {
  background: #fff;
}
.ivu-page-options {
  float: none;
  margin-left: 0;
}
.ivu-drawer-body {
  padding-left: 30px;
  padding-right: 30px;
}
.ivu-tabs-nav .ivu-tabs-tab .ivu-icon {
  margin-right: 0px;
}
.ivu-message {
  z-index: 3000!important;
}
.ivu-scroll-container {
  height: 100%!important;
}
.ivu-drawer-wrap {
  z-index: 2002;
}
.ivu-drawer-mask {
  z-index: 2002;
}



.console-title {
  padding: 10px 0px;
  min-height: 50px;
}
.console-title h5 {
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 16px;
}
.console-title h5 {
  display: inline-block;
  text-indent: 8px;
  border-left: 2px solid #88B7E0;
}
.ecs-smart-search-wrapper {
  display: inline-block;
  position: relative;
  z-index: 99;
}
.list-tool-bar {
  position: absolute;
  right: 0;
  top: 0;
}
.demo-drawer-profile{
  font-size: 14px;
}
.demo-drawer-profile .ivu-col{
  margin-bottom: 12px;
}
.demo-drawer-p{
  font-size: 16px;
  color: rgba(0,0,0,0.85);
  line-height: 24px;
  display: block;
  margin-bottom: 16px;
}
.el-table--border th, .el-table__fixed-right-patch{
  border-top: none!important;
}
.el-table .cell{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-menu-item:focus, .el-menu-item:hover{
  /*background: linear-gradient(90deg,rgba(38,140,247,1),rgba(73,197,255,1));*/
  background: none;
  color: #3AADFC;
}
.el-menu-item:hover i{
  /*color: #FFFFFF;*/
}
.el-menu-item.is-active{
  color: #FFFFFF;
  background: #3F8BEE;
  /*background: -webkit-linear-gradient(80deg, #268CF7, #49C5FF); !*Safari 5.1 - 6.0*!
  background: -o-linear-gradient(80deg, #268CF7, #49C5FF); !*Opera 11.1 - 12.0*!
  background: -moz-linear-gradient(80deg, #268CF7, #49C5FF); !*Firefox 3.6 - 15*!
  background: linear-gradient(80deg, #268CF7, #49C5FF); !*标准的语法*!*/
}

.el-dialog__title{
  line-height: 0;
}



/* 按钮 */
.blue{
  background: #3F8BEE;
  filter:alpha(Opacity=80);
  -moz-opacity:0.8;
  opacity: 0.8;
  color: #ffffff;
}
.error{
  background: #E25141;
  color: #ffffff;
  opacity: 0.8;
}
.error:hover{
  opacity: 1;
}
.ivu-btn{
  border-radius: 2px;
  margin-right: 4px;
  font-size: 12px;
}
.ivu-btn:hover, .el-button:hover{
  background: #3F8BEE;
  color: #ffffff;
  opacity: 1;
}
.ivu-btn.active, .ivu-btn:active, .el-button:hover{
  background: #2b85e4;
}
.es-detail-container .el-button:hover span{
  color: white !important;
}
.ivu-btn:focus{
  box-shadow: none;
}

.ivu-btn.disabled, .ivu-btn.disabled.active, .ivu-btn.disabled:active, .ivu-btn.disabled:focus, .ivu-btn.disabled:hover, .ivu-btn[disabled], .ivu-btn[disabled].active, .ivu-btn[disabled]:active, .ivu-btn[disabled]:focus, .ivu-btn[disabled]:hover, fieldset[disabled] .ivu-btn, fieldset[disabled] .ivu-btn.active, fieldset[disabled] .ivu-btn:active, fieldset[disabled] .ivu-btn:focus, fieldset[disabled] .ivu-btn:hover{
  color: #ffffff;
  background-color: #3F8BEE;
  border-color: #dcdee2;
  opacity: 0.4;
}
.ivu-btn-error{
  background: #E25141!important;
}
.reset, .float-reset{
  margin-right: 0;
}
.float-reset:hover{
  color: #3F8BEE;
  background-color: #FFFFFF;
  border-color: #3F8BEE;
}
.reset:hover{
  color: #3F8BEE!important;
  background-color: #FFFFFF;
  border-color: #3F8BEE;
}
.reset:focus{
  color: #3F8BEE!important;
  background-color: #FFFFFF;
  border-color: #3F8BEE;
}
.blue:focus{
  background: #3F8BEE;
  -moz-opacity:0.8;
  opacity: 0.8;
  color: #ffffff;
}
.ivu-input{
  border-radius: 2px;
  font-size: 12px;
}
.bt{
  border: none;
  color: #3F8BEE;
}
.bt:hover, .bt:active{
  /*background: #f5f7fa;*/
  background: none;
  color: #3F8BEE;
  opacity: .8;
}

/* 表格 */
.el-table thead{
  color: #333333;
}
.el-table td{
  color: #666666;
}
.el-table--enable-row-hover .el-table__body tr:hover> td .bt{
  background: none;
}
.el-table--striped .el-table__body tr.el-table__row--striped td{
  background: #F2F5F9;
}
.el-table--striped .el-table__body tr.el-table__row--striped td .bt{
  /*background: #F2F5F9;*/
  background: none;
}
.el-table--enable-row-hover .el-table__body tr:hover>td{
  background: none;
}
.el-table--striped .el-table__body tr.el-table__row--striped td{
  background: #F2F5F9;
}
.el-table--mini, .el-table--small, .el-table__expand-icon{
  font-size: 12px;
}
.ivu-btn-small{
  font-size: 12px;
}
.el-checkbox:last-child{
  margin-right: 0!important;
}
/* 项目过滤 */
.ativeProject{
  height: 100%;
}
/* ecs头部 */
/* .ecs-blank{
  display: none;
}
@media screen and (max-width: 1360px) {
  .lwy-a {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  .lwy-a .ivu-btn{
    margin-right: 0;
  }
  .list-tool-bar-1{
    display: flex;
    width: 100%;
  }
  .ecs-blank{
    display: block;
    flex: 1;
  }
} */
.pre{
  background: white !important;
}
.pre *{
  background: white !important;
  color: black !important;
}
.loading-balance-detail .el-menu-item{
  padding: 0 10px !important;
}
.choose_project{
  color: #3F8BEE;
}
.choose_project:hover{
  color: #1e77ec;
}
.cloud-application-menu-content{
  border: none;
}
.department-title{
  margin-bottom: 12px;
}
.department-title-label{
  font-size: 14px;
  font-weight: 700;
}
.department-title-name{
  font-size: 14px;
}
.department-title-no{
  font-size: 14px;
  color: var(--color-red);
}
.department-right .el-tabs{
  height: 100%;
}
.department-right .el-tabs__content{
  height: calc(100% - 45px);
  overflow: hidden;
}
.menu-active{
  background:rgba(0,0,0,.1) !important;
}
.el-input-group .el-input-group__append{
  border: none;
}
.app-strategy .cloud-button .cl-btn-text{
  background: none !important;
}
.UserManageClass{
  height: 100%;
}
.ProjectManageClass{
  height: 100%;
}
.RoleManageClass{
  height: 100%;
}
.department-left-menu .menu-blank-icon{
  display: inline-block;
  width: 14px;
  height: 21px;
  background: none !important;
}
.department-left-menu .cloud-tree-menu{
  position: relative;
}
.department-left-menu .menu-su-line{
  height: 100%;
  content: '';
  background: #a1a0a0;
  width: 1px;
  position: absolute;
  top: 0px;
  left: 6px;
  z-index: 1;
}
.department-left-menu .cloud-application-menu .cloud-application-menu-msg .menu-zankai{
  position: relative;
  z-index: 2;
}
.department-left-menu .tree-msg-dom{
  padding-left: 5px;
  position: relative;
}
.department-left-menu .tree-msg-dom::before{
  display: block;
  width:13px;
  content: '';
  background: #a1a0a0;
  height: 1px;
  position: absolute;
  top: 50%;
  left: -6px;
  transform: translateY(-50%);
}