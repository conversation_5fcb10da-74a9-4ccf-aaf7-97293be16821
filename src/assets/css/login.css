.page-wrapper {
  width: 100%;
  height: 100%;
  table-layout: fixed;
}
.hts-background_1 {
  background-size: cover;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.hts-background_box{
  position: absolute;
  width: 1200px;
  height: 800px;
  left: 50%;
  top: 50%;
  margin-left: -600px;
  margin-top: -400px;
  /*height: 100%;*/

}
.hts-login-topbar {
  height: 60px;
  position: relative;
  border-bottom: 1px solid rgba(255,255,255,0.3);
}
.hts-login-logo{
  position: absolute;
  left: 20px;
  top: 12px;
  height: 35px;
  /*width: 200px;*/
/*北京交管注释掉width*/
}

.hts-login_footer{
  width: 100%;
  color: #404754;
  font-family:PingFangSC-Regular;
  font-weight:400;
}
.hts-login_footer p{
  line-height: 30px;
  text-align: center;
}
.hts-login-img-icon {
  position: absolute;
  right: 0px;
}
.a-apply{
  text-decoration: none;
  color:  #66B3FF;
  font-size: 13px;
}
.a-apply:hover{
  text-decoration: none;
  color:  #0066CC;
}
.slidetounlock{
  font-size: 100px;
  background:-webkit-gradient(linear,left top,right top,color-stop(0,#707886),color-stop(.4,#707886),color-stop(.5,#fff),color-stop(.6,#707886),color-stop(1,#707886));
  -webkit-background-clip:text;
  -webkit-text-fill-color:transparent;
  -webkit-animation:slidetounlock1 3s infinite;
  -webkit-text-size-adjust:none
}
@keyframes slidetounlock1
{
  0%{background-position:-4em 0}
  100%{background-position:4em 0}
}
@-webkit-keyframes slidetounlock1
{
  0%{background-position:-100% 0}
  100%{background-position:100% 0}
}

.el-input-group__append, .el-input-group__prepend{
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  background-color: #242A35;
  border: none;
  color: #707886;
}
.el-tabs__nav{
  float: none;
}

.register-content .register-form .el-input__inner{
  height: 35px;
  background: #242A35;
  border-radius:2px;
  border: none;
  color: #707886;
  opacity:0.6;
}
.register-content .register-form .el-input.is-active .el-input__inner, .register-content .register-form .el-input__inner:focus{
  color: #B6BFCD;
  border:1px solid rgba(166, 173, 186, 0.59);
  opacity: 1;
}

.register-content .register-form .el-input-group__append, .register-content .register-form .el-input-group__prepend{
  background: #4B515D;
  border: 1px solid #4B515D;
  border-radius: 2px;
}
.register-content .register-form .el-checkbox__label{
  color: #B6BECC;
}
.register-content .register-form .el-checkbox__inner{
  background: none;
  border: 1px solid #6E7684;
}
.register-content .register-form .el-button--primary{
  background: #3C91F7;
  border: #3C91F7;
}
.login-tab-active{
  font-size: 1.5em !important;
  padding: 0;
  color: #333333;
  font-weight: 600;
}
.login-input-style{
  background: #F3F3F3;
}

.login-input{
  height: 0.33em;
  background: #ebf2fe;
}

.login-input i{
  font-size: 0.14em;
  color: #c0c4cc;
}
.login-input input{
  font-size: 0.14em;
  position: absolute;
  left: 0;
  top: 0;
  background: #F3F3F3;
}
