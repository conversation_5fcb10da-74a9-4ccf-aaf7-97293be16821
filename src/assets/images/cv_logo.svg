<svg id="组_33" data-name="组 33" xmlns="http://www.w3.org/2000/svg" width="128" height="32" viewBox="0 0 128 32">
  <g id="图层_2" data-name="图层 2" transform="translate(8 8.999)">
    <g id="图层_1" data-name="图层 1" transform="translate(0 0.001)">
      <path id="路径_12" data-name="路径 12" d="M20.391,7.972A4.2,4.2,0,0,0,18.5,6.688a7.2,7.2,0,0,0-2.12-4.541A7.283,7.283,0,0,0,7.2,1.211a7.2,7.2,0,0,0-2.521,2.8,5.516,5.516,0,0,0-3.056,9.363h0A5.531,5.531,0,0,0,5.537,15H10.7L6.407,12.52V7.564L10.7,5.083l4.294,2.481v4.962L10.7,15h6.447A4.28,4.28,0,0,0,21.4,10.7a4.207,4.207,0,0,0-1.01-2.729Z" transform="translate(0 -0.001)" fill="#228be6"/>
      <path id="路径_13" data-name="路径 13" d="M18.167,17.5,13.874,15.02,9.58,17.5l4.294,2.474Z" transform="translate(-3.173 -4.976)" fill="#7dccf3"/>
      <path id="路径_14" data-name="路径 14" d="M13.874,7.61,9.58,10.091v4.956l4.294-2.481Z" transform="translate(-3.173 -2.521)" fill="#a2d6f4"/>
      <path id="路径_15" data-name="路径 15" d="M16,7.61l4.294,2.481v4.956L16,12.566Z" transform="translate(-5.299 -2.521)" fill="#fff"/>
    </g>
  </g>
  <g id="组_32" data-name="组 32" transform="translate(38 -1.077)">
    <g id="组_9" data-name="组 9" transform="translate(0 10.077)">
      <path id="路径_18" data-name="路径 18" d="M9.123,35.98a2.422,2.422,0,0,0-.925.294,4.585,4.585,0,0,1-1.976.5c-2.1-.126-3.237-1.471-3.405-3.952.126-2.607,1.261-3.952,3.405-4.078a5.984,5.984,0,0,1,1.892.336,3.129,3.129,0,0,0,.925.21c.673-.042,1.009-.462,1.051-1.219C10.006,26.9,8.7,26.226,6.222,26.1,2.354,26.394.294,28.623,0,32.743c.294,4.078,2.354,6.264,6.222,6.517,2.691-.042,4.036-.715,4.078-1.976C10.216,36.484,9.8,36.022,9.123,35.98Z" transform="translate(0 -25.259)" fill="#fff"/>
      <path id="路径_19" data-name="路径 19" d="M28.461,24.1c-.8.042-1.219.5-1.261,1.429V36.671c.042.883.462,1.345,1.261,1.345a1.237,1.237,0,0,0,1.345-1.345V25.529A1.3,1.3,0,0,0,28.461,24.1Z" transform="translate(-15.765 -24.1)" fill="#fff"/>
      <path id="路径_20" data-name="路径 20" d="M40.467,33.8c-2.817.252-4.372,1.892-4.667,5,.168,3.111,1.724,4.751,4.667,4.877,2.9-.126,4.456-1.766,4.667-4.877C44.839,35.734,43.283,34.052,40.467,33.8Zm0,7.652c-1.261-.084-1.934-.967-2.06-2.607.084-1.64.757-2.523,2.06-2.607,1.261.084,1.934.967,2.06,2.607C42.4,40.485,41.728,41.368,40.467,41.452Z" transform="translate(-20.749 -29.722)" fill="#fff"/>
      <path id="路径_21" data-name="路径 21" d="M67.831,34c-.8.042-1.219.547-1.261,1.471v4.667c-.042,1.009-.631,1.514-1.682,1.64a1.578,1.578,0,0,1-1.682-1.64V35.471c-.042-.925-.462-1.429-1.261-1.471A1.339,1.339,0,0,0,60.6,35.471v4.667q.126,3.595,4.288,3.658,4.162-.063,4.288-3.658V35.471C69.093,34.547,68.672,34.042,67.831,34Z" transform="translate(-35.123 -29.838)" fill="#fff"/>
      <path id="路径_22" data-name="路径 22" d="M91.488,24.1c-.8.042-1.219.5-1.261,1.429V29.1a3.428,3.428,0,0,0-2.48-.925c-2.607.168-3.994,1.892-4.246,5.171.252,2.985,1.682,4.541,4.288,4.751a2.942,2.942,0,0,0,2.4-1.051,1.141,1.141,0,0,0,1.261,1.009c.8,0,1.219-.462,1.261-1.345V25.529C92.707,24.6,92.287,24.142,91.488,24.1ZM88.167,35.83c-1.261-.084-1.934-.967-2.06-2.607.084-1.64.757-2.523,2.06-2.607,1.261.084,1.934.925,2.06,2.523C90.1,34.821,89.428,35.746,88.167,35.83Z" transform="translate(-48.395 -24.1)" fill="#fff"/>
    </g>
    <g id="组_10" data-name="组 10" transform="translate(45.069 10.371)">
      <path id="路径_23" data-name="路径 23" d="M114.179,34c-.5,0-.883.294-1.135.925L111.362,40.1l-1.682-5.171A1.088,1.088,0,0,0,108.545,34a1.237,1.237,0,0,0-1.345,1.261,2.037,2.037,0,0,0,.126.757l2.607,6.727a1.319,1.319,0,0,0,1.429,1.009,1.268,1.268,0,0,0,1.429-1.009l2.607-6.727c.042-.084.084-.126.084-.084a2.562,2.562,0,0,0,.126-.715A1.3,1.3,0,0,0,114.179,34Z" transform="translate(-107.2 -30.132)" fill="rgba(255,255,255,0.7)"/>
      <path id="路径_24" data-name="路径 24" d="M129.729,24.8a1.473,1.473,0,0,0-1.429,1.429,1.454,1.454,0,0,0,1.429,1.471,1.436,1.436,0,0,0,1.471-1.471A1.454,1.454,0,0,0,129.729,24.8Z" transform="translate(-119.429 -24.8)" fill="rgba(255,255,255,0.7)"/>
      <path id="路径_25" data-name="路径 25" d="M129.961,34c-.8.042-1.219.547-1.261,1.471v6.937c.042.883.462,1.345,1.261,1.345a1.237,1.237,0,0,0,1.345-1.345V35.471A1.339,1.339,0,0,0,129.961,34Z" transform="translate(-119.661 -30.132)" fill="rgba(255,255,255,0.7)"/>
      <path id="路径_26" data-name="路径 26" d="M146.233,38.551c-.378-2.9-1.934-4.5-4.667-4.751-2.817.252-4.372,1.892-4.667,5,.168,3.111,1.808,4.751,4.877,4.877,2.48-.126,3.91-.841,4.246-2.06a1.179,1.179,0,0,0-1.051-1.135,2.594,2.594,0,0,0-1.009.42,4.571,4.571,0,0,1-1.976.631,2.271,2.271,0,0,1-2.48-1.892H145.1A.959.959,0,0,0,146.233,38.551ZM139.423,38c.252-1.3.925-2.018,2.1-2.06,1.177.042,1.85.715,2.06,2.06Z" transform="translate(-124.414 -30.016)" fill="rgba(255,255,255,0.7)"/>
      <path id="路径_27" data-name="路径 27" d="M172.287,34c-.631,0-1.009.336-1.219,1.051l-1.556,4.961-1.556-4.877Q167.578,34,166.695,34a1.311,1.311,0,0,0-1.345,1.135l-1.556,4.877-1.471-4.961A1.3,1.3,0,0,0,161.061,34c-.8.042-1.219.42-1.261,1.051a3.34,3.34,0,0,0,.294,1.261l2.186,6.222a1.593,1.593,0,0,0,1.556,1.219,1.354,1.354,0,0,0,1.471-1.135l1.345-4.162h.084l1.345,4.162a1.438,1.438,0,0,0,1.471,1.135,1.593,1.593,0,0,0,1.556-1.219l2.27-6.264a3.912,3.912,0,0,0,.21-1.009A1.249,1.249,0,0,0,172.287,34Z" transform="translate(-137.686 -30.132)" fill="rgba(255,255,255,0.7)"/>
    </g>
  </g>
  <rect id="矩形_180" data-name="矩形 180" width="128" height="32" fill="rgba(255,255,255,0)"/>
</svg>
