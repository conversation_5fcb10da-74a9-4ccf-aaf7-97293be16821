.cv-login-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  .cv-login-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #040c23;
    z-index: 9;
  }
  .cv-login-animate {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 60px;
    right: 35%;
    z-index: 10;
    .images-five-after {
      content: '';
      position: absolute;
      display: block;
      width: 2554px;
      height: 904px;
      background: url(images/cv_login_y.svg) center center no-repeat;
      bottom: -480px;
      @media (min-width: 1367px) {
        bottom: -440px;
      }
      @media (min-width: 1441px) {
        bottom: -400px;
      }
      left: 50%;
      margin-left: -1277px;
      z-index: -1;
    }
    .images-five {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 420px;
      height: 670px;
      margin: -335px 0 0 -210px;
      background: url(images/cv_login_5.svg) center center no-repeat;
      transform: scale(0.62,0.62);
      transition: all 0.3s ease;
      animation: five 0.5s ease;
      @keyframes five {
        0% {
          left: calc(50% - 10px);
          opacity: 0;
        }
        100% {
          left: 50%;
        }
      }
      @media (min-width: 1367px) {
        transform: scale(0.7,0.7);
      }
      @media (min-width: 1441px) {
        transform: scale(1,1);
      }
    }
    .images-cloud {
      position: absolute;
      top: 375px;
      left: 72px;
      width: 60px;
      height: 48px;
      background: url(images/cv_login_cloud.svg) center center no-repeat;
      animation: cloud 2s linear infinite;
      @keyframes cloud {
        0% {
            top: 375px;
        }
        50% {
          top: 385px;
        }
        100% {
          top: 375px;
        }
      }
    }
    .images-fire {
      position: absolute;
      width: 188px;
      height: 188px;
      background: url(images/cv_login_fire.svg) center center no-repeat;
      background-size: 100% 100%;
      &.one {
        bottom: -180px;
        left: -140px;
        animation: fire1 1.5s ease;
        @keyframes fire1 {
          0% {
            opacity: 0;
          }
          20% {
            opacity: 0;
          }
          40% {
            opacity: 0;
          }
          60% {
            opacity: 0.4;
          }
          80% {
            opacity: 0.7;
          }
          100% {
            opacity: 1;
          }
        }
      }
      &.two {
        width: 120px;
        height: 120px;
        bottom: -20px;
        right: -120px;
        opacity: 0.8;
        animation: fire2 1.5s ease;
        @keyframes fire2 {
          0% {
            opacity: 0;
          }
          20% {
            opacity: 0;
          }
          40% {
            opacity: 0;
          }
          60% {
            opacity: 0.2;
          }
          80% {
            opacity: 0.4;
          }
          100% {
            opacity: 0.8;
          }
        }
      }
      &.three {
        width: 80px;
        height: 80px;
        bottom: 150px;
        left: -120px;
        opacity: 0.3;
        animation: fire3 1.5s ease;
        @keyframes fire3 {
          0% {
            opacity: 0;
          }
          20% {
            opacity: 0;
          }
          40% {
            opacity: 0;
          }
          60% {
            opacity: 0.1;
          }
          80% {
            opacity: 0.2;
          }
          100% {
            opacity: 0.3;
          }
        }
      }
    }
  }
  .cv-login-logo {
    position: absolute;
    top: 30px;
    left: 30px;
    width: 160px;
    height: 50px;
    background: url(images/cloudview-white.svg) left top no-repeat;
    z-index: 999;
  }
  .cv-login-formbox {
    position: absolute;
    left: 60%;
    top: 50%;
    margin-top: -200px;
    z-index: 12;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    .tabs {
      margin-bottom: 32px !important;
      .tab-phone {
        color: #FFF;
        font-size: 14px !important;
        transition: all 0.2s ease;
        height: 32px;
        line-height: 32px;
        &.login-tab-active {
          font-size: 24px !important;
        }
      }
    }
    .login-input {
      height: 33px;
      width: 320px;
      background: none;
    }
    .login-input-1 {
      position: relative;
      height: 33px;
      margin-bottom: 5px;
      border-radius: 0;
      background: none;
      color: #FFF;
      &.input_error {
        border: none;
        input {
          border-color: #F56C6C;
          &:focus {
            border-color: #F56C6C;
          }
        }
      }
      i {
        font-size: 12px;
        position: absolute;
        margin: 0;
        top: 9px;
        left: 0;
      }
      input {
        width: 320px;
        font-size: 14px;
        padding-left: 20px;
        color: #FFF;
        border-bottom: 1px solid rgba($color: #FFF, $alpha: 0.5);
        margin-bottom: 3px;
        &:focus {
          margin: 0;
          border-bottom-width: 4px;
          border-color: #FFF;
        }
      }
    }
    .error-text, .erro-text {
      color: #F56C6C !important;
    }
    .sugon-input {
        width: 320px;
        font-size: 14px;
        padding-left: 0;
        border-radius: 0;
        background: none;
        color: #FFF;
        border: none;
        border-bottom: 1px solid rgba($color: #FFF, $alpha: 0.5);
        margin-bottom: 3px !important;
        transition: none;
        &:focus {
          margin: 0;
          border: none;
          border-bottom: 4px solid #FFF;
        }
        &.error_status {
          border-color: #F56C6C !important;
          &:focus {
            margin: 0;
            border-color: #F56C6C !important;
          }
        }
    }
    #drag {
      height: 33px;
      background: rgba($color: #FFF, $alpha: 0.1);
      .drag_bg {
        height: 33px;
      }
      .drag_text {
        font-size: 12px;
        line-height: 33px;
        color: rgba($color: #FFF, $alpha: 0.5);
      }
      .handler {
        width: 40px;
        height: 33px;
      }
    }
    .el-button {
      margin-top: 20px;
      background: none;
      border: 2px solid #FFF;
      border-radius: 0 !important;
      height: 40px;
      line-height: 18px;
      font-size: 14px;
      transition: all 0.3s ease;
      &:hover {
        border-color: #2680EB;
        background: #2680EB;
      }
    }
  }
  .cv-login-copyright {
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    border-top: 1px solid rgba($color: #FFF, $alpha: 0.1);
    padding: 10px 0;
    z-index: 13;
    p {
      margin: 0;
      text-align: center;
      color: rgba($color: #FFF, $alpha: 0.3);
      line-height: 20px;
      font-size: 12px;
    }
  }
}