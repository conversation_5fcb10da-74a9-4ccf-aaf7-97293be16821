/*权限*/
import hello from "@/page/hello";
/*模型广场*/
const ModelSquence = () =>
  Promise.resolve(import("@/page/model-center/model-square"));
/*模型仓库*/
const ModelRepository = () =>
  Promise.resolve(import("@/page/model-center/model-repository"));
/*模型调优*/
const ModelTuning = () =>
  Promise.resolve(import("@/page/model-center/model-tuning"));
const TuningNewModel = () =>
  Promise.resolve(
    import("@/page/model-center/model-tuning/handle/training-new-model")
  );
/**模型部署 */
const ModelDeployment = () =>
  Promise.resolve(import("@/page/model-center/model-deployment"));
const DeploymentNewModel = () =>
  Promise.resolve(
    import(
      "@/page/model-center/model-deployment/handle/deployment-new-model.vue"
    )
  );

/*模型体验 */
const ModelExperience = () =>
  Promise.resolve(import("@/page/model-center/model-experience/index.vue"));
/**数据中心 */
/**数据集 */
const DataSet = () => Promise.resolve(import("@/page/data-center/data-set"));
/*数据集中心-详情**/
const DataSetDetail = () =>
  Promise.resolve(import("@/page/data-center/data-set/detail"));
/**数据中心 */

const ModelOnline = () =>
  Promise.resolve(
    import("@/page/model-center/model-square/dialog/model-online.vue")
  );
/**系统管理 */
/**API  Key*/
const ApiKey = () =>
  Promise.resolve(import("@/page/system-management/api-key"));
/**显卡管理 */
const GraphicsCard = () =>
  Promise.resolve(import("@/page/system-management/graphics-card"));

const ModelSquenceDetail = () =>
  Promise.resolve(import("@/page/model-center/model-square/detail.vue"));

/**集群管理 */
const ClusterManagement = () =>
  Promise.resolve(import("@/page/system-management/cluster-management"));
/**集群管理详情 */
const ClusterManagementDetail = () =>
  Promise.resolve(
    import("@/page/system-management/cluster-management/detail.vue")
  );
/**用量统计 */
const usageStatistics = () =>
  Promise.resolve(import("@/page/operation-center/usage-statistics/index.vue"));

export const routers = [
  {
    path: "/AiClusterManagement",
    component: ClusterManagement,
    name: "ClusterManagement"
  },
  {
    path: "/AiClusterManagement/detail",
    component: ClusterManagementDetail,
    name: "ClusterManagementDetail"
  },
  {
    path: "/ModelSquenceDetail",
    component: ModelSquenceDetail,
    name: "ModelSquenceDetail"
  },
  {
    path: "/modelSquence",
    component: ModelSquence,
    name: "modelSquence"
  },
  {
    path: "/modelRepository",
    component: ModelRepository,
    name: "modelRepository"
  },
  {
    path: "/modelTuning",
    component: ModelTuning,
    name: "modelTuning"
  },
  {
    path: "/modelTuning/training",
    component: TuningNewModel,
    name: "TuningNewModel"
  },
  {
    path: "/modelDeployment",
    component: ModelDeployment,
    name: "modelDeployment"
  },
  {
    path: "/modelDeployment/deployment",
    component: DeploymentNewModel,
    name: "deploymentNewModel"
  },
  {
    path: "/modelExperience",
    component: ModelExperience,
    name: "modelExperience"
  },
  {
    path: "/viewModelExperience",
    component: ModelExperience,
    name: "viewModelExperience"
  },
  {
    path: "/dataSet",
    component: DataSet,
    name: "DataSet"
  },
  {
    path: "/dataSet/detail",
    component: DataSetDetail,
    name: "DataSetDetail"
  },
  {
    path: "/modelOnline",
    component: ModelOnline,
    name: "modelOnline"
  },
  {
    path: "/apiKey",
    component: ApiKey,
    name: "ApiKey"
  },
  {
    path: "/GraphicsCard",
    component: GraphicsCard,
    name: "GraphicsCard"
  },
  {
    path: "/usageStatistics",
    component: usageStatistics,
    name: "usageStatistics"
  }
];
