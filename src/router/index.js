import index from '@/page/index.vue'
import {routers} from './routers'
import routerConfig from './router-config'
import {get_codes} from '@/http/codes-http/codes-http.js';
export const createRouter = function(others = []){
  return [
    {
      path:'/index',
      component:index,
      redirect:'/modelSquence',
      children:[
        ...others,
        ...routers,
        ...routerConfig,
      ]
    }
  ]
}
const checkBread = function(fun){
  let timer = null;
  let count = 0
  timer = setInterval(()=>{
    let el = document.querySelector('.bread-container');
    count++
    if(el){
      fun(el)
      clearInterval(timer)
    }else if(count > 20){
      clearInterval(timer)
    }
  },50)
}
export const routerInterceptor = function(to, from, next, router){
  try{
    let noBreadPath=['/modelSquence', '/ModelSquenceDetail','/modelRepository','/modelTuning','/modelDeployment','/dataSet','/AiClusterManagement']
    if(noBreadPath.indexOf(to.path) !== -1){
      checkBread((el)=>{
        el.style.display = 'none'
      })
    }else {
      checkBread((el)=>{
        el.style.display = 'block'
      })
    }
    if(!AppStore.state.aiCodes.length && to.path != '/login' && to.path != '/blank'){
        console.log('去向2',to.path)
        get_codes({page_num:1,page_size:1000,}).then((res)=>{
          next()
          if(res.status_code != 1){
            window.CloudApplicationMainCliGlobal.modules.ElementUI.Message.error(res.status_mes)
          }else{
            AppStore.commit('set_aiCodes',res.content.list)
          }
        }).catch((err)=>{
          window.CloudApplicationMainCliGlobal.modules.ElementUI.Message.error('字典接口请求失败')
          next()
        })
    }else{
      console.log('去向',to.path)
      next()
    }
  }catch(err){
    console.error(err)
  }
}