export default {
  header: {
    api: 'API',
    cooperation: 'Cooperation',
    cooperationMore: 'Contact us ',
    cooperationTips: 'Feedback and technical support',
  },
  common: {
    type: 'en',
    newPlaceholder: 'Name of the Knowledge Base',
    new: 'New',
    newChat: 'New Chat',
    refreshChat: 'Refresh Chat',
    manage: 'manage',
    rename: 'rename',
    renameSucceeded: 'Succeeded',
    renameFailed: 'Failed',
    delete: 'delete',
    preview: 'preview',
    regenerate: 'regenerate',
    dragUrl: 'or drag and drop to upload',
    click: 'Click',
    updesc1:
      'Support: md, txt, pdf, jpg, png, jpeg, docx, xlsx,pptx, eml, csv, single document less than 30M, single picture less than 5M',
    updesc2:
      'Can upload files in batches, supported file type: doc、docx、ppt、pptx、xls、xlsx、pdf、md、JPG、JPEG、PNG、BMP、txt、eml, single document less than 30MB, single image less than 5MB',
    confirm: 'Confirm',
    cancel: 'Cancel',
    addUrl: 'Add URL',
    urlPlaceholder: 'Please enter the URL',
    uploading: 'Uploading',
    parsing: 'Parsing',
    failed: 'Failed',
    succeeded: 'Succeeded',
    upSucceeded: 'Succeeded',
    upFailed: 'Failed',
    loadingText: 'loading...',
    problemPlaceholder: 'Type a question',
    errTip: 'Cannot be null',
    uploadFile: ' Upload files',
    errorKnowledge: 'Type the name of the Knowledge Base',
    successTip: 'Succeeded',
    faileTip: 'Failed',
    deleteDec: 'Delete this Knowledge Base? Not recoverable after deletion',
    deleteChatDec: 'Delete this Chat? Not recoverable after deletion',
    chooseError: 'Select at least one Knowledge Base',
    chooseChatError: 'Select at least one Chat',
    saveTip: 'Save Sugonquery conversations as images?',
    clearTip: 'Delete all conversations?',
    stop: 'Stop',
    errorTip:
      'Note: files that failed to be uploaded will not be displayed on the management page.',
    deleteTitle: 'Delete the document?',
    dataSource: 'data source',
    correlation: 'correlation:',
    copySuccess: 'Copied Successfully',
    copyFailed: 'Copy Failed',
    copied: 'Copied',
    copy: 'Copy',
    send: 'Send',
    like: 'Like',
    notSupported: 'Not Supported',
    error: 'error',
    isNweChat: 'is new Chat',
  },
  home: {
    documentId: 'Document ID',
    documentName: 'Document Name',
    documentStatus: 'Document Status(Q&A after successful parsing)',
    fileSize: 'File Size',
    creationDate: 'Creation Time',
    remark: 'Remarks or Notes',
    operate: 'Operate',
    conversation: 'Conversation',
    knowledgeID: 'Knowledge Base ID',
    upload: 'Upload Documents',
    addUrl: 'Add URL',
    homeTitle1: 'Chat with ',
    homeTitle2: 'Sugonquery',
    defaultDec:
      'Build a document knowledge base, efficiently retrieve document information, and accurately answer professional questions',
    defaultName: 'Default Knowledge Base',
    startDec: 'Upload documents and type questions',
    updesc2: 'Support: md, txt, pdf, jpg, png, jpeg, docx, xlsx, pptx, eml, csv',
    require1: 'Can upload files in batches, single file less than 20mb/1 million words',
    emptyText: 'No data available',
  },
};
