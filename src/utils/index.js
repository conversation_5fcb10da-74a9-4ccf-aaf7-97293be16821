export const static_name = {
  api_path: "api",
  chat_path: 'api/sugoncloud-wxdl-api/api'
};

export function throttle(func, wait) {
  let timeout = null;
  let lastArgs = null;
  let lastThis = null;
  let lastCallTime = 0;

  function later() {
    lastCallTime = Date.now();
    timeout = null;
    func.apply(lastThis, lastArgs);
    if (!timeout) {
      lastThis = lastArgs = null;
    }
  }

  return function (...args) {
    const now = Date.now();
    const remainingTime = wait - (now - lastCallTime);

    lastArgs = args;
    lastThis = this;

    if (remainingTime <= 0 || remainingTime > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      lastCallTime = now;
      func.apply(this, args);
    } else if (!timeout) {
      timeout = setTimeout(later, remainingTime);
    }
  };
}

export function copy(vue, item) {
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(item.answer)
      .then(() => {
        item.copied = true;
        vue.$message.success("复制成功", 1);
        setTimeout(() => {
          item.copied = false;
        }, 1000);
      })
      .catch(() => {
        vue.$message.error("复制失败", 1);
      });
  } else {
    // Fallback for older browsers
    const textarea = document.createElement("textarea");
    textarea.value = item.answer;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand("copy");
    document.body.removeChild(textarea);
    item.copied = true;
    vue.$message.success("复制成功", 1);
    setTimeout(() => {
      item.copied = false;
    }, 1000);
  }
}

export function setScale(config = { dom: "", width: 0, height: 0 }) {
  console.log("宽度变化");
  window.windowScale(() => {
    if (config.dom) {
      let currentWidth = config.dom.offsetWidth;
      let currentHeight = currentWidth * (config.height / config.width);
      config.dom.style.height = currentHeight + "px";
    }
  }, "all");
}
export function formatFileSize(bytes,precision =2) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024; // 1 KB = 1024 Bytes
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k)); // 计算合适的单位索引
  return `${(bytes / Math.pow(k, i)).toFixed(Number(precision))} ${sizes[i]}`; // 格式化输出，保留两位小数
}
export function getSchemaType(type){
  if(!type){
    return '';
  };
  let schema_type_dict ={
      "SFT":'SFT-文本生成',
      "DPO":'DPO-文本生成',
      "CPT":'CPT-文本生成'
  };
  return schema_type_dict[type]?schema_type_dict[type]:"--"
}
