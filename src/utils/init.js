import { rejects } from "assert";
var timer = null
export default {
  check: function (names, fn) {
    timer = setInterval(() => {
      var state = true
      for (var item of names) {
        if (!window[item]) {
          state = false
        }
      }
      if (state) {
        console.log('创建实例成功')
        fn()
        clearInterval(timer)
      }
    }, 100);
  },
  cliInit: function (names, url) {
    var _this = this
    return new Promise((resolve, rejects) => {
      var xhr = new XMLHttpRequest(); // XMLHttpRequest对象用于在后台与服务器交换数据
      xhr.open('GET', url, true);
      // xhr.onreadystatechange = function () {
      //   if (xhr.readyState == 4 && xhr.status == 200 || xhr.status == 304) { // readyState == 4说明请求已完成
      //     var data = JSON.parse(xhr.responseText)
      //     for (let item of data.link) {
      //       var link = document.createElement('link')
      //       link.rel = 'stylesheet'
      //       link.href = item
      //       var headDom = document.head
      //       headDom.insertBefore(link, headDom.childNodes[2])
      //     }
      //     for (let item of data.script) {
      //       var script = document.createElement('script')
      //       script.src = item
      //       document.head.appendChild(script)
      //     }
      //     _this.check(names,function () {
      //       resolve(CloudApplicationMainCliGlobal)
      //     })
      //   }
      // };
      xhr.onreadystatechange = function () {
        if (xhr.readyState == 4 && xhr.status == 200 || xhr.status == 304) { // readyState == 4说明请求已完成
          var data = JSON.parse(xhr.responseText)
          data = data.cloud
          for (let item of data.link) {
            var link = document.createElement('link')
            if (item.includes('.ico')) {
              link.rel = "icon"
              link.type = "image/png"
              link.sizes = "144x144"
            } else {
              link.rel = 'stylesheet'
            }
            link.href = item
            var headDom = document.head
            headDom.insertBefore(link, headDom.childNodes[2])
          }
          for (let item of data.script) {
            var script = document.createElement('script')
            script.src = item
            document.head.appendChild(script)
          }
          _this.check(names, function () {
            resolve(CloudApplicationMainCliGlobal)
          })
        }
      };
      xhr.send();
    })
  }
}
