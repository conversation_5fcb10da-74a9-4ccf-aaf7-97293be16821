<template>
  <div class="index-box">
    <CloudLeftMenu />
    <div class="index-content">
      <cloud-container>
        <router-view style="width:100%" />
      </cloud-container>
    </div>
  </div>
</template>

<script>
import menu from './menu'
export default {
  data () {
    return {
      menuData:menu,
      // menuData:[
      //   {
      //     policy_name: "VDC管理",
      //     index: "",
      //     icon: "icon-ziyuanbianpai",
      //     uuid:'11',
      //     resourcePolicyFlag:'0',
      //   }
      // ]
    }
  },
  created(){

  },
  methods:{
   
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .container-router{
    width: 100%;
    overflow: auto;
    background: var(--color-theme-router-background)
  }
  .index-box{
    width: 100%;
    height: 100%;
    /* overflow: hidden; */
    display: flex;
  }
  .index-top{
    width: 100%;
    height: 48px;
  }
  .index-content{
    /* height:100%; */
    padding: 10px 10px 0 10px;
    flex: 1;
    display: flex;
    /* overflow: auto; */
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
  }
  .index-content .index-conent-left{
    overflow: hidden;
    height: 100%;
    display: flex;
  }
  .index-content .index-content-container{
    flex: 1;
  }
</style>
