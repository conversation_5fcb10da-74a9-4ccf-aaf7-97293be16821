<template>
  <sugon-dialog
    :isShow.sync="state"
    @confirm="confirm"
    width="600"
    title="上线模型"
    type="no-form"
    ref="dialog"
  >
    <DeploymentNewModel
      :model="model"
      ref="DeploymentNewModel"
      :key="key"
      @max_available="max_available"
    />
    <div class="sugon-dialog-out-padding"></div>
  </sugon-dialog>
</template>

<script>
import DeploymentNewModel from "@/page/model-center/model-deployment/handle/deployment-new-model.vue";
export default {
  components: {
    DeploymentNewModel
  },
  data() {
    return {
      model: "",
      state: false,
      can_submit: true,
      key: +new Date()
    };
  },
  methods: {
    open(data) {
      this.model = data;
      this.state = true;
      this.key = +new Date();
    },
    max_available(value) {
      this.can_submit = value;
    },
    confirm(response) {
      if (this.can_submit) {
        this.$refs.dialog.confirm_loading = true;
        this.$refs.DeploymentNewModel.submitForm({
          ok: () => {
            response.close();
            this.state = false;
          },
          err: () => {
            response.close();
          }
        });
      } else {
        this.$message.error("最大可部署实例数为0，请重新选择");
      }
    }
  }
};
</script>

<style></style>
