<template>
  <div ref="editor"></div>
</template>

<script>
import CodeMirror from 'codemirror';
import 'codemirror/mode/javascript/javascript.js';
import 'codemirror/mode/python/python.js';
import 'codemirror/addon/edit/matchbrackets';
import 'codemirror/addon/edit/closebrackets';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/idea.css';

export default {
  name: 'CodeMirrorEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      default: () => ({
        mode: 'javascript',
        lineNumbers: true,
        theme: 'idea',
        tabSize: 2,
        indentUnit: 2,
        lineWrapping: true,
      })
    }
  },
  data() {
    return {
      editor: null
    };
  },
  mounted() {
    this.editor = CodeMirror(this.$refs.editor, {
      ...this.options,
      value: this.value
    });

    this.editor.on('change', () => {
      this.$emit('input', this.editor.getValue());
    });
  },
  watch: {
    value(newValue) {
      if (newValue !== this.editor.getValue()) {
        this.editor.setValue(newValue);
      }
    },
    options: {
      deep: true,
      handler(newOptions) {
        for (const key in newOptions) {
          this.editor.setOption(key, newOptions[key]);
        }
      }
    }
  },
  beforeDestroy() {
    if (this.editor) {
      this.editor.off('change');
    }
  }
};
</script>

<style scoped>
div.CodeMirror {
  height: auto;
}
</style>
