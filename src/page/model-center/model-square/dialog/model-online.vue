<template>
  <div class="model-online">
    <div class="form-content">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="150px"
        label-position="right"
      >
        <el-form-item label="模型名称" prop="name">
          <el-input
            placeholder="请输入模型名称"
            v-model="form.name"
            class="input-lenght"
            @change="changeInputDan"
          ></el-input>
        </el-form-item>
        <el-form-item label="模型ID" prop="model_id">
          <el-input
            placeholder="请输入模型ID"
            v-model="form.model_id"
            class="input-lenght"
            :disabled="is_edit"
          ></el-input>
        </el-form-item>
        <el-form-item label="模型厂商" prop="manufacturer">
          <el-select
            class="input-lenght"
            placeholder="请输入模型厂商"
            v-model="form.manufacturer"
          >
            <el-option
              :label="item.name"
              :value="item.code"
              v-for="(item, index) of manufacturerArr"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模型类型" prop="model_type">
          <el-select
            class="input-lenght"
            placeholder="请输入模型类型"
            v-model="form.model_type"
          >
            <el-option
              :label="item.name"
              :value="item.code"
              v-for="(item, index) of model_typeArr"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模型描述" prop="description">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入模型描述"
            v-model="form.description"
            class="input-lenght"
          ></el-input>
        </el-form-item>
        <el-form-item label="上下文长度(K)" prop="context_length">
          <el-input-number
            :step-strictly="true"
            placeholder="请输入上下文长度"
            v-model="form.context_length"
            class="input-lenght"
            :min="1"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="是否推荐" prop="test">
          <el-radio-group v-model="form.recommend" class="input-lenght">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="test">
          <el-radio-group v-model="form.online" class="input-lenght">
            <el-radio :label="true">上架</el-radio>
            <el-radio :label="false">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="训练方式" prop="training_type">
          <el-select
            v-model="form.training_type"
            clearable
            multiple
            class="input-lenght"
            placeholder="请选择训练方式"
          >
            <el-option
              :label="item.name"
              :value="item.code"
              v-for="(item, index) of taskTypeArr"
              :key="index"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模型文件存储地址" prop="model_path">
          <el-input
          placeholder="请输入模型文件存储地址"
          v-model="form.model_path"
          class="input-lenght"
        ></el-input>
          <!-- <el-upload
            :headers="headers"
            :file-list="fileList"
            class="upload-demo"
            drag
            :action="`/${static_name.api_path}/sugoncloud-xiaolian-api/files/upload`"
            :on-success="model_path_success"
            :on-change="handleChange"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload> -->
        </el-form-item>
        <el-form-item
          label="推理服务配置"
          prop="inference_config"
        >
        <jsonInput @inputBlur="inferenceConfigChange" :inferenceConfig="form.inference_config"></jsonInput>
        </el-form-item>
        <el-form-item label="模型介绍" prop="model_introduction">
          <vue-quill
            v-model="form.model_introduction"
            :options="editorOption"
            class="input-lenght-4"
          ></vue-quill>
        </el-form-item>
        <!-- <el-form-item label="模型API示例" prop="model_api">
          <vue-quill
            v-model="form.model_api"
            :options="editorOption"
            class="input-lenght-4"
            @selection-change="changeInputDan"
            placeholder="请输入示例"
          ></vue-quill>
        </el-form-item> -->

        <!-- <el-form-item label="模型API示例" prop="model_api" v-if="false">
          <div style="width:100%;height:100%;margin-bottom:10px" v-for="(item,index) of apiEdit" :key="index">
            <div v-if="item.type === 'markdown'" class="apiEdit-box">
              <div class="apiEdit-content">
                <vue-quill
                  v-model="form.model_api"
                  :options="editorOption"
                  class="input-lenght-4"
                  placeholder="请输入示例"
                ></vue-quill>
              </div>
              <cl-button><i class="el-icon-delete" @click="delEditeApi(index)"></i></cl-button>
            </div>
            <div v-if="item.type === 'code'" class="apiEdit-box">
              <div class="apiEdit-content apiEdit-content-code">
                <div class="tabs">
                  <div
                    v-for="(ele,index) in item.content"
                    :key="index"
                    :class="{ 'code-active': item.current === index }"
                    @click="item.current = index"
                  >
                    {{ index }}
                  </div>
                </div>
                <codeEdit v-model="item.content[item.current]" :options="cmOptions"/>
              </div>
              <cl-button><i class="el-icon-delete" @click="delEditeApi(index)"></i></cl-button>
            </div>
          </div>
          <div>
            <cl-button @click="addEditeApi('markdown')">添加文档</cl-button>
            <cl-button @click="addEditeApi('code')">添加代码</cl-button>
          </div>
        </el-form-item> -->
      </el-form>
    </div>
    <div class="footer-btn">
      <cl-button
        @click="
          () => {
            $router.push('/modelSquence');
          }
        "
        >取消</cl-button
      >
      <cl-button type="primary" @click="submitForm('form')">确定</cl-button>
    </div>
  </div>
</template>

<script>
// import { quillEditor } from "vue-quill-editor";
import { static_name } from "@/utils";
import { createModel, get_gpu_info, editModel } from "@/http/squrare-http";
import codeEdit from "./code-edit.vue";
import jsonInput from './json-input.vue';



export default {
  name: 'modelOnline',
  components: {
    codeEdit ,
    jsonInput
  },
  data() {
    var suan_li_dan_yuan = (rule, value, callback) => {
      const isNumber = /^\d+$/.test(value);
      console.log(value);
      if (!isNumber) {
        callback(new Error("请输入大于0的自然整数"));
      } else {
        callback();
      }
    };
    var number = (rule, value, callback) => {
      const isNumber = /^\d+$/.test(value);
      if (!isNumber) {
        callback(new Error("请输入大于0的自然整数"));
      } else if (value < 1) {
        callback(new Error("请数字不能小于1"));
      } else {
        callback();
      }
    };
    var validateModelId = (rule, value, callback) => {
      const regex = /^[a-zA-Z0-9-]+$/; // 移除了下划线校验
      if (!regex.test(value)) {
        callback(new Error("只能输入英文、数字和中划线")); // 提示语同步修改
      } else {
        callback();
      }
    };
    var inferenceConfig = (rule, value, callback) => {
      try {  
        JSON.parse(value);  
        callback();  
      } catch(err) { 
        callback(new Error("请输入合法的JSON格式"));  
      } 
    };
    return {
      static_name,
      activeTab:'',
      form: {
        context_length: "",
        manufacturer: "",
        // model_api: "",
        model_id: "",
        model_introduction: "",
        model_path: "",
        model_type: "",
        name: "",
        project_id: "",
        recommend: false,
        user_id: "",
        description: "",
        online: true,
        training_type: "",
        inference_config: null
      },
      rules: {
        description: [
          {
            required: true,
            message: "请填写模型描述",
            trigger: "blur",
          },
        ],
        context_length: [
          {
            required: true,
            message: "请填写上下文长度",
            trigger: "blur",
          },
          {
            validator: number,
            trigger: "blur",
          },
        ],
        manufacturer: [
          {
            required: true,
            message: "请选择模型厂商",
            trigger: "blur",
          },
        ],
        model_id: [
          {
            required: true,
            message: "请填写模型ID",
            trigger: "blur",
          },
          {
            validator: validateModelId,
            trigger: "blur",
          },
          {
            min: 3,
            max: 50,
            message: "长度在 3 到 50 个字符",
            trigger: "blur",
          },
        ],
        model_path: [
          {
            required: true,
            message: "请输入模型文件存储地址",
            trigger: "blur",
          },
        ],
        model_type: [
          {
            required: true,
            message: "请选择模型类型",
            trigger: "blur",
          },
        ],
        // training_type: [
        //   {
        //     required: true,
        //     message: "请选择训练方式",
        //     trigger: "blur",
        //   },
        // ],
        name: [
          {
            required: true,
            message: "请填写模型名称",
            trigger: "blur",
          },
          {
            min: 3,
            max: 50,
            message: "长度在 3 到 50 个字符",
            trigger: "blur",
          },
        ],
        suan_li_dan_yuan: [
          { validator: suan_li_dan_yuan, trigger: "blur" },
          {
            required: true,
            message: "请填写算力单元",
            trigger: "blur",
          },
        ],
        user_id: [
          {
            required: true,
            message: "请填写用户ID",
            trigger: "blur",
          },
        ],
        online: [
          {
            required: true,
            message: "请选择状态",
            trigger: "blur",
          },
        ],
        inference_config: [
        {
            required: true,
            message: "请输入推理服务配置",
            trigger: "blur",
          },
          { validator: inferenceConfig, trigger: "blur" },
        ]
      },
      editorOption: {
        // 富文本编辑器配置
        placeholder: "请输入",
        modules: {
          toolbar: [
            ["bold", "italic", "underline", "strike"],
            ["blockquote", "code-block"],
            [{ header: 1 }, { header: 2 }],
            [{ list: "ordered" }, { list: "bullet" }],
            [{ script: "sub" }, { script: "super" }],
            [{ indent: "-1" }, { indent: "+1" }],
            [{ direction: "rtl" }],
            [{ size: ["small", false, "large", "huge"] }],
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            [{ color: [] }, { background: [] }],
            [{ font: [] }],
            [{ align: [] }],
            ["clean"],
            ["link", "image", "video"],
          ],
        },
        theme: "snow",
      },
      manufacturerArr: [],
      model_typeArr: [],
      headers: {},
      fileList: [],
      lengthArr: [],
      taskTypeArr: [],
      gpu_info: [],
      is_edit: false,
      // 代码编辑器配置
      code: 'const A = 10',
      cmOptions: {
        mode: 'javascript',
        lineNumbers: true,
        theme: 'idea',
        tabSize: 2,
        indentUnit: 2,
        lineWrapping: true,
      },
      apiEdit:[],
    };
  },
  created() {
    if (this.$route.params&&this.$route.params.id) {
      Object.assign(this.form, this.$route.params)
      if (this.$route.params.training_type) this.$set(this.form, 'training_type', this.$route.params.training_type.split(','))
      this.$set(this.form, 'context_length', Number(this.$route.params.context_length)/1024)
      this.is_edit = true
    }
  },
  mounted() {
    this.manufacturerArr = this.$getCode({ type: "provider" });
    this.model_typeArr = this.$getCode({ type: "model_type" });
    this.lengthArr = this.$getCode({ type: "content_length" });
    this.taskTypeArr = this.$getCode({ type: "task_type" });
    let api_header = localStorage.getItem("api_header")
      ? JSON.parse(localStorage.getItem("api_header"))
      : {};
    this.headers = {
      Authorization: api_header.Authorization,
    };
    get_gpu_info({ page_size: 9999, page_num: 1 }).then((data) => {
      this.$handle_http_back(data, true, false).then((res) => {
        this.gpu_info = res.content ? res.content.list : [];
      });
    });
  },
  methods: {
    inferenceConfigChange(val) {
      this.form.inference_config = val
    },
    changeInputDan(){
      console.log(this.form.model_api);
    },
    delEditeApi(index){
      this.apiEdit.splice(index,1)
      console.log(this.apiEdit,index);
    },
    addEditeApi(type){
      switch(type){
        case 'markdown':
          this.apiEdit.push({
            type:'markdown',
            current:'Curl',
            content:{
              data:''
            }
          })
          break;
        case 'code':
          this.apiEdit.push({
            type:'code',
            current:'Curl',
            content:{
              'Curl':'',
              'Node.js':'',
              'Python':'',
            }
          })
        break;
      }
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(fileList.length - 1);
    },
    model_path_success(res) {
      this.form.model_path =
        res.content.file_path + "/" + res.content.file_name;
    },
    submitForm(formName) {
      // console.log(this.form.model_api);
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form));
          data.user_id = GetUserInfo("userId");
          data.context_length = Number(data.context_length) * 1024;
          data.training_type = data.training_type.join(",");
          data.user_name = GetUserInfo("username");
          data.inference_config = JSON.stringify(JSON.parse(data.inference_config))
          data.property = -1
          if (this.is_edit) {
            editModel(data).then((data) => {
              this.$handle_http_back(data, false, false).then((res) => {
                this.$router.push("/modelSquence");
              });
              this.is_edit = false
            });
          } else {
            data.project_id = this.$get_projectId();
            createModel(data).then((data) => {
              this.$handle_http_back(data, false, false).then((res) => {
                this.$router.push("/modelSquence");
              });
            });
          }
        }
      });
    },
  },
};
</script>


<style lang="scss">
.model-online {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height:100%;
  width: 100%;
  .tabs {
    display: flex;
    border-bottom: 1px solid #ccc;
  }

  .tabs > div {
    padding: 10px 20px;
    cursor: pointer;
    border: 1px solid #ccc;
    border-bottom: none;
    background-color: #f8f8f8;
    font-weight: 600;
  }

  .tabs > div.code-active {
    background-color: white;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    color: var(--color-theme);
  }
  .apiEdit-box{
    width: 100%;
    display: flex;
    align-items: center;
    .apiEdit-content{
      flex: 1;
      margin-right: 10px;
      padding-bottom: 20px;
      &.apiEdit-content-code{
       border: 1px solid #ccc; 
      }
    }
  }
  .form-content{
    flex: 1;
    overflow: auto;
    padding: 20px 40px 0px 40px;
    box-sizing: border-box;
    padding-bottom: 120px;
  }
  .gpu-box {
    display: flex;
    box-sizing: border-box;
    width: 100%;
    padding: 10px 6px 0 6px;
    background: ghostwhite;
    margin-bottom: 10px;
    .input-lenght {
      flex: 1;
      & + .input-lenght {
        margin-left: 6px;
      }
    }
  }
  .footer-btn {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    border-top: 1px solid #ebeef5;
    box-sizing: border-box;
    padding-left: 24px;
  }
  .el-form-item__label {
    padding-right: 24px !important;
  }
  .input-lenght {
    width: 600px !important;
  }
  .danyuan-box-all {
    width: 600px;
    display: flex;
    align-items: center;
    span {
      display: inline-block;
      margin-right: 5px;
    }
    .danyuan-box {
      width: 50%;
      display: flex;
      align-items: center;
      .input-lenght-3 {
        flex: 1;
      }
    }
  }
}
</style>