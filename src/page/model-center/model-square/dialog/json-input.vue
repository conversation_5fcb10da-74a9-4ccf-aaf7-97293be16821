<template>  
  <div>  
    <div  
      :style="fullScreen ? fullscreenStyle : cardStyle"  
      class="arco-card"  
    >  
      <div class="arco-card-header">  
        <span style="color: transparent;">占位</span>  
        <cl-button  
          type="text"  
          style="float: right; "  
          @click="toggleFullScreen"  
        >  
          {{ fullScreen ? '退出全屏' : '全屏' }}  
        </cl-button>  
      </div>  
      <el-input  
        :style="inputStyle"  
        v-model="inferenceConfig"  
        type="textarea"  
        :rows="fullScreen ? 22 : 8"  
        placeholder="请输入JSON字符串"  
        spellcheck="false"  
        @change="inputBlur"  
        resize="none"  
      ></el-input>  
    </div>  
  </div>  
</template>  

<script>  
export default {  
  name: "ArcoJsonInput",  
  props: ["inferenceConfig"],
  data() {  
    return {  
      inferenceConfig: "",  
      fullScreen: false,   
    };  
  },  
  computed: {  
    cardStyle() {  
      return {  
        background: "#f9fbfd",  
        borderRadius: "8px",  
        boxShadow: "0 2px 8px 0 rgba(25,27,29,0.06)", 
        padding: "0",  
        overflow: "hidden",  
        border: "1px solid #e5e6eb",  
        position: "relative"  
      };  
    },  
    fullscreenStyle() {  
      let height = document.getElementById('cloud-container-content').getBoundingClientRect().height
      let width = document.getElementById('cloud-container-content').getBoundingClientRect().width
      
      return {  
        position: "fixed",  
        top: '105px',  
        left: '226px',  
        width: width + "px",  
        height: height + "px", 
        background: "#f9fbfd",  
        zIndex: 9999,  
        borderRadius: 0,  
        margin: 0,   
        boxShadow: "none",  
        border: "none",  
        padding: 0,  
        overflow: "auto",  
        transition: "all 0.2s"  
      };  
    },  
    inputStyle() {  
      return {   
        background: "#fff",  
        border: "1px solid #e5e6eb", 
      };  
    }  
  },  
  methods: {  
    inputBlur(val) {  
      console.log(val);
      
      this.$emit('inputBlur', val)  
    },  
    toggleFullScreen() {  
      this.fullScreen = !this.fullScreen;  
    }  
  }  
};  
</script>  

<style lang="scss" scoped>  
>>> .el-textarea {
  border: none !important;
  height: calc(100% - 50px);
}
>>> .el-textarea__inner {
  border: none !important;
  height: 100% !important;
}
.arco-card {  
  transition: box-shadow .2s;  
}  

.arco-card-header {  
  background: #f7f8fa;  
  border-bottom: 1px solid #e5e6eb;  
  border-top-left-radius: 10px;  
  border-top-right-radius: 10px;  
  height: 40px;  
  display: flex;  
  align-items: center;  
  padding: 0 20px;  
  font-size: 14px;  
  justify-content: space-between;  
}  

</style>