<template>
  <div class="card-list-square" ref="cardList" v-loading="loading">
    <div
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 30px;
      "
      v-if="!cards.length"
    >
      <div style="width: 106px; height: 99px">
        <img src="../../../assets/images/noresult.png" />
      </div>
      <p>
        <i class="fa fa-info-circle"></i>
        没有查询到符合条件的记录
      </p>
    </div>
    <div
      class="card-box"
      :style="{
        width: cardWidth,
        marginRight: (index + 1) % speed !== 0 ? '20px' : '',
      }"
      v-for="(card, index) of cards"
      :key="index"
    >
      <div class="card-header">
        <img :src="transType(card.manufacturer, 'img')" alt="" />
        <div class="card-title-content">
          <div class="card-title">
            <span class="text">{{ card.name }}</span>
            <div
              class="card-tag"
              v-if="transLine(card)"
              :class="transLine(card) === '未上架' ? 'noLine' : ''"
            >
              {{ transLine(card) }}
            </div>
          </div>
          <p class="tag-box">
            <span class="tag-text">{{ transType(card.model_type) }}</span>
            <span class="tag-text"
              >{{ Math.floor(card.context_length / 1024) }}K</span
            >
          </p>
        </div>
      </div>
      <div class="card-discripiton">
        {{ card.description }}
      </div>
      <div class="card-footer-msg">
        <span>{{ transType(card.manufacturer) }}</span>
        <span>{{ transTime(card.update_time) }}</span>
      </div>
      <div class="card-footer-btn">
        <div class="card-footer-btn-item" @click="go_detail(card)">
          查看详情
        </div>
        <div class="card-footer-btn-item" @click="go_detail(card, 'api')">
          API调用示例
        </div>
        <div
          class="card-footer-btn-item"
          v-if="card.model_type == 'TG' || card.model_type == 'IU'"
          :class="is_disabled2(card)"
          @click="tiyan(card, is_disabled2(card))"
        >
          立即体验
        </div>
        <div class="card-footer-btn-item" v-if="$store.state.isAdmin">
          <el-dropdown size="small" placement="bottom" trigger="click">
            <div class="el-dropdown-link">操作</div>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click="openDialog(card, 'editDialog')">
                编辑
              </el-dropdown-item>
              <el-dropdown-item @click="openDialog(card, 'delDialog')">
                删除
              </el-dropdown-item>
              <el-dropdown-item @click="openDialog(card, 'onLineStatus')">
                {{ transStautsBtn(card.model_enum, "deploy_status") }}
              </el-dropdown-item>
              <el-dropdown-item @click="openDialog(card, 'online')">
                {{ card.online ? "下架" : "上架" }}
              </el-dropdown-item>
              <el-dropdown-item @click="openDialog(card, 'recommend')">
                {{ card.recommend ? "取消推荐" : "推荐" }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>

        <!-- <div class="card-footer-btn-item" @click="$emit('onLineStatus',card)">{{ card.status?'下线':'上线' }}</div> -->
      </div>
    </div>
  </div>
</template>

<script>
import mixin from "./mixin.js";
export default {
  mixins: [mixin],
  props: {
    loading: { type: Boolean, default: false },
    cards: { type: Array, default: () => [] },
  },
  data() {
    return {
      speed: 4,
    };
  },
  computed: {
    cardWidth() {
      let str = `calc((100% - ${(this.speed - 1) * 20}px) / ${this.speed})`;
      return str;
    },
  },
  mounted() {
    this.$nextTick(() => {
      const setWidth = () => {
        if (this.$refs.cardList) {
          let width = this.$refs.cardList.clientWidth;
          if (width < 1200) {
            this.speed = 3;
          } else {
            this.speed = 4;
          }
          this.$emit("cardSizeChange", this.speed);
        }
      };
      setWidth();
      window.windowScale(() => {
        setWidth();
      }, "all");
    });
  },
  methods: {
    transLine(data) {
      if (!data.online) {
        return "未上架";
      } else if (data.recommend) {
        return "最新推荐";
      } else {
        return "";
      }
    },
    tiyan(card, dis) {
      if (dis) return;
      if (card.model_type == 'TG') {
        this.$router.push(
          `/modelExperience?name=${card.name}&model_id=${card.deployment_id}&mode_type=1`
        );
      } else if (card.model_type == 'IU') {
        this.$router.push(
          `/viewModelExperience?name=${card.name}&model_id=${card.deployment_id}&mode_type=1`
        );
      }
      
    },
    is_disabled(card) {
      if (!card.online) {
        return "btn-disabled";
      } else {
        return "";
      }
    },
    is_disabled2(card) {
      if (card.model_enum != "1") {
        return "btn-disabled";
      } else {
        return "";
      }
    },
    openDialog(data, type) {
      this.$emit("handle", data, type);
    },
  },
};
</script>

<style lang="scss">
.card-list-square {
  width: 100%;
  overflow: auto;
  height: 100%;
  .card-box {
    width: calc((100% - 60px) / 4);
    padding: 16px;
    box-sizing: border-box;
    border-radius: 4px;
    overflow: hidden;
    background: white;
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.2s;
    float: left;
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    .card-footer-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .card-footer-btn-item {
        width: 100%;
        box-sizing: border-box;
        position: relative;
        height: 32px;
        text-align: center;
        line-height: 32px;
        color: #1487fd;
        font-weight: 400px;
        cursor: pointer;
        &.btn-disabled {
          position: relative;
          cursor: not-allowed;
          color: gainsboro;
          &:hover {
            color: gainsboro;
          }
          &::after {
            content: "";
            position: absolute;
            display: block;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
            left: 0;
            top: 0;
          }
        }
        .el-dropdown-link {
          color: #1487fd;
          font-weight: 400px;
          cursor: pointer;
          font-size: 12px;
          &:hover {
            color: #075eb5;
          }
        }
        &:hover {
          color: #075eb5;
        }
        &::before {
          position: absolute;
          display: block;
          content: "";
          width: 1px;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          background: #eaeaeb;
          height: 18px;
        }
        &:last-child::before {
          display: none;
        }
      }
    }
    .card-footer-msg {
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #898b8e;
      margin-bottom: 8px;
    }
    .card-discripiton {
      width: 100%;
      height: 48px;
      overflow: hidden;
      font-size: 12px;
      line-height: 16px;
      margin-bottom: 8px;
      margin-top: 8px;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }
    .card-header {
      display: flex;
      align-items: center;
      img {
        height: 40px;
        margin-right: 16px;
      }
      .card-title-content {
        display: flex;
        flex: 1;
        flex-direction: column;
        .tag-box {
          display: flex;
          margin-top: 4px;
          .tag-text {
            height: 20px;
            gap: 10px;
            border-radius: 2px;
            padding: 0 10px;
            line-height: 20px;
            background: #f7f7f7;
            & + .tag-text {
              margin-left: 4px;
            }
          }
        }
        .card-title {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          height: 20px;
          .text {
            font-size: 13px;
            color: #010205;
            font-weight: 600;
          }
          .card-tag {
            width: 68px;
            height: 100%;
            background: var(--color-theme);
            gap: 10px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            &.noLine {
              background: #cec8c7;
              color: #28202159;
            }
          }
        }
      }
    }
  }
}
</style>
