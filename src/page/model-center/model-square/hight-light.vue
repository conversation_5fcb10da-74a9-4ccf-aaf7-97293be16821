<template>
  <div>
    <pre v-highlightjs><code ref="codeBlock" :class="`language-javascript`">{{ code }}</code></pre>
  </div>
</template>

<script>
  import hljs from 'highlight.js';
  import 'highlight.js/styles/xcode.css';
export default {
    props:['code'],
    mounted(){
        this.$nextTick(()=>{
           this.highlightCode();
        })
    },
    methods:{
        highlightCode() {
            const codeBlock = this.$refs.codeBlock;
            if (codeBlock) {
            // 移除 dataset.highlighted 属性
            // 重新高亮代码
                hljs.highlightBlock(codeBlock);
            }
        }
    }
}
</script>

<style>
 pre {
    margin: 0;
    padding: 20px;
    border: 1px solid #ccc;
    border-top: none;
    background-color: #f8f8f8;
  }
  
  code {
    white-space: pre-wrap; /* 保持换行 */
  }
</style>