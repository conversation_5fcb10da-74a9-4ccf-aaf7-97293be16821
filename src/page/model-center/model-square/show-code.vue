<template>
    <div class="code-tabs">
      <div class="tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          :class="{ 'code-active': activeTab === tab }"
          @click="activeTab = tab"
        >
          {{ tab }}
        </div>
      </div>
      <!-- <el-tabs v-model="activeTab" value="">
        <el-tab-pane :label="tab" :name="tab" v-for="(tab, index) in tabs" :key="index"></el-tab-pane>
      </el-tabs> -->
      <div v-for="(tab, index) in tabs" :key="index">
        <hightLight :code="code[tab] " v-if="activeTab === tab" />
      </div>
    </div>
  </template>
  
  <script>
  import hightLight from './hight-light.vue'
  export default {
    components: {
      hightLight
    },
    props: {
      tabs: {
        type: Array,
        required: true
      },
      code: {
        type: Object,
        required: true
      }
    },
    data() {
      return {
        activeTab: this.tabs[0]
      };
    },
    methods: {

    },
    mounted() {
    
    }
  };
  </script>
  
  <style scoped>
  .code-tabs {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .tabs {
    display: flex;
    border-bottom: 1px solid #ccc;
  }
  
  .tabs > div {
    padding: 10px 20px;
    cursor: pointer;
    border: 1px solid #ccc;
    border-bottom: none;
    background-color: #f8f8f8;
    font-weight: 600;
  }
  
  .tabs > div.code-active {
    background-color: white;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    color: var(--color-theme);
  }
  
  pre {
    margin: 0;
    padding: 20px;
    border: 1px solid #ccc;
    border-top: none;
    background-color: #f8f8f8;
  }
  
  code {
    white-space: pre-wrap; /* 保持换行 */
  }
  </style>