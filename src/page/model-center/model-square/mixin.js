import {del_model_by_id,update_model_online_status,update_model_recommend_status,update_model_online} from '@/http/squrare-http'
export default {
    methods: {
        transStauts(val,type){
            return this.$getCode({code:String(val),type:type}).description
        },
        transStautsBtn(val){
            if(val=='7'){
                return '上线'
            }else{
                return '下线'
            }
        },
        go_detail(data,api){
            if(api){
                this.$go_detail(this,`/ModelSquenceDetail?id=${data.model_id}&type=api&modelType=${data.model_type}`,{})
            }else{
                this.$go_detail(this,`/ModelSquenceDetail?id=${data.model_id}&modelType=${data.model_type}`,{})
            }
        },
        transTime(time){
            return time?window.globalFilter.dateformat(time,'YYYY-MM-DD'):'--'
        },
        transType(code,isImage){
            let msg = this.$getCode({code})
            if(isImage){
                return msg?msg.description:''
            }else{
                return msg?msg.name:'--'
            }
        },
        openDialog(item, type) {
            let status = item.status?'下线':'上线'
            let recommend = item.recommend?'取消推荐':'推荐'
            let online = item.online?'下架':'上架'
            let _this = this
            switch (type) {
                case "delDialog":
                    this.select_list = [item];
                    this.dialogDel = true;
                    break;
                case 'onLineStatus':
                    if(String(item.model_enum) == '7'){
                        this.$refs.modelUpDialog.open(item)
                    }else{
                        if(String(item.model_enum) == '4'){
                            this.$message({
                                message: '正在下线中，请勿重复操作',
                                type: 'warning'
                            })
                        }else{
                            this.$refs.stopDialog.open(item)
                        }
                    }
                break
                case 'recommend':
                    if(!item.online){
                        this.$message({
                            message: '请先上架模型',
                            type: 'warning'
                        })
                        return
                    }
                    this.$clAlert({
                        title: recommend,
                        pre_message:`确定${recommend}`,
                        message: item.name,
                        after_message:'吗？',
                        type: 'alert',
                        confirmText:'确定',
                        loadingText:'确定中...',
                        ok: function (handleClose,closeConfirmLoading) {
                            update_model_recommend_status(item.id,!item.recommend).then(data=>{
                                closeConfirmLoading()
                                handleClose()
                                _this.$handle_http_back(data,false,false).then((res)=>{
                                    _this.getList()
                                })
                            }).catch((err)=>{
                                console.error(err)
                                closeConfirmLoading()
                            })
                        },
                        cancel: function () {
                        }
                    })
                break
                case 'online':
                    this.$clAlert({
                        title: online,
                        pre_message:`确定${online}`,
                        message: item.name,
                        after_message:'吗？',
                        type: 'alert',
                        confirmText:'确定',
                        loadingText:'确定中...',
                        ok: function (handleClose,closeConfirmLoading) {
                            update_model_online(item.id,!item.online).then(data=>{
                                closeConfirmLoading()
                                handleClose()
                                _this.$handle_http_back(data,false,false).then((res)=>{
                                    _this.getList()
                                })
                            }).catch((err)=>{
                                console.error(err)
                                closeConfirmLoading()
                            })
                        },
                        cancel: function () {
                        }
                    })
                break
            case "editDialog":
                this.$router.push({
                    name: 'modelOnline',
                    params: item
                })
                break;
            }
        },
          
        delTableChoose(response) {
            let value = response.data.map((el) => {
             console.log(el,'8888')
              return {
                name: el.name,
                params: el.id,
                request: del_model_by_id,
              };
            });
            const that = this;
            const batch_delete = new this.$batch_delete({
              data: value,
              title: "删除提示",
              success: function () {
                that.dialogDel = false;
                that.getList();
              },
              error: function (err) {
                that.dialogDel = false;
                that.getList();
              },
            });
            /**发送请求 */
            batch_delete.del();
          },
    },
}