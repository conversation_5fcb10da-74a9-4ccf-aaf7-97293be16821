<template>
  <div class="model-square-detail">
    <div class="sugon-custom-back-box">
      <div class="sugon-custom-back-content">
        <div class="sugon-custom-back-content-box" @click="go_back">
          <i class="el-icon-back sugon-custom-back-icon"></i>
          <span class="sugon-custom-back-name">{{name}}</span>
          <div class="model-square-detail-header">
          <div class="img-box">
              <img :src="transType(detail.manufacturer, 'img')" alt="" />
            </div>
            <div>
              <div class="msg-box">
                <span class="msg-name">{{ detail.name }}</span>
                <span class="model_type">{{ transType(detail.model_type) }}</span>
              </div>
              <div class="msg-box" style="margin-top: 5px;" @click.stop="">
                <div class="manufacturer">
                  {{ detail.model_id }}
                  <el-tooltip
                    effect="dark"
                    content="模型Id，用于模型调用"
                    placement="right"
                  >
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                  <i class="el-icon-document-copy" @click.stop="copy(detail.model_id)"></i>
                </div>
                <i class="block-line"></i>
                <span class="manufacturer"
                  >由{{ transType(detail.manufacturer) }}提供</span
                >
                <i class="block-line"></i>
                <span class="manufacturer">{{
                  transTime(detail.update_time)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="sugon-custom-back-divider"></div> 
    </div>
    <div style="background: white; padding-top: 10px;flex:1" v-loading="loading" element-loading-text="数据加载中">
      <el-tabs type="border" :before-leave="tabClick" v-model="tab_type">
        <el-tab-pane label="模型介绍" :key="1" name="des">
          <div v-html="detail.model_introduction"></div>
        </el-tab-pane>
        <el-tab-pane label="API示例" :key="2" name="api">
          <div v-html="detail.model_api"></div>
          <div class="api-box" v-for="(item, index) of api_demo" :key="index">
            <div
              class="markdown-cotent"
              v-if="item.type == 'markdown'"
              v-html="item.content.data"
            ></div>
            <div class="api-box-btn" v-else style="margin:14px 0;">
              <showCode
                :tabs="tabs(item)"
                :code="item.content"
              ></showCode>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { get_model_detail, get_model_sample } from "@/http/squrare-http";
import mixin from "./mixin";
import showCode from "./show-code.vue";

export default {
  components: {
    showCode,
  },
  mixins: [mixin],
  data() {
    return {
      id: this.$route.query.id,
      detail: {},
      tab_type: "api",
      api_demo: [],
      codeBlocks: {},
      loading:true,
      modelType:''
    };
  },
  created() {
    this.tab_type = this.$route.query.type || "des";
    this.modelType = this.$route.query.modelType
  },
  methods:{
    tabs(codeData){
      if(!codeData){
        return []
      }else{
        return Object.keys(codeData.content)
      }
    },
    copy(text){
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('复制成功')
    },
    go_back(){
      this.$back_list(this)
    },
  },
  mounted() {
    get_model_detail(this.id).then((data) => {
      this.$handle_http_back(data, true, false).then((res) => {
        this.loading = false;
        this.detail = res.content ? res.content : {};
        console.log(JSON.parse(this.detail.property), "property");
        console.log(this.detail, "this.detail");
      });
    }).catch((err) => {
      this.loading = false;
    });
    get_model_sample(this.id,this.modelType).then((data) => {
      this.loading = false;
      this.$handle_http_back(data, true, false).then((res) => {
        console.log(res, "res");
        this.api_demo = res.content ? res.content : [];
      });
    }).catch((err) => {
      this.loading = false;
    });
  },
};
</script>

<style lang="scss">
.model-square-detail {
  width: 100%;
  box-sizing: border-box;
  padding: 24px 24px 0 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-icon-document-copy{
    margin-left: 5px;
    &:hover{
      color: var(--color-theme);
    }
  }
  .sugon-custom-back-box .sugon-custom-back-content {
    padding-left: 0;
    border-bottom: 1px solid gainsboro;
    height: 68px;
  }
  .el-tabs__nav-scroll {
    padding-top: none;
    background: white;
  }
  .sugon-custom-back-box .sugon-custom-back-divider {
    background: white;
  }
  .el-tabs__nav-scroll .el-tabs__nav .el-tabs__item {
    margin-right: 0 !important;
  }
  .model-square-detail-header {
    display: flex;
    align-items: center;
    .msg-box {
      height: 20px;
      display: flex;
      align-items: center;
      .block-line{
        height: 75%;
        width: 1px;
        background: gainsboro;
        display: inline-block;
        margin: 0 10px;
      }
      .msg-name {
        font-weight: 500;
        font-size: 14px;
        letter-spacing: 0%;
        color: #010205;
        display: inline-block;
        margin-right: 5px;
      }
      .model_type {
        padding: 0 10px;
        background: #f7f7f7;
        display: flex;
        align-items: center;
      }
      .manufacturer {
        color: #898b8e;
      }
    }
    .img-box {
      width: 48px;
      height: 48px;
      border: 1px solid #292c331a;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 16px;
      img {
        width: 32px;
      }
    }
  }
}
</style>