<template>
  <div class="model-square">
    <div class="model-square-header" ref="header">
      <div class="model-square-header-left">
        <p class="title">模型广场</p>
        <p class="msg">
          曙光云霄练AI平台，是一站式的大模型开发及应用构建平台，模型广场汇集了时下主流的大模型，用户可以自由地查看及体验模型先进能力。
        </p>
        <cl-button type="primary" class="tiyan-btn" @click="$router.push('/modelExperience')">立即体验</cl-button>
      </div>
      <img src="../../../assets/images/square.jpg" alt="" height="100%" />
    </div>
    <div class="square-content">
      <div class="search-and-title">
        <div class="title-box">
          <span>共{{ page_total }}个模型</span>
          <cl-button
            v-if="$store.state.isAdmin"
            type="primary"
            @click="
              () => {
                $router.push('/modelOnline');
              }
            "
            >模型上架</cl-button
          >
        </div>
        <div class="search-box">
          <el-select class="search-select" v-model="manufacturer" clearable placeholder="请选择厂商" @change="getList">
            <el-option
              :label="item.name"
              :value="item.code"
              v-for="(item, index) of modeles"
              :key="index"
            ></el-option>
          </el-select>
          <el-select
            class="search-select"
            placeholder="请选择模型类型"
            clearable
            v-model="model_type"
            @change="getList"
          >
            <el-option
              :label="item.name"
              :value="item.code"
              v-for="(item, index) of types"
              :key="index"
              @change="getList"
            ></el-option>
          </el-select>
          <el-select
            style="width: 160px;"
            class="search-select"
            placeholder="请选择上下文长度"
            v-model="content_windows_value"
            clearable
            @change="getList"
          >
            <el-option
              :label="item.name"
              :value="item.code"
              v-for="(item, index) of lengths"
              :key="index"
            ></el-option>
          </el-select>
          <el-input
            placeholder="请输入模型名称"
            class="search-input"
            v-model="name"
            @keyup.native.enter="getList"
          ></el-input>
          <cl-button class="search-button" type="border" @click="getList"
            >搜索</cl-button
          >
          <div style="width:8px"></div>
          <div class="icon-btn" :class="{active:table_show === 'card'}" @click="changeList('card')">
            <i class="iconfont icon-card-list"></i>
          </div>
          <div class="icon-btn" :class="{active:table_show === 'table'}" @click="changeList('table')">
            <i class="iconfont icon-table-list"></i>
          </div>
        </div>
      </div>
      <div style="flex: 1; width: 100%; overflow: hidden">
        <cardList @cardSizeChange="cardSizeChange" :cards="table_list" :loading="table_loading" v-if="table_show === 'card'" @handle="(item,type)=>openDialog(item,type)"/>
        <cl-table
          v-else
          height="100%"
          ref="all_model_repository"
          :data="table_list"
          v-loading="table_loading"
          element-loading-text="加载中..."
          @selection-change="tableSelect"
          :columns="table_colunms"
        ></cl-table>
      </div>
      <div style="height: 8px"></div>
      <el-pagination
        class="is-background"
        @size-change="pageSizeChange"
        @current-change="pageChange"
        :current-page="page_num"
        :page-sizes="size_arr"
        :page-size="page_size"
        layout="sizes, total, jumper, next, pager, prev"
        :total="page_total"
      ></el-pagination>
    </div>
    <sugon-delete-dialog
        :list="select_list"
        :isShow.sync="dialogDel"
        @confirm="delTableChoose"
        :title="select_list.length > 1 ? '批量删除' : '删除'"
        nameKey="name"
        idKey="model_id"
      ></sugon-delete-dialog>
      <modelUpDialog ref="modelUpDialog"/>
      <stopDialog :isSquare="true" ref="stopDialog" @ok="getList"></stopDialog>
  </div>
</template>

<script>
import cardList from "./card-list.vue";
import { get_model } from "@/http/squrare-http";
import mixin from "./mixin.js";
import modelUpDialog from "./dialog/model-up-dialog.vue";
import stopDialog from "../model-deployment/handle/stop-dialog.vue";
export default {
  mixins: [mixin],
  name:'modelSquare',
  components: { cardList ,modelUpDialog,stopDialog},
  data() {
    return {
      total: 23,
      modeles: [],
      types: [
        {
          label: "deepSeek",
          value: "1",
        },
      ],
      lengths: [
        {
          label: "deepSeek",
          value: "1",
        },
      ],
      table_loading: false,
      page_num: 1,
      page_size: 10,
      select_list: [],
      table_list: [],
      page_total: 0,
      dialogDel: false,
      search_value: "",
      content_windows_value: "",
      manufacturer: "",
      model_type: "",
      name: "",
      table_colunms: [],
      table_select:[],
      table_show:'card',
      size_arr:[10, 20, 50, 100],
      cardSize:4,
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.modeles = this.$getCode({ type: "provider" });
      this.types = this.$getCode({ type: "model_type" });
      this.lengths = this.$getCode({ type: "content_length" });
      this.getList();
    });
  },
  created() {
    this.table_colunms = [
      {
        prop: "name",
        label: "模型名称",
        render:(h,scope)=>{
          return <a onClick={()=>this.go_detail(scope.row)}>{scope.row.name}</a>
        }
      },
      {
        prop: "context_length",
        label: "上下文长度",
        render: (h, scope) => {
          return <span>{Math.floor(scope.row.context_length / 1024)}k</span>;
        },
      },
      {
        prop: "manufacturer",
        label: "厂商",
        render: (h, scope) => {
          return <span>{this.transType(scope.row.manufacturer)}</span>;
        },
      },
      {
        prop: "model_type",
        label: "模型类型",
        render: (h, scope) => {
          return <span>{this.transType(scope.row.model_type)}</span>;
        },
      },
      {
        prop: "recommend",
        label: "是否推荐",
        render: (h, scope) => {
          return <span>{scope.row.recommend ? "是" : "否"}</span>;
        },
      },
      {
        prop: "online",
        label: "上架状态",
        render: (h, scope) => {
          return <span>{scope.row.online ? "上架" : "未上架"}</span>;
        },
      },
      {
        prop: "status",
        label: "上线状态",
        render: (h, scope) => {
          return <span>{this.transStauts(scope.row.model_enum,'deploy_status')}</span>;
        },
      },
      {
        prop: "update_time",
        label: "更新时间",
        render: (h, scope) => {
          return <span>{this.transTime(scope.row.update_time)}</span>;
        },
      },
      {
        label: "操作",
        width: "60",
        hidden:!this.$store.state.isAdmin,
        render: (h, scope) => (
          <el-dropdown size="small" placement="bottom" trigger="click">
            <cl-button icon="el-icon-setting" title="操作"></cl-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                onClick={() => {
                  this.openDialog(scope.row, "editDialog");
                }}
              >
                编辑
              </el-dropdown-item>
              <el-dropdown-item
                onClick={() => {
                  this.openDialog(scope.row, "delDialog");
                }}
              >
                删除
              </el-dropdown-item>
              <el-dropdown-item
                onClick={() => {
                  this.openDialog(scope.row, "onLineStatus");
                }}
              >
                {this.transStautsBtn(scope.row.model_enum,'deploy_status')}
              </el-dropdown-item>
              <el-dropdown-item
                onClick={() => {
                  this.openDialog(scope.row, "online");
                }}
              >
                {scope.row.online ? "下架" : "上架"}
              </el-dropdown-item>
              <el-dropdown-item
                onClick={() => {
                  this.openDialog(scope.row, "recommend");
                }}
>
                {scope.row.recommend ? "取消推荐" : "推荐"}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        ),
      },
    ];
  },
  methods: {
    cardSizeChange(size){
      this.cardSize = size
      this.cardSizeFn(size)
    },
    cardSizeFn(size){
      if(this.table_show === 'card'){
        if(size == 3){
          this.size_arr = [6, 12, 30, 60]
          this.page_size = 6
          this.page_num = Math.floor((this.page_size*this.page_num)/this.page_size);
        }else if(size == 4){
          this.size_arr = [12, 16, 40, 80]
          this.page_size = 12
          this.page_num = Math.floor((this.page_size*this.page_num)/this.page_size);
        }
      }else{
        this.size_arr = [10, 20, 50, 100]
        this.page_size = 10
        this.page_num = Math.floor((this.page_size*this.page_num)/10);
      }
      this.getList()
    },
    changeList(type){
      this.table_show = type
      this.cardSizeFn(this.cardSize)
    },
    pageSizeChange(size) {
      this.page_size = size;
      this.getList();
    },
    pageChange(num) {
      this.page_num = num;
      this.getList();
    },
    getList() {
      const send = {
        page_num: this.page_num,
        page_size: this.page_size,
        content_windows_value: this.content_windows_value,
        manufacturer: this.manufacturer,
        model_type: this.model_type,
        name: this.name,
        // project_id: localStorage.ProjectId,
        // user_id: GetUserInfo("userId"),
      };
      this.table_loading = true;
      get_model(send)
        .then((data) => {
          this.table_loading = false;
          this.$handle_http_back(data, true, false).then((res) => {
            this.table_list = res.content ? res.content.list : [];
            this.page_total = res.content ? res.content.total : 0;
          });
        })
        .catch((err) => {
          this.table_loading = false;
        });
    },
  },
};
</script>

<style lang="scss">
.model-square {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  .square-content {
    // .table-main{
    //   padding: 0 !important;
    // }
    .el-table{
      margin-bottom: 0 !important;
    }
    background: #f5f6f9;
    flex: 1;
    box-sizing: border-box;
    padding: 24px 24px 24px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .search-and-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      .title-box {
        display: flex;
        align-items: center;
        span {
          color: #010205;
          font-size: 18px;
          display: inline-block;
          margin-right: 10px;
        }
      }
      .search-box {
        display: flex;
        align-items: center;
        margin-bottom:10px;
        .icon-btn{
          height: 32px;
          width: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &:hover{
            background: #e9e9ed;
          }
          &.active{
            color: var(--color-theme);
          }
          &+.icon-btn{
            margin-left: 8px;
          }
        }
        .search-select {
          width: 144px;
          & + .search-select {
            margin-left: 16px;
          }
        }
        .search-input {
          width: 240px;
          margin-left: 16px;
        }
      }
    }
  }
  .model-square-header {
    width: 100%;
    height: 210px;
    background: #e4e9ef;
    // background: url('../../../assets/images/square.jpg') no-repeat;
    // background-size: cover;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding-left: 92px;
    display: flex;
    .model-square-header-left {
      flex: 1;
      z-index: 3;
    }
    img {
      height: 100%;
      position: absolute;
      right: 0;
      top: 0;
    }
    .title {
      color: #010205;
      font-size: 20px;
      font-weight: 600;
      margin-top: 38px;
    }
    .msg {
      width: 621px;
      font-size: 14px;
      margin-top: 10px;
      write-wrap: break-word;
    }
    .tiyan-btn {
      margin-top: 32px;
      width: 107px;
      .cloud-button-btn {
        text-align: center;
      }
    }
  }
}
</style>