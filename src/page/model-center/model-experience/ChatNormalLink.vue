<template>
  <div>
    <div class="data-source link-seleect" @click="handleChangeLink">
      <div class="tips">找到相关链接资料{{ item.source_documents.length }}篇</div>
      <div>
        <i :class="item.showDetail ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
      </div>
    </div>
    <div v-show="item.showDetail" class="data-source">
      <div v-for="(sourceItem, sourceIndex) in item.source_documents" :key="sourceIndex">
        <p>
          <span class="dot"></span>
          <a :href="sourceItem.chunk_id" target="_blank">{{ sourceItem.file_name }}</a>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props:{
    item: {
        type: Object,
        default: () => {},
      },
    indexItem: { type: Number, default: 0 },
  },
  methods:{
    handleChangeLink(){
      this.$emit('handleChangeLink', this.indexItem)
    }
  }
}
</script>

<style lang="scss">
.data-source {
  margin-left: 50px;
  font-size: 14px;
  // line-height: 22px;
  background: rgba(50, 53, 60, 0.08);
  padding: 16px 12px;
  border-radius: 4px;

  a {
    color: var(--color-blue-light);
    font-size: 12px;

    &:hover {
      color: var(--color-blue-light) !important;
      text-decoration: underline;
    }
  }
}

.link-seleect {
  cursor: pointer;
  width: 210px;
  background: rgba(50, 53, 60, 0.08);
  display: flex;
  justify-content: space-between;
  color: var(--color-theme-text);
  margin: 10px 0;
  margin-left: 50px;
  padding: 6px 8px;
}
.dot {
  width: 4px; /* 圆点的宽度 */
  height: 4px; /* 圆点的高度 */
  background-color: var(--color-blue-light); /* 圆点的颜色 */
  border-radius: 50%; /* 使形状为圆形 */
  display: inline-block; /* 使圆点可以与其他元素并排显示 */
  margin: 10px; /* 添加一些外边距 */
  vertical-align: middle;
}
</style>
