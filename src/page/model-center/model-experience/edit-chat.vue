<template>
  <sugon-dialog :isShow.sync="state" @confirm="confirm" width="600" title="编辑对话">
    <div class="sugon-dialog-out-padding">
      <el-form ref="form" :model="form" label-width="100px" :rules="rules" class="demo-ruleForm">
        <el-form-item label="对话名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </sugon-dialog>
</template>

<script>
import { editChat } from '@/http/chat-http/chat-base-http.js'
export default {
  data() {
    return {
      state: false,
      form: {
        name: '',
        id: ''
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      }
    }
  },
  methods: {
    open(data) {
      if (data) {
        Object.assign(this.form, data)
      }
      this.state = true
    },
    confirm(response) { //如果表单有校验，会自动校验。不需另外写element的校验函数。response返回有按钮loading关闭函数
      editChat({ chat_session_id: this.form.id, name: this.form.name }).then(data=> {
        response.close()
        this.$handle_http_back(data, false, false).then(res => {
          this.state = false
          this.$emit('OK', this.form.id)
        })
      }).catch((err) => {
        response.close()
      })
    }
  }
}
</script>

<style>

</style>
