<template>
  <sugon-dialog :isShow.sync="state" @confirm="confirm" width="600" title="模型配置">
    <div class="sugon-dialog-out-padding">
      <el-form :model="modelInfos" :rules="rules" ref="ruleForm" label-width="130px" class="demo-ruleForm" label-position="right">
        <div v-for="(item, index) in modelInfos" :key="index">
          <el-form-item
            :label="item.displayName"
            :prop="item.name"
            :key="item.name"
          >
            <span slot="label">
              {{ item.displayName }}
              <el-tooltip placement="top-start">
                <div slot="content">{{ item.description }}</div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
            <el-input-number
              v-if="item.type == 'number' || item.type == 'intInputNumber'"
              style="width: 100%;"
              :step="item.step"
              :min="item.range[0]"
              :max="item.range[1]"
              v-model="item.recommendValue"
            ></el-input-number>
            <el-input-number
              v-if="item.type == 'floatInputNumber'"
              :precision="2"
              style="width: 100%;"
              :step="item.step"
              :min="item.range[0]"
              :max="item.range[1]"
              v-model="item.recommendValue"
            ></el-input-number>
            <el-input
              v-if="item.type == 'input'"
              style="width: 100%;"
              v-model="item.recommendValue"
            ></el-input>
            <el-select
              style="width: 100%;"
              v-model="item.recommendValue"
              v-if="item.type == 'select'"
            >
              <el-option
                :label="item"
                :value="item"
                v-for="(item, index) of item.supportValues"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </sugon-dialog>
</template>

<script>
export default {
  data() {
    return {
      state: false,
      rules: {},
      modelInfos: []
    }
  },
  methods: {
    open(data) {
      console.log('info-----', data)
      this.modelInfos = data
      this.state = true
    },
    confirm(response) {
      let _p = {}
      this.modelInfos.forEach(e => {
        _p[e.name] = e.recommendValue
      })
      this.$emit('OK', _p)
      response.close()
      this.handleClose()
    },
    handleClose() {
      this.state = false
      this.modelInfos = []
    }
  }
}
</script>