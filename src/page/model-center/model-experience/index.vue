<template>
  <div v-loading="loading" style="width: 100%; height: 100%; overflow: hidden;" element-loading-background="rgba(255, 255, 255, 1)">
    <!-- <Main ref="mainChat" v-if="!isDefault" :modelObj="modelObj" @handleOpenModel="handleOpenModel" /> -->
    <DefaultPage v-if="isDefault === true" @handleOpenModel="handleOpenModel" @handleShowChat="handleShowChat" @setLoading="setLoading" />
    <div v-if="isDefault === false" class="model-experience-content">
      <!-- <div class="model-experience-top">
        <div class="title-left">{{ $route.name === 'viewModelExperience' ? '视觉模型' : '文本模型' }}</div>
      </div> -->
      <div class="model-experience-main">
        <ChatHistoryList :chatList="historyList" :currentId="currentId" :canAdd="canAdd"
          @changeCurrent="changeCurrent" @createChat="handleOpenModel()"
          @editChat="editChat" @deleteChat="deleteChat" />
        <Chat ref="chatList" :currentId="currentId" :loading="chatLoading" :modelObj="modelObj" :modelInfos="modelInfos" :isShowErrorTips="isShowErrorTips" :showText="showText" :QA_List="QA_List" @handleOpenInfo="handleOpenInfo"
          @OK="getHistoryList()" @setAdd="setAdd" @handleClearChat="handleClearChat" @handleOpenModel="handleOpenModel" />
      </div>
    </div>
    <Modelselect ref="Modelselect" @OK="handleShowChat"></Modelselect>
    <ModelInfo ref="ModelInfo" @Ok="handleSetModelInfo" />
    <ChatEdit ref="ChatEdit" @OK="getHistoryList(1)" />
  </div>
</template>

<script>
// import Main from './index.vue'
import DefaultPage from './experience-index.vue'
import Modelselect from './select-model-dialog.vue'
import ModelInfo from './info-model-dialog.vue'
import ChatHistoryList from './chat-history-list.vue'
import Chat from './chat-index.vue'
import ChatEdit from './edit-chat.vue'

import * as Chat_http from '@/http/chat-http/chat-base-http.js'

export default {
  name:'ModelExperience',
  components: { DefaultPage, Modelselect, ModelInfo, ChatHistoryList, Chat, ChatEdit },
  data() {
    return {
      loading: false,
      isDefault: null,        // 展示：默认页 -true / 对话页 - false
      modelObj: {},           // 选中的模型
      historyList: [],        // 历史对话
      currentId: '',          // 选中的对话
      canAdd: false,
      QA_List: [],
      chatLoading: false,
      modelInfos: [],
      isShowErrorTips: false,     // 模型不可用提示
      showText: ''
    }
  },
  watch: {
    '$route.name'(val) {       // 切换路由重新获取数据
      this.currentId = ''
      this.init()
    }
  },
  methods: {
    init() {
      this.loading = true
      if(this.$route.query && !!this.$route.query.name) {      // 有query参数，说明是从模型广场页面来的，直接展示会话页面
        // this.isDefault = false
        this.modelObj = this.$route.query;
        this.currentId = ''
        this.getHistoryList(1)
        this.getModelInfo()     // 获取模型配置信息
      } else {
        this.getHistoryList()
      }
    },
    getHistoryList(val) {
      if(!val) {
        this.loading = true
      }
      
      // 获取历史记录列表数据，有历史会话展示会话页面，没有
      Chat_http.getChatList({
        type: true,
        project_id: localStorage.getItem("ProjectId") || '',
        model_type: this.$route.name === 'viewModelExperience' ? 'IU' : 'TG'
      }).then(data => {  
        this.$handle_http_back(data, true, false).then(res => {
          if(res.content && res.content.sessions.length) {
            this.loading = false
            this.isDefault = false
            this.historyList = res.content.sessions || []
            if((val !== 1) && !this.currentId) {
              this.currentId = res.content.sessions[0].id
            }

            if(!val) {
              let _a = this.historyList.filter(e => e.id === this.currentId)
              if(_a.length) {
                this.modelObj = {
                  name: _a[0].model_name,
                  model_id: _a[0].model_id
                }
              } else {
                this.currentId = res.content.sessions[0].id
                this.modelObj = {
                  name: res.content.sessions[0].model_name,
                  model_id: res.content.sessions[0].model_id
                }
              }
              
              // 不是新建会话才获取会话详情
              this.getChatCurrentContent(this.currentId)
            }
          } else {
            this.historyList = []
            if(!val) {
              this.isDefault = true       // 展示默认页面到时候不取消loading
              // this.loading = false
              // this.$forceUpdate()
            } else {
              this.isDefault = false
              this.loading = false
            }
          }
        }).catch(_ => {
          this.historyList = []
          this.currentId = ''
        })
      }).catch(_ => {
        this.historyList = []
        this.currentId = ''
      })
    },
    handleOpenModel(data) {
      this.$refs.Modelselect.open(data)
    },
    handleOpenInfo() {
      this.$refs.ModelInfo.open(this.modelInfos)
    },
    handleShowChat(data, data1, data2) {
      this.isDefault = false
      this.modelObj = data
      this.modelInfos = data1
      this.isShowErrorTips = false
      this.showText = ''

      if(!!data2) {
        this.getHistoryList()
      } else {
        this.QA_List = []
        this.currentId = ''
      }
    },

    setAdd(val) {
      this.canAdd = val
    },

    changeCurrent (data) {        // 切换历史对话
      this.currentId = data.id
      this.isShowErrorTips = false
      this.showText = ''
      this.getChatCurrentContent(data.id)
      this.modelObj = {model_id: data.model_id, name: data.model_name}
    },

    getChatCurrentContent(id) {
      this.chatLoading = true;
      Chat_http.getChatDetail({ session_id: id, project_id: localStorage.getItem("ProjectId") || '' }).then(data => {
        this.chatLoading = false
        this.$handle_http_back(data, true, false).then(res => {
          let _rel = res.content && res.content.messages ? (res.content.messages || []) : [];
          
          if(res.content && !!res.content.model_id) {      // 获取模型详情，获取配置信息
            this.getModelInfo(res.content.model_params_setting)
          }
          
          let _tempArr = [];
          _rel.forEach((el, index) => {
            if (el.message_type === 'user') {
              let _objUser = {
                type: 'user',
                message_id: el.message_id
              }

              if(typeof el.message === 'object' && el.message.length) {
                el.message.forEach(m => {
                  if(m.type === 'image') {
                    _objUser.key = m.content
                    let _tempIndex = _tempArr.length
                    this.getImgUrl(_tempIndex, m.content)
                  } else {
                    _objUser.question = m.content
                  }
                })
              }

              _tempArr.push(_objUser)
            } else if (el.message_type === 'clear') {
              _tempArr.push({
                type: 'clear',
                message: el.message && el.message.length ? el.message[0].content : '',
              });
            } else if (el.message_type === 'assistant') {
              let deepthinkText = '';
              let answer0 = el.message && el.message.length ? el.message[0].content : '';
              let answer = ''
              if (answer0.indexOf('<think>') === 0 && answer0.indexOf('</think>') > -1) {
                let _aa = answer0.split('</think>');
                answer = _aa[1];
                deepthinkText = _aa[0].replace('<think>', '');
              } else if (answer0.indexOf('<think>') === 0 && answer0.indexOf('</think>') < 0) {
                deepthinkText = answer0.replace('<think>', '');
                answer = '';
              } else {
                deepthinkText = '';
                answer = answer0;
              }
              _tempArr.push({
                type: 'ai',
                answer: answer,
                copied: false,
                like: false,
                unlike: false,
                source_documents: el.source_documents,
                showTools: true,
                message_id: el.message_id,
                parent_message: el.parent_message,      // 重新生成时获取问题信息
                deepthinkShow: false,
                deepthinkText: deepthinkText,
                isDeep: false,
                showDetail: false,
              });
            }
          });
          this.QA_List = _tempArr;

          setTimeout(_ => {
            this.$nextTick(_ => {
              console.log(this.$refs.chatList, 'this.$refs.chatList')
              if(this.$refs.chatList) {
                this.$refs.chatList.scrollBottom()
              }
            })
            
          })
        }).catch(_ => {
          this.QA_List = []
        })
      }).catch(_ => {
        this.chatLoading = false
      })
    },
    handleMessageId(id, list) {
      let obj = {
        question: '',
        image_url: ''
      }
      list.forEach(e => {
        if(e.message_id === id) {
          obj.question = e.message
          obj.image_url = e.key
        }
      })
      return obj
    },

    deleteChat(item) {
      // this.$refs.ChatDel.open(data)
      let _this = this
      this.$clAlert({
        title: '删除对话',
        pre_message:`确定删除`,
        message: item.name,
        after_message:`吗？`,
        type: 'alert',
        confirmText:'确定',
        loadingText:'确定中...',
        render() {
          return <div><div style="font-weight: 700;  margin-bottom: 10px; font-size: 16px;"><i class="el-icon-warning" style="color: orange; margin-right: 8px;"></i>确定删除对话？</div>删除后，该对话内的所有记录都将被删除。</div>
        },
        ok: function (handleClose,closeConfirmLoading) {
          Chat_http.deleteChat({chat_session_id: item.id, project_id: ''}).then(data => {
            closeConfirmLoading()
            _this.$handle_http_back(data, false, false).then(res => {
              _this.currentId = ''
              _this.QA_List = []
              _this.getHistoryList()
            })
            handleClose()
          }).catch(_ => {
            closeConfirmLoading()
          })
        },
        cancel: function () {
        }
    })
    },
    editChat(data) {
      this.$refs.ChatEdit.open(data)
    },
    handleClearChat() {
      if(!this.QA_List.length) return
      // 如果问答最后一条信息为已清空上下文，则不再调接口
      if(this.QA_List[this.QA_List.length - 1].type === 'clear') return
      Chat_http.clearChat(this.currentId).then(data => {
        this.$handle_http_back(data, false, false).then(res => {
          this.QA_List = []
          this.getChatCurrentContent(this.currentId)
        })
      })
    },
    setLoading(val) {
      this.loading = val
    },
    handleSetModelInfo(data) {
      this.modelInfos = data
    },
    getImgUrl(index, content) {
      Chat_http.getImageUrl({key: content}).then(data => {
        this.$handle_http_back(data, true, false).then(res => {
          this.$nextTick(_ => {
            this.$set(this.QA_List[index], 'imgUrl', res.content.url)
          })
        })
      })
    },
    getModelInfo(valueObj) {
      Chat_http.getModelInfos(this.modelObj.model_id).then(res => {
        // this.$handle_http_back(data, true, false).then(res => {
          if(!res.content) {      // 说明模型已不可用，显示提示框
            this.modelInfos = []
            this.isShowErrorTips = true
            this.showText = '该模型服务已下线，无法继续体验，请切换模型服务后进行体验。'
          } else if (res.content.status !== '运行中') {
            this.modelInfos = []
            this.isShowErrorTips = true
            this.showText = '该模型服务状态异常，暂无法继续体验，请等待该服务状态正常或切换模型服务后进行体验。'
          } else {
            this.isShowErrorTips = false
            if(res.content.model && res.content.model.chatConfig) {
              let _a = JSON.parse(res.content.model.chatConfig)
              if(valueObj) {
                for(let key in valueObj) {
                  _a.forEach(p => {
                    if(p.name === key) {
                      p.recommendValue = valueObj[key]
                    }
                  })
                }
              }

              this.modelInfos = _a
            } else {
              // 不展示模型配置按钮
              this.modelInfos = []
            }
            this.showText = ''
          }
         })
      // })
    }
  },
  created() {
    this.init()
  },
  mounted() {
    this.$nextTick(() => {
      document.getElementsByClassName('bread-container')[0].style.marginBottom = '10px'
    })
  },
}
</script>

<style lang="scss" scoped>
.model-experience-content {
  height: calc(100vh - 60px);
  width: 100%;
  background: #f5f6f9;
}
.model-experience-top {
  width: 100%;
  // height: 44px;
  margin-bottom: 10px;
  background: #fff;
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;

  .title-left {
    font-size: 18px;
    line-height: 34px;
    font-weight: 600;
  }

  // .model-select {
  //   width: 250px;
  //   margin-right: 16px;
  //   display: flex;
  //   justify-content: space-between;
  //   cursor: pointer;
  //   border: 1px solid var(--color-theme-header-search-border);
  //   border-radius: 2px;
  //   padding: 5px 10px;
  //   line-height: 24px;

  //   .model-select-text {
  //     width: calc(100% - 15px);
  //     overflow: hidden;
  //     text-overflow: ellipsis;
  //     white-space: nowrap;
  //   }
  // }
}

.model-experience-main{
  width: 100%;
  height: calc(100% - 54px);
  background: #fff;
}
</style>