<template>
  <div class="chat-content" v-loading="loading">
    <div style="display: flex; justify-content: right;margin: 8px 20px;">
      <div class="model-select">
        <div class="model-select-text">{{ modelObj.name }}</div>
        <div @click="handleOpenModel({ ...modelObj, chat_session_id: currentId })" :style="{cursor: showLoading ? 'not-allowed' : 'pointer'}"><i class="el-icon-sort" style="transform: rotate(90deg);"></i></div>
        <div @click="handleOpenInfo" v-show="modelInfos.length" :style="{cursor: showLoading ? 'not-allowed' : 'pointer'}"><i class="el-icon-s-operation"></i></div>
      </div>
      <cl-button @click="handleClearChat" :disabled="isShowErrorTips">
        <i class="iconfont icon-geshi" style="margin-right: 8px;"></i>清除上下文
      </cl-button>

    </div>
    <div v-if="!QA_List.length && !loading">
      <default-chat :show-loading="showLoading" :modelObj="modelObj" :isShowErrorTips="isShowErrorTips" @send="send" @handleChangeNeedWbSearch="handleChangeNeedWbSearch" />
    </div>
    <div v-else>
      <chat-list
        ref="childRef"
        :QA_List="QA_List"
        :show-loading="showLoading"
        :need-web-search="need_wb_sarch"
        :isShowErrorTips="isShowErrorTips"
        :showText="showText"
        :currentId="currentId"
        @stopChat="stopChat"
        @reAnswer="reAnswer"
        @setScroll="setScroll"
      />
      <div class="chat-bottom">
        <send-box
          v-if="$route.name === 'modelExperience'"
          :show-loading="showLoading"
          :isShowErrorTips="isShowErrorTips"
          style="width: 752px; text-algin: center; margin-left: 50px"
          @send="send"
          @handleChangeNeedWbSearch="handleChangeNeedWbSearch"
        />
        <view-send-box
          v-else
          :show-loading="showLoading"
          :model_id="modelObj.model_id"
          :isShowErrorTips="isShowErrorTips"
          style="width: 752px; text-algin: center; margin-left: 50px"
          @send="send"
          @handleChangeNeedWbSearch="handleChangeNeedWbSearch"
        />
      </div>
    </div>
  </div>
</template>

<script>
import DefaultChat from './default-chat.vue'
import ChatList from './chat-list.vue'
import SendBox from './send-box.vue'
import viewSendBox from './view-send-box.vue'

import { Typewriter } from '@/utils/typewriter';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import {static_name} from "@/utils"

export default {
  components: { DefaultChat, ChatList, SendBox, viewSendBox },
  props: {
    modelObj: {
      type: Object,
      default: () => {}
    },
    modelInfos: {
      type: Array,
      default: () => []
    },
    QA_List: {
      type: Array,
      default: () => []
    },
    currentId: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    isShowErrorTips: {
      type: Boolean,
      default: false
    },
    showText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showLoading: false,
      need_wb_sarch: false,
      isSend: false,
      question:'',
      history: [],
      ctrl: null,
      typewriter: null,
      typewriterDeep: null,
      isScroll: false,       // true - 不需要滚动到底部，false - 需要滚动到底部
      isShowErrorTips1: true,
    }
  },
  methods: {
    handleClearChat() {
      this.$emit('handleClearChat')
    },
    handleOpenModel(data) {
      if(this.showLoading) return
      this.$emit('handleOpenModel', data)
    },
    handleOpenInfo() {
      if(this.showLoading) return
      this.$emit('handleOpenInfo')
    },
    setScroll(val) {            // 控制是否滚动到底部
      this.isScroll = val;
    },
    handleChangeNeedWbSearch(val) {
      this.need_wb_sarch = val
    },
    stopChat() {
      if(this.ctrl) {
        this.ctrl.abort();
      }
      this.typewriter.done();
      this.typewriterDeep.done();

      this.showLoading = false;
      this.$emit('setAdd', false)
      this.QA_List[this.QA_List.length - 1].showTools = true;
    },
    reAnswer(data) {
      // this.question = data.question
      this.send(data)
    },

    //发送问答消息
    send(params) {
      this.question = params && params.question ? params.question : this.question;
      this.typewriter = new Typewriter((str) => {
        if (str) {
          this.QA_List[this.QA_List.length - 1].answer += str || '';
        }
      });
      this.typewriterDeep = new Typewriter((str) => {
        if (str) {
          this.QA_List[this.QA_List.length - 1].deepthinkText += str || '';
        }
      });
      // console.log('发送问答消息', this.question);
      if (!this.question.length) {
          return;
      }
      if (!this.modelObj.model_id) {
        this.$message.error('请选择模型');
        return
      }

      // 没有选中会话，说明是新建
      // if (!selectList.value.length) {
      //   return message.warning(common.chooseChatError);
      // }

      // 获取上一个回答的message_id；
      const parent_message_id = this.getParentMessageId() ? this.getParentMessageId() : null;

      // const q = this.question;
      let q = '';
      if (this.question.indexOf('\n') !== -1) {
          q = this.question.replace(/\n/g, '\n\n');
      } else {
          q = this.question;
      }
      this.question = '';

      this.addQuestion({question: q, key: params.key || '', imgUrl: params.imgUrl || ''});
      // if (this.history.length >= 3) {
      //     this.history = [];
      // }
      this.showLoading = true;
      this.$emit('setAdd', true)
      this.isScroll = false
      this.ctrl = new AbortController();
      // const Authorization = headerConfig.Authorization;
      let api_header = localStorage.getItem('api_header')?JSON.parse(localStorage.getItem('api_header')):{}
      const Authorization = api_header.Authorization
      let model_params_setting = {}

      this.modelInfos.forEach(p => {
        model_params_setting[p.name] = p.recommendValue
      })

      this.isSend = true; // 发送消息中
      let _params = {
        user_id: GetUserInfo("userId"),
        // history: this.control ? this.history : [],
        history: [],
        question: q,
        streaming: true,
        chat_session_id: this.currentId,
        parent_message_id: parent_message_id,
        rerank: false,
        need_wb_sarch: this.need_wb_sarch,
        model_id: this.modelObj.model_id,
        project_id: this.$get_projectId(),       // 两种情况：project_id在新建时，没选中项目传admin-inner-project，在查询时没选中项目传空
        model_type: this.$route.name === 'viewModelExperience' ? 'IU' : 'TG',
        model_params_setting
      };
      if(this.$route.name === 'viewModelExperience') {
        _params.image_url = params.key || ''
      }


      const currentId =this.currentId
      this.addAnswer(q);
      let _this = this
      fetchEventSource(`/${static_name.chat_path}/local_doc_qa/local_chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: ['text/event-stream', 'application/json'],
          Authorization,
        },
        openWhenHidden: true,
        body: JSON.stringify(_params),
        signal: this.ctrl.signal,
        onopen(e) {
          console.log('open');
          if (e.ok && e.headers.get('content-type') === 'text/event-stream') {
            // 没有选中的会话，说明是第一次
            if (!currentId) {   // 获取历史会话列表
              _this.$emit('OK', 'send')
            }
            console.log("everything's good");
            // _this.addAnswer(q);
             _this.QA_List[_this.QA_List.length - 1].loading = false;
            _this.typewriter.start();
            _this.typewriterDeep.start();
          } else if (e.headers.get('content-type') === 'application/json') {
            _this.showLoading = false;
            _this.$emit('setAdd', false)
            return e.json().then(data => {
              message.error(data ? data.msg : '出错了,请稍后刷新重试。');
            }).catch(e => {
              // console.log(e);
              message.error('出错了,请稍后刷新重试。');
            }); // 将响应解析为 JSON
          }
        },
        onmessage(msg) {
          if (_this.isSend === false) {
              // 切换对话时，中断当前对话
              _this.showLoading = false;
              _this.$emit('setAdd', false)
              _this.ctrl.abort();
              return;
          }
          const res = JSON.parse(msg.data);
          _this.$handle_http_back(res, true, false).then(data => {
            if(data.response) {
              if (data.response === '<think>') {
                  _this.QA_List[_this.QA_List.length - 1].isDeep = true;
                  _this.QA_List[_this.QA_List.length - 1].deepthinkShow = true;
              } else if (data.response === '</think>') {
                  _this.QA_List[_this.QA_List.length - 1].isDeep = false;
              } else {
                  if (_this.QA_List[_this.QA_List.length - 1].isDeep === true) {
                      // this.QA_List[this.QA_List.length - 1].deepthinkText += res?.response || '';
                      _this.typewriterDeep.add(data.response);
                  } else {
                      _this.typewriter.add(data.response);
                  }
              }
              // this.QA_List[this.QA_List.length - 1].answer += res.result.response;
              // this.typewriter.add(res?.response.replaceAll('\n', '<br/>'));
              if(_this.isScroll === false) {
                _this.scrollBottom();
              }
            }
          })
          // if (res.code == 200 && res.response) {
              
          // }

          if (res.message_id && !_this.QA_List[_this.QA_List.length - 1].message_id) {
            // parent_message_id = res?.message_id;
            _this.QA_List[_this.QA_List.length - 1].message_id = res.message_id || '';
          }

          if (res.source_documents.length) {
            _this.QA_List[_this.QA_List.length - 1].source_documents = res.source_documents;
          }

          // if (res.history.length) {
          //   _this.history = res.history;
          // }
        },
        onclose(e) {
          console.log('close');
          _this.typewriter.done();
          _this.typewriterDeep.done();
          _this.ctrl.abort();
          _this.showLoading = false;
          _this.$emit('setAdd', false)
          _this.QA_List[_this.QA_List.length - 1].showTools = true;
          _this.QA_List[_this.QA_List.length - 1].loading = false;
          _this.$nextTick(() => {
            if(_this.isScroll === false) {
              _this.scrollBottom();
            }
          });
        },
        onerror(err) {
          console.log(err,'error');
          _this.typewriter.done();
          _this.typewriterDeep.done();
          _this.ctrl.abort();
          _this.showLoading = false;
          _this.$emit('setAdd', false)
          _this.QA_List[_this.QA_List.length - 1].showTools = true;
          _this.QA_List[_this.QA_List.length - 1].loading = false;
          // message.error(err.msg || '出错了');
          _this.$nextTick(() => {
            if(_this.isScroll === false) {
              _this.scrollBottom();
            }
          });
          throw err;
        },
      });
    },
    getParentMessageId() {
      let _rel = '';
      const a = this.QA_List.filter(el => el.type === 'ai' && el.message_id);
      if (a.length) {
        let _obj = a[a.length - 1];
        if (_obj.message_id + '') {
          _rel = _obj.message_id;
        }
      }
      return _rel;
    },
    scrollBottom(){
      this.$nextTick(()=>{
        this.$refs.childRef.scrollBottom();
      })
    },
    addQuestion(data) {
      this.QA_List.push({ ...data, type: 'user' });
      
      //need:滚动底部，请从原来的代码拷贝
      this.scrollBottom();
    },
    addAnswer(question){
      this.QA_List.push({
        answer: '',
        question,
        type: 'ai',
        copied: false,
        like: false,
        unlike: false,
        source_documents: [],
        showTools: false,
        message_id: '',
        deepthinkShow: false,
        deepthinkText: '',
        isDeep: false,
        showDetail: false,
        loading: true
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-content {
  display: inline-block;
  width: calc(100% - 244px);

}
.chat-bottom {
  width: 100%;
  padding-bottom: 10px;
  display: flex;
  justify-content: center;
}

.model-select {
  width: 250px;
  margin-right: 16px;
  display: flex;
  justify-content: space-between;
  // cursor: pointer;
  border: 1px solid var(--color-theme-header-search-border);
  border-radius: 2px;
  padding: 3px 10px;
  line-height: 24px;

  .model-select-text {
    width: calc(100% - 36px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>