<template>
  <div style="margin-top: 10px;">
    <div style="text-align: left;">
      <el-upload
        class="upload-demo"
        :action="action"
        accept="image/*"
        :headers="{Authorization}"
        :data="queryData"
        :limit="1"
        :on-error="handleError"
        :on-success="handleSuccess"
        :before-upload="handleBeforeUpload"
        :file-list="fileList"
        :show-file-list="false">
        <cl-button icon="el-icon-picture-outline" title="上传图片"></cl-button>
      </el-upload>
    </div>
    <div class="default-chat-main-style cont-style">
      <div class="img-style" v-loading="loading" v-show="loading || imgUrl" @mouseenter="imgDialog = true" @mouseleave="imgDialog = false">
        <img :src="imgUrl" alt="上传图片" style="max-width: 100%; max-height: 100%;" />
        <div class="upload-img-dialog" v-show="imgDialog">
          <i class="el-icon-delete delete-style" @click="imgUrl = ''"></i>
        </div>
      </div>
      <div style="width: 100%;">
        <el-input
          type="textarea"
          v-model="question"  
          max-length="200"
          size="small"
          placeholder="请输入问题，我可以完成智能解答..."
          :auto-size="{ minRows: 4, maxRows: 4 }"
          class="textarea"
          resize="none"
          @keydown.native="handleKeyDown"
        >
        </el-input>
        <div style="display: flex; justify-content: right; margin-top: 10px">
          <cl-button type="primary" :disabled="showLoading || isShowErrorTips" @click="send">
            <i class="iconfont icon-yingyongrukou"></i>
            发送(Enter)
          </cl-button>
        </div>
      </div>
    </div>
  </div>
  
</template>

<script>
import {static_name} from "@/utils"

export default {
  props: {
    showLoading: {
      type: Boolean,
      default: false
    },
    model_id: {
      type: String,
      default: ''
    },
    isShowErrorTips: {
      type: Boolean,
      default: false,
    }
  },
  computed: {
    Authorization() {
      let api_header = localStorage.getItem('api_header')?JSON.parse(localStorage.getItem('api_header')):{}
      return api_header.Authorization
    }
  },
  data() {
    return {
      need_wb_sarch: false,
      question: '',
      loading: false,
      imgUrl: '',
      fileList: [],
      queryData: { key: '' },
      action: `/${static_name.chat_path}/local_chat/upload`,
      imgDialog: false
    }
  },
  methods: {
    send() {
      if(!this.question) {
        this.$message.error('请输入问题')
        return
      }
      this.$emit('send', {question: this.question, imgUrl: this.imgUrl, key: this.queryData.key})
      this.question = '' // 清空输入框
      this.imgUrl = ''
    },
    handleChangeNeedWbSearch() {
      this.need_wb_sarch = !this.need_wb_sarch;
      this.$emit('handleChangeNeedWbSearch', this.need_wb_sarch);
    },
    handleKeyDown(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        // 阻止默认行为，防止换行符被插入到文本框中
        e.preventDefault();
        // send(); // 发送消息
        console.log('this.showLoading')
        if (this.showLoading === false) {
          if(!this.question) {
            this.$message.error('请输入问题')
            return
          }
          this.$emit('send', {question: this.question, imgUrl: this.imgUrl, key: this.queryData.key});
          this.question = ''; // 清空输入框
          this.imgUrl = ''
        }
      }
    },
    handleBeforeUpload(e) {
      let time = this.getCurrentTime()
      let project_id = this.$get_projectId()
      this.queryData.key = `image-understand/${project_id}/${this.model_id}/${time}/${e.name}`
      this.loading = true
    },
    handleSuccess(data) {           // 上传成功时
      this.loading = false
      this.fileList = []
      this.$handle_http_back(data, true, false).then(res => {
        this.imgUrl = res.content.url
      })
    },
    handleError() {
      this.loading = false
    },
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，所以要加 1
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      return `${year}${month}${day}${hours}${minutes}${seconds}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.default-chat-main-style {
  padding: 10px;
  // border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: rgba(50, 53, 60, 0.08);
  margin-top: 10px;
  border: 1px solid transparent;

  &:hover {
    // box-shadow: 0 4px 8px rgba(5, 145, 255, 0.1);
    border: 1px solid rgba(41, 44, 51, 0.10);
  }
  &:focus {
    border: 1px solid rgba(41, 44, 51, 0.10);
  }

  .download {
    cursor: pointer;
    padding: 3px 13px;
    margin-right: 10px;
    border-radius: 4px;
    background: var(--color-theme-router-background);
    color: var(--color-theme-text);

    svg {
      width: 16px;
      height: 16px;
    }
    &.control-true {
      border: 1px solid var(--color-theme);
      color: var(--color-theme);
    }
    &.control-false {
      border: 1px solid #e5e5e5;
      color: #666666;
    }
  }
  .active {
    background: var(--color-menu-active);
    color: var(--color-theme);
  }

  .send-plane {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    padding: 2px;
    vertical-align: middle;
  }

  /deep/ .el-textarea__inner {
    background: transparent !important;
    border: none !important;
  }
}

.cont-style {
  display: flex;
  justify-content: space-between;
}

.img-style {
  width: 70px;
  height: 100px;
  overflow: hidden;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-img-dialog {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba($color: #000000, $alpha: 0.7);
  overflow: hidden;

  .delete-style {
    display: inline-block;
    cursor: pointer;
    width: 16px;
    height: 16px;
    margin: 42px 0 0 27px;
    color: #fff;
  }
}
</style>
