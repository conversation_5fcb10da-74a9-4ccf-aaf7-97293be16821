<template>
  <div class="my-page">
    <div
      id="chat"
      class="chat"
      :style="chatUlHeight"
    >
      <ul id="chat-ul" ref="scrollDom">
        <li v-for="(item, index) in QA_List" :key="index">
          <div v-if="item.type === 'user'" class="user">
            <img class="avatar" src="@/assets/home/<USER>" alt="头像" />
            <div class="question-text">
              <Markdown :source="item.question" />
              <div v-show="item.key && !item.imgUrl" v-loading="true">
                <img src="@/assets/home/<USER>" style=" height: 200px;" />
              </div>
              <img :src="item.imgUrl" v-if="item.imgUrl" :key="item.imgUrl" class="fada-style" @click="hancleShowBigImg(item.imgUrl)" />
            </div>
          </div>
          <div v-else-if="item.type === 'clear'" style="width: calc(100% - 50px); margin-left: 50px;">
            <p class="clear-style">{{item.message}}</p>
          </div>
          <div v-else class="ai">
            <div class="content">
              <img class="avatar" src="@/assets/home/<USER>" alt="头像" />
              <div v-if="item.loading"><i class="el-icon-loading"></i></div>
              <div class="content1" v-if="!item.loading">
                <div class="deepthink-style" v-if="!!item.deepthinkText">
                  <div style="display: flex;">
                    <span
                      v-show="needWebSearch && !item.answer && index === QA_List.length - 1"
                      size="small"
                      class="spin-loading-style"
                    >
                      <i class="el-icon-loading"></i>
                    </span>
                    <div class="deepthink_btn" @click="item.deepthinkShow = !item.deepthinkShow">
                      <div style="margin-right: 16px;">
                        <!-- <LoadingOutlined
                          v-show="
                            props.needWebSearch && props.showLoading && index === QA_List.length - 1
                          "
                        /> -->
                        {{ item.isDeep ? '深度思考中' : '已深度思考' }}
                      </div>
                      <div>
                        <i :class="item.deepthinkShow ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
                      </div>
                    </div>
                  </div>
                  <div v-show="item.deepthinkShow" class="deepthink_content">
                    <Markdown :source="item.deepthinkText" />
                  </div>
                </div>
                <Markdown
                  v-if="item.answer"
                  :source="item.answer"
                  class="question-text markdown-content"
                  :class="[
                    !item.source_documents.length ? 'change-radius' : '',
                    item.showTools ? '' : 'flashing',
                  ]"
                />
              </div>
            </div>
            <template v-if="item.source_documents.length">
              <chat-normal-link
                :key="index + item.source_documents.length"
                :item="item"
                :index-item="index"
                @handleChangeLink="handleChangeLink"
              />
            </template>
            <div v-if="item.showTools" class="feed-back">
              <div class="reload-box" @click="reAnswer(item, index)">
                <i class="el-icon-refresh"></i>
                <span class="reload-text">{{ common.regenerate }}</span>
              </div>
              <div class="tools">
                <div class="tools-svg">
                  <span
                    class="iconfont icon-fuzhi1"
                    :class="item.copied ? 'tools-active' : ''"
                    name="copy"
                    @click="myCopy(item)"
                  ></span>
                </div>
                <div class="tools-svg">
                  <span
                    class="iconfont icon-unlike-o like-icon"
                    :class="item.like ? 'tools-active' : ''"
                    name="like"
                    @click="like(item, $event)"
                  ></span>
                </div>
                <div class="tools-svg">
                  <span
                    class="iconfont icon-unlike-o"
                    :class="item.unlike ? 'tools-active' : ''"
                    name="unlike"
                    @click="unlike(item)"
                  ></span>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div class="stop-btn">
      <cl-button v-show="showLoading" style="width: 114px" danger @click="stopChat">
        <i class="el-icon-video-pause"></i>
        {{ common.stop }}
      </cl-button>
    </div>
    <div class="alert-style" style="width: 600px; margin: 0 auto;" v-show="isShowErrorTips && isShowErrorTips1">
      <div>
        <img src="@/assets/home/<USER>" style="width: 16px; height: 16px;" />
        <span style="margin-left: 8px; vertical-align: middle;">{{showText}}</span>
      </div>
      <div style="cursor: pointer;" @click="isShowErrorTips1 = false">
        <i class="el-icon-close" style="font-size: 16px; vertical-align: middle;"></i>
      </div>
    </div>
    <div v-if="isVisible" class="modal-mask" @click.self="isVisible = false">
      <div class="modal-container">
        <!-- 大图 -->
        <img 
          :src="largeImage" 
          class="large-image"
          @click="isVisible = false"
        >
      </div>
    </div>
  </div>
</template>
  
<script>
import { getLanguage } from '@/language/index';
import Markdown from '@/components/markdown/index.vue';
import 'highlight.js/styles/default.css';
import {throttle,copy} from '@/utils/index.js'

import ChatNormalLink from './ChatNormalLink.vue';
export default{
  components:{
    Markdown,
    ChatNormalLink
  },
  props:{
    QA_List:{
        type:Array,
        default:()=>[],
    },
    needWebSearch:{
        type:Boolean,
        default:false,
    },
    showLoading:{
      type:Boolean,
      default:false
    },
    isShowErrorTips: {
      type: Boolean,
      default: false
    },
    showText: {
      type: String,
      default: ''
    },
    currentId: {
      type: String,
      default: ''
    }
  },
  computed: {
    chatUlHeight() {
      let _h = 'calc(100vh - 337px)'
      if(this.$route.name=== 'viewModelExperience') {     // 视图模型
        _h = this.isShowErrorTips && this.isShowErrorTips1 ? 'calc(100vh - 430px)' : 'calc(100vh - 387px)'
      } else {  // 文本模型
        _h = this.isShowErrorTips && this.isShowErrorTips1 ? 'calc(100vh - 380px)' : 'calc(100vh - 337px)'
      }
      return { height: _h }
    }
  },
  data(){
      return{
        common:getLanguage().common,
        isShowErrorTips1: true,
        isVisible: false,
        largeImage: ''
      }
  },
  watch: {
    currentId(cal) {
      this.isShowErrorTips1 = true
    }
  },
  methods:{
    hancleShowBigImg(url) {
      this.largeImage = url
      this.isVisible = true
    },
    myCopy(text) {
      copy(this,text)
    },
    like:throttle((item, e) => {
        item.like = !item.like;
        item.unlike = false;
        // _czc.push(['_trackEvent', 'qanything', '问答页面', '点赞', '', '']);
        if (item.like) {
          e.target.parentNode.style.animation = 'shake ease-in .5s';
          const timer = setTimeout(() => {
            clearTimeout(timer);
            e.target.parentNode.style.animation = '';
          }, 600);
        }
    },800),
    unlike(item){
      item.unlike = !item.unlike;
      item.like = false;
    },
    scrollBottom(){
      this.$nextTick(() => {
        this.$refs.scrollDom.scrollIntoView(false);
      });
    },
    handleChangeLink($index) {
      this.QA_List[$index]['showDetail'] = !this.QA_List[$index]['showDetail'];
    },
    stopChat() {
      this.$emit('stopChat')
    },
    reAnswer(obj) {
      let _a = { question: '', key: '', imgUrl: ''}
      let _v = this.QA_List.filter(e => e.type === 'user' && e.message_id === obj.parent_message)
      if(_v.length) {
        _a.question = _v[0].question
        _a.key = _v[0].key || ''
        _a.imgUrl = _v[0].imgUrl || ''
      }

      this.$emit('reAnswer', _a)
    },
  },
  mounted() {
    let _dom = this.$refs.scrollDom
    let _this = this

    _dom.addEventListener('wheel', function() {
      _this.$emit('setScroll', true)
    })
  },
  beforeDestroy() {
    let _dom = this.$refs.scrollDom
    // _dom.removeEventListener('scroll', function() {})
    _dom.removeEventListener('wheel', function() {})
  }
}
</script>
  
<style lang="scss" scoped>
  .like-icon{
    transform: rotateX(-180deg);
    display: inline-block;
  }
  .my-page {
    position: relative;
    margin: 0 auto;
  }
  
  .chat {
    margin: 0 auto;
    width: 800px;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox 不展示滚动条 */
    // padding-right: 10px;
    .avatar {
      width: 40px;
      height:40px;
      margin-right: 10px;
    }
  
    .user {
      display: flex;
      margin-bottom: 24px;
      // justify-content: right;
  
      .question-text {
        padding: 10px;
        // color: #26293b;
        // background: #ECF7FF;
        background: rgba(50, 53, 60, 0.08);
        border-radius: 0px 8px 8px 8px;
        word-wrap: break-word;
        max-width: calc(100% - 50px);
        // border-radius: 8px;
      }
    }
  
    .ai {
      margin-bottom: 24px;
      .content {
        display: flex;

        .content1 {
          width: calc(100% - 50px);
        }

        .deepthink-style{
          background: rgba(50, 53, 60, 0.04);
          padding: 0 16px;
          border-radius: 4px 4px 0px 0px;
          margin-bottom: 10px;
        }
  
        .question-text {
          flex: 1;
          // padding: 13px 20px;
          font-size: 14px;
          font-weight: normal;
          // line-height: 22px;
          color: #222222;
          background:#fff;
          // border-radius: 0px 0px 4px 4px;
          word-wrap: break-word;
          width: 100%;
        }
      }
  
      .feed-back {
        display: flex;
        height: 20px;
        margin-top: 10px;
        margin-left: 50px;
  
        .reload-box {
          display: flex;
          cursor: pointer;
          align-items: center;
          margin-right: auto;
          color: var(--color-theme);
  
          .reload-text {
            height: 22px;
            line-height: 22px;
            margin-left: 8px;
          }
        }
  
        .tools {
          display: flex;
          align-items: center;
  
          .tools-svg {
            padding: 0 4px;
            margin-left: 6px;
            width: 23px;
            height: 23px;
            border-radius: 4px;
  
            svg {
              width: 16px;
              height: 16px;
              cursor: pointer;
            }
  
            &:hover {
              // color: $colorPrimary;
              background: rgba(50, 53, 60, 0.04)0;
            }

            .tools-active {
              color: var(--color-theme)
            }
          }
        }
      }
    }
  }
  .chat::-webkit-scrollbar {
    width: 0; /* 水平滚动条宽度 */
    height: 0; /* 垂直滚动条宽度 */
  }
  
  .stop-btn {
    height: 32px;
    width: 100%;
    text-align: center;
    // width: 114px;
    // position: absolute;
    // left: 50%;
    // bottom: 0;
  
    svg {
      width: 14px;
      height: 14px;
      margin-right: 8px;
    }
  
    .loading {
      animation: loading 3s infinite;
    }
  }  
  .deepthink_btn {
    // border: 1px solid rgba(41, 44, 51, 0.1);
    // background: rgba(50, 53, 60, 0.04);
    margin: 12px 0;
    // width: 167px;
    // padding: 5px 8px;
    cursor: pointer;
    border-radius: 4px;
    // justify-content: center;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: rgba(38, 39, 42, 1);
  }
  .deepthink_content {
    border-left: 1px solid rgba(41, 44, 51, 0.1);
    padding: 8px 10px 8px 16px;
    color: rgba(137, 139, 142, 1);
    font-size: 12px;
  }
  
  .spin-loading-style {
    display: inline-block;
    line-height: 44px;
    margin-right: 10px;
  }
  .clear-style{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px; /* 调整文字和虚线之间的间距 */
    text-align: center;
    color: #898B8E;
    margin: 58px 0;
  }
  .clear-style::before, .clear-style::after {
    content: '';
    flex: 1;
    border-bottom: 1px dashed rgba(41, 44, 51, 0.1); /* 虚线样式 */
    // margin: 0 10px; /* 调整虚线与文字的间距 */
  }

  .alert-style {
    background: rgba(255, 243, 239, 1);
    color: rgba(38, 39, 42, 1);
    padding: 9px 16px;
    display: flex;
    justify-content: space-between;
    line-height: 24px;
  }
  .fada-style{
    margin-top: 10px;
    max-height: 200px;
    max-width: 100%;
    cursor: zoom-in;
  }

  .modal-mask {
    position: fixed;
    z-index: 999;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: zoom-out;
  }

  .modal-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
  }

  .large-image {
    max-width: 100%;
    max-height: 80vh;
    // cursor: zoom-out;
    transform: scale(1);
    transition: 'transform 0.3s ease';
  }
  </style>
  