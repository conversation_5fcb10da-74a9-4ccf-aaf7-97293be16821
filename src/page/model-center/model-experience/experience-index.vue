<template>
  <div class="exper-main">
    <div class="exper-center">
      <div class="title-top">体验中心</div>
      <div class="title-center" v-if="cards.length">为您推荐以下热门模型</div>
      <div class="card-list" ref="cardList" v-if="cards.length">
        <div :class="selectModel.name === card.name ? 'card-box card-box-active' : 'card-box'" v-for="(card,index) of cards" :key="index" @click="selectModel = card">
            <div class="card-header">
                <img :src="handleImg(card.manufacturer)" alt="">
                <div class="card-title-content">
                    <div class="card-title">
                        <span class="text">{{card.name}}</span>
                    </div>
                    <p class="tag-box">
                      <span class="tag-text">{{transType(card.model_type)}}</span>
                      <span class="tag-text">{{ Math.floor(card.context_length / 1024) }}K</span>
                    </p>
                </div>
            </div>
            <div class="card-discripiton">{{ card.description }}</div>
            <div class="card-footer-msg">
              <span>{{ transType(card.manufacturer) }}</span>
              <span>{{ transTime(card.update_time) }}</span>
            </div>
        </div>
      </div>
      <div class="card-list" v-else>
        <div class="no-data">
          <img src="../../../assets/images/EmptyState.svg" />
          <div style="text-align: center; font-size: 16px; font-weight: 600;">暂无推荐模型服务</div>
        </div>
      </div>
      <div :class="selectModel && selectModel.name ? 'bottom-btn' : 'bottom-btn bottom-btn-dis'" @click="handleRouter">立即体验</div>
      <div class="link-style" @click="handleCheckModel">选择更多模型 ></div>
    </div>
    
  </div>
</template>

<script>
import { get_model } from "@/http/squrare-http";
import mixin from "../model-square/mixin.js";

export default {
  mixins: [mixin],
  data(){
      return {
        selectModel:{},
        cards: [],
        manufacturer: {}
      }
  },
  watch: {
    '$route.name'(val) {       // 切换路由重新获取数据
      this.getCardList()
    }
  },
  methods: {
    handleRouter(){
      if(!this.selectModel.name) {
        // this.$message.error('请选择模型')
        return
      }

      let _chat_config = this.selectModel.chat_config ? JSON.parse(this.selectModel.chat_config) : []
      this.$emit('handleShowChat',
        {name: this.selectModel.name, model_id: this.selectModel.deployment_id, deployment_id: ''},
        _chat_config
      )

    },
    getCardList() {
      get_model({
        page_num: 1,
        page_size: 10000000,
        name: '',
        // project_id: localStorage.ProjectId,
        // user_id: GetUserInfo("userId"),
        recommend: true,
        online: true,
        status: 1,    // model_enum: 1
        model_type: this.$route.name === 'viewModelExperience' ? 'IU' : 'TG'
      }).then(data => {
        this.$emit('setLoading', false)
        this.$handle_http_back(data, true, false).then(res => {
          this.cards = res.content.list || []
        }).catch(_ => {
          this.cards = []
        })
      }).catch(_ => {
        this.cards = []
        this.$emit('setLoading', false)
      })
    },
    handleCheckModel() {
      this.$emit('handleOpenModel')
    },
    handleImg(m) {
      return m ? this.transType(m, 'isImage') : ''
    }
  },
  created() {
    this.getCardList()
  }
}
</script>

<style lang="scss" scoped>
.exper-main {
  width: 100%;
  height: 100%;
  // background: model_experience_bg.svg;
  background: url('../../../assets/images/model_experience_bg.svg') no-repeat center / cover;
  position: relative;
}
.exper-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); /* 调整偏移量 */
  width: 75%;
}

.title-top {
  font-weight: 600;
  font-size: 32px;
  line-height: 44px;
  text-align: center;
}
.title-center {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  margin-top: 8px;
  margin-bottom: 40px;
  text-align: center;
}
.card-list{
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    // overflow: auto;
    gap: 24px;
    max-height: 370px;
    overflow-y: auto;
    padding-right: 10px;
    
    .card-box{
      flex: 1 1 1 31%; /* 每个子元素占据 30% 的宽度 */
      cursor: pointer;
      width: 31%; /* 每行 3 个，减去间距 */
      height: 170px;
      padding: 16px 16px 8px 16px;
      box-sizing: border-box;
      border-radius: 4px;
      overflow: hidden;
      background: white;
      border: 1px solid white;
      transition: all 0.2s;
      &:hover{
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
      }

      .card-footer-msg{
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #898B8E;
        margin-bottom: 14px;
      }
      .card-discripiton{
        width: 100%;
        height: 48px;
        overflow: hidden;
        font-size: 12px;
        line-height: 16px;
        margin-bottom: 16px;
        margin-top: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }
      .card-header{
          display: flex;
          align-items: center;
          img{
              height: 40px;
              margin-right: 16px;
          }
          .card-title-content{
              display: flex;
              flex: 1;
              flex-direction: column;
              .tag-box{
                  display: flex;
                  margin-top: 4px;
                  .tag-text{
                      height: 20px;
                      gap: 10px;
                      border-radius: 2px;
                      padding: 0 10px;
                      line-height: 20px;
                      background: #f7f7f7;
                      &+.tag-text{
                          margin-left: 4px;
                      }
                  }
              }
              .card-title{
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  width: 100%;
                  .text{
                      font-size: 13px;
                      color: #010205;
                      font-weight: 600;
                  }
              }
          }
      }
    }

    .card-box-active {
      border: 1px solid var(--color-theme);

    }
}
.bottom-btn{
  width: 264px;
  height: 48px;
  margin: 0 auto;
  line-height: 48px;
  text-align: center;
  border-radius: 8px;
  // background-image: linear-gradient(to bottom, #E8555A, #AF1F24);
  background: var(--color-theme);
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  margin-bottom: 16px;
  margin-top: 40px;
}
.bottom-btn-dis {
  background: #EAE7E7;
  color: rgba(40, 32, 33, 0.35);
  &:hover{
    cursor: not-allowed;
  }
}
.link-style {
  color: #1487FD;
  font-weight: 400px;
  cursor: pointer;
  text-align: center;
  line-height: 48px;
  &:hover{
      color: #075eb5;
  }
}
</style>
