<template>
  <div class="default-chat-content-style">
    <div class="default-chat-box-style">
      <div style="margin-bottom: 10px">
        <p class="tltle1">你好，我是{{modelObj.name}}</p>
        <!-- <p class="tltle1">你可以试着问我</p>
        <div class="btns">
          <div v-for="(item, index) in btnsList" :key="index" class="default-chat-btn">
            <i :class="item.icon" style="margin-right: 8px;"></i>
            {{ item.label }}
          </div>
        </div> -->
      </div>
      <send-box
        v-if="$route.name === 'modelExperience'"
        :show-loading="showLoading"
        :isShowErrorTips="isShowErrorTips"
        @send="send"
        @handleChangeNeedWbSearch="handleChangeNeedWbSearch"
      />
      <view-send-box
        v-else
        :show-loading="showLoading"
        :model_id="modelObj.model_id"
        :isShowErrorTips="isShowErrorTips"
        @send="send"
        @handleChangeNeedWbSearch="handleChangeNeedWbSearch"
      />
    </div>
  </div>
</template>

<script>
import SendBox from './send-box.vue';
import ViewSendBox from './view-send-box.vue';

export default {
  components: { SendBox, ViewSendBox },
  props: {
    modelObj: {
      type: Object,
      default: () => {},
    },
    showLoading: {
      type: Boolean,
      default: false,
    },
    isShowErrorTips: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      btnsList: [
        { label: '什么是一云多芯', icon: '' },
        { label: '云服务器怎样修改规格', icon: '' },
        { label: '什么是安全云服务器', icon: '' },
        // { label: '代码模式', icon: 'iconfont icon-jifeiguanli-xinjian' },
        // { label: '解题答疑', icon: 'iconfont icon-jifeiguanli-xinjian' },
        // { label: '工具助手', icon: 'iconfont icon-jifeiguanli-xinjian' },
        // { label: '帮我写作', icon: 'iconfont icon-jifeiguanli-xinjian' },
        // { label: 'PPT制作', icon: 'iconfont icon-jifeiguanli-xinjian' },
        // { label: '深度搜索', icon: 'iconfont icon-jifeiguanli-xinjian' },
      ],
    }
  },
  methods: {
    send(params) {
      this.$emit('send', params);
    },
    handleChangeNeedWbSearch(val) {
      this.$emit('handleChangeNeedWbSearch', val);
    }
  }
}
</script>

<style lang="scss" scoped>
.default-chat-content-style {
  position: relative; /* 定位方式 */
  height: calc(100vh - 80px); /* 容器高度设置为视口高度 */
  width: 100%;

  .default-chat-box-style {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* 调整偏移量 */
    width: 90%; /* 子元素宽度 */
    height: 270px; /* 子元素高度 */
    text-align: center; /* 文字居中 */

    p {
      font-size: 20px;
      font-weight: 600;
      line-height: 28px;
    }
    p:last-child {
      margin-top: 9px;
    }
  }  
}
.btns {
  margin: 24px 0 105px 0;

  .default-chat-btn {
    display: inline-block;
    border: 1px solid #626366;
    border-radius: 8px;
    margin-right: 16px;
    padding: 10px 21px;

  }
  .default-chat-btn:last-child {
    margin-right: 0;
  }
}
</style>
