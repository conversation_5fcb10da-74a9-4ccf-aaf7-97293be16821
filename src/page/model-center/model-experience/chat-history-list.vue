<template>
  <div class="history-content">
    <span style="font-weight: 600;">历史对话</span>
    <!-- <div
      :class="canAdd ? 'global-btn-primary global-btn-primary-dis' : 'global-btn-primary'"
      @click="createChat" style="width: 100%;"
    >
      <i class="el-icon-circle-plus-outline"></i>
      创建新对话
    </div> -->
    <cl-button style="width: 100%; margin: 10px 0; text-align: center;" :disabled="canAdd" @click="createChat">
      <i class="el-icon-circle-plus-outline"></i>
      创建新对话
    </cl-button>
    <div class="session-list">
      <div
        v-for="(item, index) in chatList" :key="index"
        :class="currentId === item.id ? 'chat-history-card active' : 'chat-history-card'" @click="handleCheckChat(item)">
        <div style="width: calc(100% - 16px);">
          <p class="title" :title="item.name"><span style="font-weight: 700; margin-right: 8px;">#</span><span class="title-text">{{item.name}}</span></p>
          <p class="time" :title="item.time_created">{{item.time_created}}</p>
        </div>
        <div style="line-height: 36px;">
          <el-popover
            placement="right"
            width="70"
            trigger="hover">
            <div>
              <div>
                <div>
                  <cl-button type="text" size="mini" @click="$emit('editChat', item)">编辑对话</cl-button>
                </div>
                <div>
                  <cl-button type="text" size="mini" @click="$emit('deleteChat', item)">删除对话</cl-button>
                </div>
              </div>
            </div>
            <div slot="reference">
              <i class="el-icon-more"></i>
            </div>
          </el-popover>
        </div>

        <!-- <el-popover
          placement="right"
          width="70"
          trigger="hover">
          <div>
            <div>
              <div>
                <cl-button type="text" size="mini" @click="$emit('editChat', item)">编辑对话</cl-button>
              </div>
              <div>
                <cl-button type="text" size="mini" @click="$emit('deleteChat', item)">删除对话</cl-button>
              </div>
            </div>
          </div>
          <div slot="reference">
            <p class="title" :title="item.name"><span style="font-weight: 700; margin-right: 8px;">#</span>{{item.name}}</p>
            <p class="time" :title="item.time_created">{{item.time_created}}</p>
          </div>
        </el-popover> -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    chatList: {
      typeof: Array,
      default: () => []
    },
    currentId: {
      typeof: String,
      default: ''
    },
    canAdd: {
      typeof: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    handleCheckChat(id) {
      this.$emit('changeCurrent', id)
    },
    createChat() {
      this.$emit('createChat')
    }
  }
}
</script>

<style lang="scss" scoped>
.history-content {
  display: inline-block;
  width: 240px;
  height: 100%;
  vertical-align: top;
  padding: 24px 16px;
  border-right: 1px solid rgba(41, 44, 51, 0.1);
}

.session-list {
  height: calc(100% - 50px);
  overflow-y: auto;
}

.chat-history-card {
  overflow: hidden;
  position: relative;
  height: 56px;
  margin-bottom: 8px;
  cursor: pointer;
  // border: 1px solid transparent;
  user-select: none;
  padding: 8px;
  display: flex;
  justify-content: space-between;

  &:hover {
    background: rgba(50, 53, 60, 0.04);
  }

  p {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .title {
    display: flex;
    // align-items: center;
    color: #26272A;
    height: 22px;
    line-height: 22px;

    i {
      margin-right: 8px;
      margin-top: 2px;
      width: 14px;
      height: 14px;
    }

    .title-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .time {
    color: #898B8E;
  }

}

.active {
  background: var(--color-menu-active);
  &:hover {
    background: var(--color-menu-active);
  }

  .title, .time, i {
    color: var(--color-theme);
  }
  
}
</style>
