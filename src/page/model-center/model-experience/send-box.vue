<template>
  <div class="default-chat-main-style">
    <el-input
      type="textarea"
      v-model="question"  
      max-length="200"
      size="small"
      placeholder="请输入问题，我可以完成智能解答..."
      :auto-size="{ minRows: 4, maxRows: 4 }"
      class="textarea"
      resize="none"
      @keydown.native="handleKeyDown"
    >
    </el-input>
    <div style="display: flex; justify-content: space-between; margin-top: 10px">
      <div style="display: flex">
        <!-- <div class="download" title="上传文件" @click="handleUpload">
          <PaperClipOutlined /> 上传文件
        </div> -->
        <!-- <div
          :class="need_wb_sarch ? 'download active' : 'download'"
          title="联网搜索"
          @click="handleChangeNeedWbSearch"
        >
          <i class="iconfont icon-GL_wangluo"></i> 联网搜索
        </div> -->
        <!-- <div class="download" title="下载" @click="downloadChat">
          <SvgIcon name="chat-download" /> 下载
        </div> -->
      </div>
      <div>
        <cl-button type="primary" :disabled="showLoading || isShowErrorTips" @click="send">
          <i class="iconfont icon-yingyongrukou"></i>
          发送(Enter)
        </cl-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showLoading: {
      type: Boolean,
      default: false
    },
    isShowErrorTips: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      need_wb_sarch: false,
      question: ''
    }
  },
  methods: {
    send() {
      if(!this.question) {
        this.$message.error('请输入问题')
        return
      }
      this.$emit('send', {question: this.question})
      this.question = '' // 清空输入框
    },
    handleChangeNeedWbSearch() {
      this.need_wb_sarch = !this.need_wb_sarch;
      this.$emit('handleChangeNeedWbSearch', this.need_wb_sarch);
    },
    handleKeyDown(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        // 阻止默认行为，防止换行符被插入到文本框中
        e.preventDefault();
        // send(); // 发送消息
        if (this.showLoading === false) {
          if(!this.question) {
            this.$message.error('请输入问题')
            return
          }
          this.$emit('send', {question: this.question});
          this.question = ''; // 清空输入框
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.default-chat-main-style {
  padding: 10px;
  // border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: rgba(50, 53, 60, 0.08);
  margin-top: 10px;
  border: 1px solid transparent;

  &:hover {
    // box-shadow: 0 4px 8px rgba(5, 145, 255, 0.1);
    border: 1px solid rgba(41, 44, 51, 0.10);
  }
  &:focus {
    border: 1px solid rgba(41, 44, 51, 0.10);
  }

  .download {
    cursor: pointer;
    padding: 3px 13px;
    margin-right: 10px;
    border-radius: 4px;
    background: var(--color-theme-router-background);
    color: var(--color-theme-text);

    svg {
      width: 16px;
      height: 16px;
    }
    &.control-true {
      border: 1px solid var(--color-theme);
      color: var(--color-theme);
    }
    &.control-false {
      border: 1px solid #e5e5e5;
      color: #666666;
    }
  }
  .active {
    background: var(--color-menu-active);
    color: var(--color-theme);
  }

  .send-plane {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    padding: 2px;
    vertical-align: middle;
  }

  /deep/ .el-textarea__inner {
    background: transparent !important;
    border: none !important;
  }
}
</style>
