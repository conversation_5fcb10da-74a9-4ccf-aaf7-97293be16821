<template>
<sugon-dialog :isShow.sync="state" @confirm="confirm" width="960" :title="title">
  <div class="sugon-dialog-out-padding" v-loading="loading">
    <el-radio-group v-model="searchType" @change="getModelList">
      <el-radio-button label="模型广场"></el-radio-button>
      <el-radio-button label="模型仓库"></el-radio-button>
    </el-radio-group>
    <!-- <div style="margin: 16px 0;">
      <el-input v-model="searchModelName" style="width: 240px;" placeholder="从全部模型中进行搜索"></el-input>
      <cl-button @click="handleSearchModel">搜索</cl-button>
    </div> -->
    <div class="model-list" style="margin-top: 16px;" v-if="(searchType === '模型广场' && !currentModel && modelChild.length) || (searchType === '模型广场' && currentModel) || (searchType === '模型仓库' && modelChild.length)">
      <div class="model-list-child" style="width: 30%;" v-if="searchType === '模型广场'">
        <div class="title">模型系列</div>
        <div class="model-list-content">
          <div :class="currentModel === item.code ? 'model model1 model-active' : 'model model1'"
            v-for="(item, index) in modelList" :key="index" @click="handleCheckModel(item)">
            <div style="line-height: 24px;"><img :src="item.description" style="margin-right: 8px;height: 24px;" />{{item.name}}</div>
            <div><el-badge :value="item.children" class="item" style="line-height: 40px;"></el-badge></div>
          </div>
        </div>
      </div>
      <div class="line-style" v-if="searchType === '模型广场'"></div>
      <div class="model-list-child" :style="{width: searchType === '模型广场' ? '30%' : '49%'}">
        <div class="title">{{ searchType === '模型广场' ? '模型版本' : '模型名称' }}</div>
        <div class="model-list-content">
          <div :class="currentModelChild&& currentModelChild.name === ite.name ? 'model model2 model-active' : 'model model2'"
            v-for="(ite, inde) in modelChild" :key="inde" @click="handleChangeModel(ite)">
            <div style="width: 100%;">
              <img :src="handleImg(ite.manufacturer)" style="height: 24px;" alt="">
              <div style="display: inline-block;vertical-align: middle;margin-left: 8px;width:calc(100% - 37px);">
                <p :title="ite.name">{{ite.name}}</p>
                <p :title="ite.description" style="color: #898B8E;" v-if="searchType === '模型广场'">{{ite.description}}</p>
                <p :title="ite.foundation_model_id" style="color: #898B8E;" v-else>基础模型：{{ite.foundation_model_id}}</p>
              </div>
            </div>
          </div>
          <div v-if="!modelChild.length" class="nodata-style">
            <img :src="nodataUrl" />
            <div style="color: #909399;">没有查询到符合条件的记录</div>
          </div>
        </div>
      </div>
      <div class="line-style"></div>
      <div class="model-list-child" :style="{width: searchType === '模型广场' ? '30%' : '49%'}">
        <div class="title">模型服务接入点</div>
        <div class="model-list-content">
          <div :class="currentModelChild&& currentModelChild.deployment_id === itec.deployment_id ? 'model model2 model-active' : 'model model2'"
            v-for="(itec, inde) in clusterList" :key="inde" @click="currentModelChild.deployment_id = itec.deployment_id">
            <div style="width: 100%;">
              <!-- <div style="display: inline-block;vertical-align: middle;margin-left: 8px;"> -->
                <p :title="itec.deployment_id">{{ itec.deployment_id }}</p>
                <p :title="itec.cluster_name" style="color: #898B8E;">{{itec.cluster_name}}</p>
              <!-- </div> -->
            </div>
          </div>
          <div v-if="!clusterList.length" class="nodata-style">
            <img :src="nodataUrl" />
            <div style="color: #909399;">没有查询到符合条件的记录</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="model-list1" style="" v-if="searchType === '模型仓库' && modelChild.length">
      <div :class="currentModelChild&&currentModelChild.name == ite.name ? 'model model-active' : 'model'"
        v-for="(ite, inde) in modelChild" :key="inde" @click="currentModelChild = {name: ite.name, model_id: ite.model_id}">
        <div style="width: calc(100% - 30px);">
          <img :src="handleImg(ite.manufacturer)" alt="">
          <div style="display: inline-block;vertical-align: middle;margin-left: 8px;width:calc(100% - 45px);">
            <p :title="ite.name">{{ite.name}}</p>
            <p :title="ite.foundation_model_id" style="color: #898B8E;">基础模型：{{ite.foundation_model_id}}</p>
          </div>
        </div>
      </div>
    </div> -->
    <div v-if="!modelChild.length && !loading && !modelList.length" class="nodata-style">
      <img :src="nodataUrl" />
      <div style="color: #909399;">没有查询到符合条件的记录</div>
    </div>
  </div>
</sugon-dialog>
</template>

<script>
import { get_model } from "@/http/squrare-http";
import { get_model_repository } from "@/http/model-http/model-http";
import { changeModelChat } from '@/http/chat-http/chat-base-http';
import mixin from "../model-square/mixin.js";

export default {
  mixins: [mixin],
  data() {
    return {
      state: false,
      searchType: '模型广场',
      searchModelName: '',
      totle: 8,
      modelList: [],
      modelChild: [],
      clusterList: [],
      currentModel: '',
      currentModelChild: {},
      title: '选择模型，创建新对话',
      chat_session_id: '',
      listObj: {},
      nodataUrl: require('@/assets/images/EmptyState.svg'),
      model_params_setting: []
    }
  },
  methods: {
    open(data) {
      this.state = true
      this.currentModel = ''
      this.searchType = '模型广场'

      if(data && data !== 'new') {
        this.title = '切换模型'
        this.currentModelChild = {name: data.name, deployment_id: data.model_id, model_id: ''}
        this.chat_session_id = data.chat_session_id
      } else {
        this.title = '选择模型，创建新对话'
        this.currentModelChild = {name: '', model_id: '', deployment_id: ''}
        this.chat_session_id = ''
      }
      this.getModelList()
    },
    confirm(response) { //如果表单有校验，会自动校验。不需另外写element的校验函数。response返回有按钮loading关闭函数
      if(!this.currentModelChild.name) {
        this.$message.error('请选择模型')
        response.close()
        return
      }
      if(!this.currentModelChild.deployment_id) {
        this.$message.error('请选择模型服务接入点')
        response.close()
        return
      }
      
      if(this.title === '切换模型' && !!this.chat_session_id) { // 存在没有历史会话，是初次创建对话，不需要调接口
        changeModelChat(this.chat_session_id, { model_id: this.currentModelChild.deployment_id }).then(data => {
          response.close()
          this.$handle_http_back(data, true, false).then(res => {
            this.$emit('OK', { name: this.currentModelChild.name, model_id: this.currentModelChild.deployment_id }, this.model_params_setting, this.chat_session_id)
            this.state = false
          })
        }).catch(_ => {
          response.close()
        })
      } else {
        response.close()
        this.$emit('OK', { name: this.currentModelChild.name, model_id: this.currentModelChild.deployment_id }, this.model_params_setting)
        this.state = false
      }
    },
    
    handleSearchModel() { // 搜索模型

    },
    getModelList() {
      this.loading = true
      let _pa = {
        page_num: 1,
        page_size: 10000000,
        name: '',
        status: 1,        // model_enum: 1
        cluster_condition: true
      }
      this.currentModelChild = {}
      this.currentModel = ''
      this.modelList = []
      this.modelChild = []
      this.clusterList = []

      if(this.searchType === '模型广场') {
        // _pa.project_id = localStorage.ProjectId
        // _pa.user_id = GetUserInfo("userId")
        _pa.online = true
        _pa.model_type = this.$route.name === 'viewModelExperience' ? 'IU' : 'TG'
        // _pa.content_windows_value = '' 
        // _pa.manufacturer = ''
        get_model(_pa).then(data => {
          this.handleListFilter(1, data)
        }).catch(_ => {
          this.loading = false
        })
      } else {
        _pa.project_id = localStorage.getItem("ProjectId") || ''
        get_model_repository(_pa).then(data => {
          this.handleListFilter(2, data)
        }).catch(_ => {
          this.loading = false
        })
      }
    },
    handleListFilter(type, data) {
      this.listObj = {}
      this.loading = false
      this.$handle_http_back(data, true, false).then(res => {
        let _a = res.content.list || []
        let _arr = []
        _a.forEach(e => {
          let _obj = {
            deployment_id: e.deployment_id,
            cluster_name: e.cluster_name,
            cluster_id: e.cluster_id
          }

          let _n = _arr.filter(o => o.model_id === e.model_id)
          if(_n.length) {
            _n[0].children.push(_obj)
          } else {
            _arr.push({
              model_id: e.model_id,
              name: e.name,
              manufacturer: e.manufacturer || '',
              description: e.description || '',
              foundation_model_id: e.foundation_model_id || '',
              system_prompt: e.system_prompt || '',
              temperature: e.temperature || '',
              top_p: e.top_p || '',
              max_tokens: e.max_tokens || '',
              chat_config: e.chat_config,
              children: [_obj]
            })
          }
          
        })
        this.modelChild = _arr
        if (type === 1 && this.modelChild.length) {
          let _b = []
          let _c = this.$getCode({type:'provider'})

          _c.forEach(v => {
            let _t = this.modelChild.filter(m => m.manufacturer === v.code)
            
            if(_t.length) {
              let _k = {
                name: v.name,
                code: v.code,       // 模型列表的厂家code
                description: v.description,       // 模型列表的厂家图标
                children: _t.length
              }
              _b.push(_k)
            }
            
            this.listObj[v.code] = _t
          })
          
          this.modelList = _b
        }

        // console.log('模型', this.modelList, this.modelChild)
      })
    },
    handleCheckModel(item) { // 选择模型系列
      this.currentModel = item.code
      this.currentModelChild = {}
      this.modelChild = this.listObj[item.code]
      this.clusterList = []
    },
    handleImg(m) {
      return m ? this.transType(m, 'isImage') : ''
    },
    cluster_list() {

    },
    handleChangeModel(ite) {
      let _a = this.modelChild.filter(e => e.model_id === ite.model_id)
      this.clusterList = _a.length ? _a[0].children : []
      this.currentModelChild = {name: ite.name, model_id: ite.model_id, deployment_id: this.clusterList.length ? this.clusterList[0].deployment_id : ''}
      this.model_params_setting = ite.chat_config ? JSON.parse(ite.chat_config) : []
      // console.log('hhhhh', this.model_params_setting, ite)
    },
  }
}
</script>

<style lang="scss" scoped>
.sugon-dialog-out-padding {
  min-height: 300px;
}
.model-list {
  // background: url('../../../assets/images/model_experience_bg.svg') no-repeat center / cover;
  height: 436px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  grid: 16px;

  .line-style {
    border-right: 1px solid rgba(41, 44, 51, 0.1);
    width: 1px;
    height: 100%;
  }
}

.model-list-child {
  // width: 50%;
  // border: 1px solid var(--color-theme-header-search-border);
  height: 100%;
  overflow: auto;
  vertical-align: top;

  .title {
    padding: 10px 0;
    font-size: 14px;
    height: 40px;
    margin-bottom: 6px;
    // background: rgba(50, 53, 60, 0.08);
  }

  .model-list-content {
    // padding: 10px;
  }

  .model {
    width: 100%;
    padding: 6px 10px;
    margin-bottom: 6px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    // border: 1px solid var(--color-theme-header-search-border);
    border: 1px solid transparent;

    &:hover {
      background: rgba(50, 53, 60, 0.04);
    }
    
  }
  .model:last-child {
    margin-bottom: 0;
  }

  .model1 {
    height: 40px;
    // align-items: center;
    // height: 50px;
    /deep/ .el-badge__content {
      background-color: var(--color-theme) !important;

    }
    
  }
  .model2 {
    // height: 52px;
    padding: 7px 10px;

    p {
      // width: calc(100% - 20px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
// .model-list-child:first-child {
//   margin-right: 24px;
// }

.model-list1 {
  height: 436px;
  margin-bottom: 10px;
  margin-top: 16px;
  overflow-x: auto;
  // padding: 10px;

  .model {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding:  8px 10px;
    margin-bottom: 6px;
    border: 1px solid var(--color-theme-header-search-border);
    cursor: pointer;

    &:hover {
      background: rgba(50, 53, 60, 0.04);
    }
  }
}

.model-active {
  border: 1px solid var(--color-theme) !important;
  background: var(--color-menu-active);
  &:hover {
    background: var(--color-menu-active) !important;
  }
}

.nodata-style {
  text-align: center;
  // margin: auto;
  margin-bottom: 50px;
  // position: absolute;
  // top: 50%;
  // left: 50%;
  // transform: translate(-50%, -50%); /* 调整偏移量 */
}
</style>
