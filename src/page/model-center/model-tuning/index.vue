<template>
  <cl-page-container title="模型调优" explain="模型调优" :hideHead="true">
    <bread name="模型调优" description="支持多种训练方式，确定训练方向并选择合适的训练数据及参数，产出更加符合用户需求的模型。"></bread>
    <cl-table-container name="compare-data-grop" model="flex">
      <cl-table-header 
      ref="my_header"
      placeholder="搜索（模型名称）"
      @search="table_search"
      inputWidth="208px"
      :rightHandles="rightHandles" 
      :table_loading="table_loading"
      :page_num="page_num"
      :page_size="page_size">
      <template slot="left">
        <div class="table-tool-bar-left">
          <cl-button
                type="primary"
                icon="el-icon-plus"
                @click="operateStrategy('', 'add')"
              >
                训练新模型
              </cl-button>
          <cl-button
            type="danger"
            icon="el-icon-delete"
            :disabled="table_select.length === 0"
            @click="operateStrategy('', 'delBatchDialog')"
          >
            批量删除
          </cl-button>
        </div>
      </template>
    </cl-table-header>
      <cl-table-body>
        <cl-table element-loading-text="加载中..." :data="table_list" :ref="`all_model_repository`" @selection-change="selectionChange" :columns="tableColunms" @no-data="noData" ></cl-table>
      </cl-table-body>
      <cl-table-footer>
        <cl-pagination background v-if="table_list.length != 0" @size-change="handleListSizeChange"
            @current-change="handleListCurrentChange" :current-page="page_num"
            :page-sizes="[10, 20, 50, 100]" :page-size="page_size"
            layout="sizes, total, jumper, next, pager, prev" :total="page_total">
          </cl-pagination>
      </cl-table-footer>
    </cl-table-container>

    
    <stopDialog ref="stopDialog" @ok="getList"/>
    <sugon-delete-dialog
      :list="table_select"
      :isShow.sync="dialogDel"
      @confirm="delTableChoose"
      :title="`${table_select && table_select.length > 1 ? '删除' : '删除'}`"
      nameKey="model_id"
      idKey="id"
    >
    </sugon-delete-dialog>
  </cl-page-container>
</template>
<script>
import * as modelHttp from "@/http/model-http/model-http";
import bread from '@/components/bread/index.vue';
import stopDialog from './handle/stop-dialog';
import coljs from './table_col.js';
export default {
  name:'modelTuning',
  components: {stopDialog, bread},
  data() {
    return {
      search_value: "",
      tabActiveName: "all",
      table_list: [],
      page_size: 10,
      page_num: 1,
      page_total: 0,
      table_loading: false,
      table_select: [],
      tableColunms: [],
      dialogDel: false,
      project_id: localStorage.getItem("ProjectId") || '',
      searchItems: [
        {
          label: '模型名称',
          prop: 'model_id'
        },
        {
          label: '任务类型',
          prop: 'task_type'
        },
        {
          label: '训练状态',
          prop: 'training_status'
        },
      ],
      searchKey: 'model_id',
      task_type:'',
      training_status:'',
      task_type_arr: [],
      training_status_arr: [],
      timer: null,
      Training: {
        '排队中': 0,
        '训练中': 1,
        '训练成功': 2,
        '训练失败': 3,
        '终止训练': 4,
        '训练超时': 5
      },
      rightHandles: [
        'setBtn',
        () => {
          return <el-select class="header-right-selset-sps"
					 	value={this.tabActiveName} placeholder="请选择"
						style="margin-right: 8px" 
						onChange={this.handleClick}>
						<el-option label='全部' value='all'></el-option>
						<el-option label='我创建的' value='mine'></el-option>
          </el-select>
        },
        () => {
          return <el-select clearable 
          class="header-right-selset-sps-160" 
          value={this.task_type} 
          placeholder="请选择任务类型"
          style="margin-right: 8px;" 
          onChange={this.task_type_change}>
            {this.task_type_arr.map(item => 
              <el-option label={item.name} value={item.code}></el-option>)}
          </el-select>
        },
        () => {
          return <el-select clearable 
          class="header-right-selset-sps" 
          value={this.training_status} 
          placeholder="请选择训练状态"
          style="margin-right: 8px;" 
          onChange={this.training_status_change}>
            {this.training_status_arr.map(item => 
              <el-option label={item.name} value={item.code}></el-option>)}
          </el-select>
        },
        'input',
        () => <cl-button onClick={this.reset}>重置</cl-button>
        ,'search'],
    }
  },
  mounted() {
    this.task_type_arr = this.$getCode({type:'task_type'})
    this.training_status_arr = this.$getCode({type:'job_status'})
    this.getList();
    this.tableColunms = coljs(this)
  },
  methods: {
    canSelectable(row){
      return row.training_status == '训练失败' || row.training_status == '训练成功' || row.training_status == '终止训练' || row.training_status == '训练超时'
    },
    reset() {
      this.tabActiveName = 'all'
      this.$refs.my_header.input_value('')
      this.search_value = ''
      this.task_type = ''
      this.training_status = ''
      this.getList()
    },
    typeFilter: function (val) {
      return val === '训练成功' ? 'success' : val === '训练失败' ? 'error' : 'warning'
    },
    table_search(res) {
      this.search_value = res.value
      this.page_num = res.page_num
      this.page_size = res.page_size
      this.getList()
    },
    noData(){
      if(this.page_num>1){
      this.page_num = this.page_num - 1
      this.getList()
      }
    },
    changeKey(val) {
      this.searchKey = val
      this.search_value = ''
    },
    name_change(val) {
      this.search_value = val
    },
    training_status_change(val) {
      this.training_status = val
      this.getList()
    },
    task_type_change(val) {
      this.task_type = val
      this.getList()
    },
    getList(id) {
      clearTimeout(this.timer)
      this.timer = null;
      if (!(id && id === 'autoRefresh')) {
          this.table_loading = true;
      }
      let params = {
        page_num: this.page_num,
        page_size: this.page_size,
        project_id: this.project_id,
        user_id: this.tabActiveName === 'all' ? '' : GetUserInfo('userId'),
        task_type: this.task_type,
        training_status: this.training_status,
        model_name: this.search_value
      };
      modelHttp
        .get_training_job(params)
        .then((res) => {
          this.$handle_http_back(res,true,false)
          if (res.content) {
            this.page_total = res.content.total;
            this.table_list = res.content.list;
            let index = this.table_list.find((item) => item.training_status == '训练中')
            if (index && !this.timer) {
              this.timer = setTimeout(() => {
                this.getList('autoRefresh')
              }, 10000);
            } else {
              this.timer = null;
            }
          }
          this.table_loading = false;
        })
        .catch((_) => {
          this.table_loading = false;
        });
    },
    handleClick(val) {
      this.tabActiveName = val
      this.page_num = 1;
      this.searchKey = 'model_id'
      this.search_value = ''
      this.page_total = 0;
      this.page_size = 10;
      this.table_list = [];
      this.table_select = []
      this.getList();
    },
    handleListSizeChange(page_size) {
      this.page_size = page_size;
      /**每页条数发生变化 */
      this.getList();
    },
    handleListCurrentChange(pageIndex) {
      /**当前页发生变化 */
      this.page_num = pageIndex;
      this.getList();
    },
    selectionChange(row) {
      this.table_select = row;
    },
    operateStrategy(item, data) {
      switch (data) {
        case 'delDialog':
          this.table_loading = false
          this.table_select = []
          this.table_select.push(item)
          this.$table_select_repeat(this.table_select, this.table_list, 'all_model_repository')
          this.dialogDel = true
          break
        case 'delBatchDialog':
          this.dialogDel = true
          this.table_loading = false
          break
        case 'stopTask':
          this.$refs.stopDialog.open(item)
          break
        case 'add':
          this.$go_detail(this, `/modelTuning/training`,
            {
              page_size: this.page_size,
              page_num: this.page_num,
            })
          break
        default:
          break
      }
    },
    delTableChoose(response) {
      let value = response.data.map((el) => {
        return {
          name: el.name,
          params: el.id,
          request: modelHttp.del_training_job,
        };
      });
      const that = this;
      const batch_delete = new this.$batch_delete({
        data: value,
        title: "删除提示",
        success: function () {
          that.dialogDel = false;
          that.getList();
        },
        error: function (err) {
          that.dialogDel = false;
          that.getList();
        },
      });
      /**发送请求 */
      batch_delete.del();
    },
    goto(link) {
      this.$router.push(link)
      this.$nextTick(_=>{
        AppMenu.setActive(link)
      })
    },
    taskTypeFilter(val) {
      let value = this.$getCode({type:'task_type'}).find((item) => {
        return item.code == val
      })
      return value.name
    }
  },
  destroyed() {
  },
};
</script>
<style lang="scss" scoped>
>>> .el-tabs,.el-tabs--top {
  height: 100% !important;
}
>>> .el-tabs__content {
  height: calc(100% - 56px);
}
>>> .el-tab-pane {
  height: calc(100%);
}
>>>  .header-right-selset-sps .el-input{
  width: 140px !important;
}
>>> .header-right-selset-sps-160 .el-input {
  width: 170px !important;
}
</style>
