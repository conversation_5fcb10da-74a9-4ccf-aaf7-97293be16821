<template>
  <div class="model-training">
    <sugon-back-button name="训练新模型"></sugon-back-button>
    <div
      style="display: flex;flex-direction: column;height: calc(100% - 50px);"
    >
      <div class="model-training-container">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-position="left"
          label-width="130px"
        >
          <div class="form-title" style="margin-top: 0;">选择模型训练方式</div>
          <div class="card-list-tm" ref="cardList">
            <div
              :class="
                selectTrainType === card.name
                  ? 'card-box card-box-active'
                  : 'card-box'
              "
              v-for="(card, index) of cards"
              :key="index"
              @click="handelClickType(card)"
            >
              <div class="card-header">
                <div class="card-title">
                  <span class="text">{{ card.name }}</span>
                </div>
                <div class="card-title-content">
                  <span class="text">{{ card.description }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="form-title">模型参数配置</div>
          <el-form-item label="选择模型" prop="trainModelType">
            <el-radio-group
              v-model="form.trainModelType"
              @change="changeTrainModelType"
            >
              <el-radio label="fondationmodel">预置模型</el-radio>
              <el-radio label="CustomModel">自定义模型</el-radio>
            </el-radio-group>
            <div
              class="training-type-tips"
              v-if="form.trainModelType == 'fondationmodel'"
            >
              选择预置模型进行训练
            </div>
            <div
              class="training-type-tips"
              v-if="form.trainModelType == 'CustomModel'"
            >
              选择已完成训练模型进行增量训练
            </div>
            <div style="margin-top: 8px;">
              <el-select
                style="width: 400px;"
                placeholder="请选择模型"
                v-model="form.trainModel"
                @change="handelChangeModel"
              >
                <el-option
                  :label="item.name"
                  :value="item.model_id"
                  v-for="(item, index) of model_list"
                  :key="index"
                ></el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item
            label="训练方式"
            prop="trainingTypes"
            v-if="choose_model"
          >
            <el-radio-group
              v-model="form.trainingTypes"
              @change="changeTrainingTypes"
            >
              <el-radio label="lora" :disabled="!choose_model_has_lora"
                >高效训练</el-radio
              >
              <el-radio label="full" :disabled="!choose_model_has_full"
                >全参训练</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="模型名称" prop="modelId">
            <el-input
              style="width: 400px;"
              placeholder="请输入模型名称"
              v-model="form.modelId"
            ></el-input>
          </el-form-item>
          <el-form-item label="选择集群" prop="cluster_id">
            <el-select
              style="width: 400px;"
              placeholder="请选择集群"
              v-model="form.cluster_id"
              @change="handelChangeCluster"
            >
              <el-option
                :label="item.name"
                :value="item.id"
                v-for="(item, index) of cluster_list"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择训练数据" prop="trainSet">
            <el-cascader
              ref="cascader"
              v-model="form.trainSet"
              style="width: 400px;"
              :options="data_set_list"
              :props="cascader_props"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item label="选择验证数据" prop="ValidationSet">
            <el-radio-group v-model="form.ValidationSet">
              <el-radio :label="0.1">自动切分</el-radio>
              <el-radio label="6" disabled>选择验证集</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="form-title" v-if="hyper_parameters_map.length > 0">
            超参配置
            <span class="click-reset-button" @click="handleClickReset"
              >恢复默认</span
            >
          </div>
          <div v-if="hyper_parameters_map.length > 0">
            <div v-for="(item, index) in hyper_parameters_map" :key="index">
              <el-form-item
                v-if="item.type == 'number' || item.type == 'intInputNumber'"
                :label="item.displayName"
                :prop="item.name"
                :key="item.name"
              >
                <span slot="label">
                  {{ item.displayName }}
                  <el-tooltip placement="top-start">
                    <div slot="content">{{ item.description }}</div>
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </span>
                <el-input-number
                  style="width: 400px;"
                  :step="item.step"
                  :min="item.range[0]"
                  :max="item.range[1]"
                  v-model="form.hyperParams[item.name]"
                ></el-input-number>
              </el-form-item>
              <el-form-item
                v-if="item.type == 'floatInputNumber'"
                :label="item.displayName"
                :prop="item.name"
                :key="item.name"
              >
                <span slot="label">
                  {{ item.displayName }}
                  <el-tooltip placement="top-start">
                    <div slot="content">{{ item.description }}</div>
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </span>
                <el-input-number
                  :precision="2"
                  style="width: 400px;"
                  :step="item.step"
                  :min="item.range[0]"
                  :max="item.range[1]"
                  v-model="form.hyperParams[item.name]"
                ></el-input-number>
              </el-form-item>
              <el-form-item
                v-if="item.type == 'input'"
                :label="item.displayName"
                :prop="item.name"
                :key="item.name"
              >
                <span slot="label">
                  {{ item.displayName }}
                  <el-tooltip placement="top-start">
                    <div slot="content">{{ item.description }}</div>
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </span>
                <el-input
                  style="width: 400px;"
                  v-model="form.hyperParams[item.name]"
                ></el-input>
              </el-form-item>
              <el-form-item
                v-if="item.type == 'select'"
                :label="item.displayName"
                :prop="item.name"
                :key="item.name"
              >
                <span slot="label">
                  {{ item.displayName }}
                  <el-tooltip placement="top-start">
                    <div slot="content">{{ item.description }}</div>
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </span>
                <el-select
                  style="width: 400px;"
                  v-model="form.hyperParams[item.name]"
                >
                  <el-option
                    :label="item"
                    :value="item"
                    v-for="(item, index) of item.supportValues"
                    :key="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div class="training-footer-btn">
        <cl-button type="primary" @click="submitForm('form')"
          >开始训练</cl-button
        >
        <cl-button
          @click="
            () => {
              $router.push('/modelTuning');
            }
          "
          >取消</cl-button
        >
      </div>
    </div>
  </div>
</template>
<script>
import {
  get_hyper_parameters,
  post_training_job,
  get_cluster_list
} from "@/http/model-http/model-http";
import { get_data_set_list } from "@/http/data-center";
export default {
  name: "TuningNewModel",
  data() {
    const nameTestRule = (rule, value, callback) => {
      if (value.length > 64 || value.length < 2) {
        callback(new Error("模型名称长度在 2 - 64 个字符之间"));
      } else if (!/^[0-9A-Za-z\-]{0,}$/.test(value)) {
        callback(new Error("模型名称只支持英文大小写、数字、横线"));
      } else {
        callback();
      }
    };
    return {
      cascader_props: {
        multiple: true
      },
      radio: 3,
      selectTrainType: "SFT微调训练",
      cards: [],
      train_type: [],
      form: {
        trainModelType: "fondationmodel",
        modelId: "",
        hyperParams: {},
        trainSet: [],
        trainingTypes: "",
        trainModel: "",
        ValidationSet: 0.1,
        cluster_id: ""
      },
      setting_list: [],
      choose_flavor: {},
      choose_model: null,
      selectTrainTypeJson: null,
      max_available: 1,
      model_list: [],
      flavor_list: [],
      choose_model_has_lora: false,
      choose_model_has_full: false,
      hyperParametersKey: "",
      hyper_parameters_map: [],
      data_set_list: [],
      cluster_list: [],
      project_id: "",
      rules: {
        trainModelType: [
          {
            required: true,
            message: "请选择模型",
            trigger: "blur"
          }
        ],
        cluster_id: [
          {
            required: true,
            message: "请选择集群",
            trigger: "blur"
          }
        ],
        modelId: [
          {
            required: true,
            message: "请输入模型名称",
            trigger: "blur"
          },
          {
            validator: nameTestRule
          }
        ],
        trainingTypes: [
          {
            required: true,
            message: "请选择训练方式",
            trigger: "blur"
          }
        ],
        trainModel: [
          {
            required: true,
            message: "请选择模型1",
            trigger: "blur"
          }
        ],
        trainSet: [
          {
            required: true,
            message: "请选择训练数据",
            trigger: "blur"
          }
        ],
        ValidationSet: [
          {
            required: true,
            message: "请选择验证数据",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    this.getCardList();
    this.getClusterList();
    this.project_id = this.$get_projectId();
    console.log(this.$route.params);
    if (this.$route.params && this.$route.params.training_type) {
      let type = this.$route.params.training_type.split("_")[0];
      let foundation_model_id = this.$route.params.foundation_model_id;
      if (foundation_model_id) {
        this.form.trainModelType = "CustomModel";
      } else {
        this.form.trainModelType = "fondationmodel";
      }
      this.selectTrainTypeJson = this.train_type.find(
        item => item.code == this.$route.params.training_type
      );
      this.selectTrainType = this.selectTrainTypeJson.name;
      console.log(this.selectTrainTypeJson);
      this.getHyperParameters(
        type,
        foundation_model_id ? "CustomModel" : "fondationmodel",
        'is_create'
      );
    } else {
      this.selectTrainTypeJson = this.train_type.find(
        item => item.name == this.selectTrainType
      );
      this.getHyperParameters();
    }
    this.getDataSetList();
  },
  methods: {
    //集群
    getClusterList() {
      let project_id = localStorage.getItem("ProjectId") || "";
      get_cluster_list(project_id)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.cluster_list = res.content;
          }
        })
        .catch(_ => {});
    },
    go_back() {
      this.$back_list(this);
    },
    //处理数据集结构
    processCascaderData(sourceData) {
      return sourceData.map(item => {
        // 创建第一级对象
        const firstLevel = {
          value: item.id, // 使用 id 作为 value
          label: item.name, // 使用 name 作为 label
          children: item.ai_data_set_version_vo_list.map(version => ({
            value: version.id, // 使用 data_set_id 作为 value
            label: version.version_name // 使用 version_name 作为 label
          }))
        };
        return firstLevel;
      });
    },
    changeTrainModelType() {
      this.form.trainModel = null;
      this.form.modelId = "";
      this.form.trainingTypes = "";
      this.choose_model = null;
      this.getHyperParameters();
    },
    //选择模型
    handelClickType(card) {
      this.form.trainModel = null;
      this.form.trainingTypes = "";
      this.form.modelId = "";
      this.choose_model = null;
      this.selectTrainType = card.name;
      this.selectTrainTypeJson = card;
      this.getHyperParameters();
      this.getDataSetList();
    },
    //模型训练方式
    changeTrainingTypes(val) {
      let key = "";
      if (this.selectTrainTypeJson.pre_code == "cpt") {
        key = "cpt";
      } else {
        key = this.selectTrainTypeJson.pre_code + "_" + val;
      }
      this.hyperParametersKey = key;
      this.hyper_parameters_map = this.choose_model.hyper_parameters_map[key];
      this.hyper_parameters_map.map(item => {
        if (item.name && item.type) {
          this.$set(this.form.hyperParams, item.name, item.recommendValue);
        }
      });
      console.log(this.hyper_parameters_map, "key");
    },
    //恢复默认
    handleClickReset() {
      this.hyper_parameters_map.map(item => {
        if (item.name && item.type) {
          this.$set(this.form.hyperParams, item.name, item.recommendValue);
        }
      });
    },
    //生成模型code
    generateTimestampWithRandomCode(mode_id) {
      console.log('in change')
      // 获取当前时间
      const now = new Date();
      // 格式化时间戳为 YYYYMMDDHHMM
      const timestamp = now
        .toISOString()
        .slice(0, 19)
        .replace(/[-T:]/g, "")
        .slice(0, 12);
      // 生成6位随机码（数字和小写字母）
      const characters = "abcdefghijklmnopqrstuvwxyz0123456789";
      let randomCode = "";
      for (let i = 0; i < 6; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        randomCode += characters[randomIndex];
      }
      // 组合时间戳和随机码
      const result = `${mode_id}-${timestamp}-${randomCode}`;
      return result;
    },
    // 模型字典
    getCardList() {
      let cards = this.$getCode({ type: "task_type" }).map(item => {
        return {
          name: item.name.split("-")[0],
          description: item.description,
          type: item.name.split("-")[1],
          code: item.code,
          pre_code: item.code.split("_")[0]
        };
      });
      this.train_type = cards;
      let uniqueArr = cards.filter((value, index, self) => {
        return index === self.findIndex(t => t.name === value.name);
      });
      this.cards = uniqueArr;
    },
    //选择模型
    handelChangeModel(val, is_create) {
      this.form.trainModel = val;
      this.form.trainingTypes = "";
      this.choose_model = this.model_list.find(item => item.model_id === val);
      let hyper_parameters_map = this.choose_model.hyper_parameters_map;
      let keys = this.choose_model.training_types;
      let types = keys.filter(
        item => item.indexOf(this.selectTrainTypeJson.pre_code) !== -1
      );
      if (types.includes(this.selectTrainTypeJson.pre_code + "_lora")) {
        this.choose_model_has_lora = true;
      } else {
        this.choose_model_has_lora = false;
      }
      if (types.includes(this.selectTrainTypeJson.pre_code + "_full")) {
        this.choose_model_has_full = true;
      } else {
        this.choose_model_has_full = false;
      }
      if (this.selectTrainTypeJson.pre_code == "cpt") {
        this.choose_model_has_full = true;
      }
      if (this.choose_model_has_lora) {
        this.form.trainingTypes = "lora";
      }
      if (!this.choose_model_has_lora && this.choose_model_has_full) {
        this.form.trainingTypes = "full";
      }
      this.changeTrainingTypes(this.form.trainingTypes);
      if (!(this.$route.params && this.$route.params.training_type)) {
        this.form.modelId =
          this.form.trainModelType == "CustomModel"
            ? this.generateTimestampWithRandomCode(
                this.choose_model.foundation_model_id
              )
            : this.generateTimestampWithRandomCode(val);
      }
      console.log(val, 'is_create')
      console.log(this.$route.params, 'this.$route.params')
      if (this.$route.params && this.$route.params.training_type&&!is_create) {
        if (val == this.$route.params.model_id) {
          this.form.modelId = this.$route.params.model_id
        } else {
          this.form.modelId =
          this.form.trainModelType == "CustomModel"
            ? this.generateTimestampWithRandomCode(
                this.choose_model.foundation_model_id
              )
            : this.generateTimestampWithRandomCode(val);
        }
      }
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          console.log(this.form);
          let trainSet = this.form.trainSet;
          let train_dataset_versions = trainSet.map(item => {
            return item[1];
          });
          let data = {};
          data.training_type = this.hyperParametersKey;
          data.name = this.form.modelId;
          data.train_dataset_versions = train_dataset_versions;
          data.hyper_params = JSON.stringify(this.form.hyperParams);
          data.train_model = this.form.trainModel;
          data.train_model_type = this.form.trainModelType;
          data.foundation_model_id =
            this.form.trainModelType == "CustomModel"
              ? this.choose_model.foundation_model_id
              : "";
          data.cluster_id = this.form.cluster_id;
          data.project_id = this.project_id;
          post_training_job(data).then(data => {
            this.$handle_http_back(data, false, false).then(res => {
              this.$router.push("/modelTuning");
            });
          });
        }
      });
    },
    //数据集列表
    getDataSetList() {
      let params = {
        pageNum: 1,
        pageSize: 999,
        schemaType: this.selectTrainTypeJson.pre_code.toUpperCase()
      };
      get_data_set_list(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          let list = res.content.list || [];
          if (list.length > 0) {
            this.data_set_list = this.processCascaderData(list);
          }
        })
        .catch(_ => {});
    },
    //模型列表
    getHyperParameters(training_types, model_type, is_create) {
      let params = {
        model_type: model_type
          ? model_type
          : this.form.trainModelType == "CustomModel"
          ? ""
          : this.form.trainModelType,
        training_types: training_types
          ? training_types
          : this.selectTrainTypeJson.pre_code
      };
      get_hyper_parameters(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          this.model_list = res.content || [];
          if (this.$route.params && this.$route.params.training_type&&is_create) {
            this.choose_model = this.model_list.find(
              item => item.model_id == this.$route.params.model_id
            );
            this.form.trainModel = this.$route.params.model_id;
            this.form.modelId = this.$route.params.model_id;
            this.handelChangeModel(this.$route.params.model_id, is_create);
            let params = JSON.parse(this.$route.params.model_args);
            Object.keys(params).map(item => {
              this.$set(this.form.hyperParams, item, params[item]);
            });
          }
        })
        .catch(_ => {});
    }
  }
};
</script>
<style lang="scss" scoped>
.model-training {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.card-list-tm {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 24px;
  .card-box {
    cursor: pointer;
    flex: 1; /* 子元素宽度自动分配 */
    width: calc(33.333%); /* 每行 3 个，减去间距 */
    height: 90px;
    padding: 0 10px;
    box-sizing: border-box;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f8f8;
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.2s;
    border: 1px solid #e0e1e4;
    &:hover {
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    .card-header {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-around;
      .card-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        .text {
          font-size: 14px;
          color: #010205;
          font-weight: 600;
        }
      }
      .card-title-content {
        .text {
          font-size: 12px;
        }
      }
    }
  }
  .card-box-active {
    border: 1px solid var(--color-theme);
    background: var(--color-menu-active) !important;
  }
}
.model-training-container {
  height: calc(100% - 50px);
  overflow: auto;
  padding: 20px;
  background: #fff;
}
.form-title {
  font-size: 15px;
  font-weight: 600;
  margin: 15px 0;
}
.click-reset-button {
  font-weight: normal;
  font-size: 14px;
  margin-left: 10px;
  color: var(--color-theme);
  cursor: pointer;
}
.training-type-tips {
  font-size: 12px;
  color: #ccc;
}
.training-footer-btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 80px;
  border-top: 1px solid #ebeef5;
  padding-left: 24px;
}
</style>
