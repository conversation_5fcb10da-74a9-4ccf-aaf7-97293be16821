export default [{
  "hyperParametersMap": {
    "sft_full": [{
        "name": "useQwenMixedStrategy",
        "recommendValue": false,
        "step": 0
      },
      {
        "leftRange": "include",
        "isAdvancedParameter": false,
        "displayName": "循环次数",
        "name": "n_epochs",
        "recommendValue": 3,
        "description": "循环次数代表模型训练过程中模型学习数据集的次数，可理解为看几遍数据，一般建议的范围是1-3遍即可，可依据需求进行调整",
        "range": [
          1,
          200
        ],
        "step": 1,
        "rightRange": "include",
        "type": "number"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "学习率",
        "name": "learning_rate",
        "recommendValue": "1e-5",
        "description": "学习率代表每次更新数据的增量参数权重，学习率数值越大参数变化越大，对模型影响越大",
        "step": 0,
        "type": "input"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "批次大小",
        "name": "batch_size",
        "recommendValue": 16,
        "description": "批次大小代表模型训练过程中，模型更新模型参数的数据步长，可理解为模型每看多少数据即更新一次模型参数，一般建议的批次大小为16/32，表示模型每看16或32条数据即更新一次参数",
        "supportValues": [
          8,
          16,
          32
        ],
        "step": 0,
        "type": "select"
      },
      {
        "isAdvancedParameter": true,
        "displayName": "学习率调整策略",
        "name": "lr_scheduler_type",
        "recommendValue": "linear",
        "description": "选择不同的学习率策略，动态地改变模型在训练过程中更新权重时所采用的学习率大小",
        "supportValues": [
          "linear",
          "cosine",
          "cosine_with_restarts",
          "polynomial",
          "constant",
          "constant_with_warmup",
          "inverse_sqrt",
          "reduce_lr_on_plateau"
        ],
        "step": 0,
        "type": "select"
      },
      {
        "isAdvancedParameter": true,
        "displayName": "开启训练加速",
        "name": "enable_torchacc",
        "recommendValue": true,
        "description": "开启训练加速",
        "step": 0,
        "type": "switch"
      },
      {
        "leftRange": "include",
        "isAdvancedParameter": true,
        "displayName": "验证步数",
        "name": "eval_steps",
        "recommendValue": 50,
        "description": "训练阶段针模型的验证间隔步长，用于阶段性评估模型训练准确率、训练损失",
        "range": [
          1,
          2147483647
        ],
        "step": 0,
        "rightRange": "include",
        "type": "intInputNumber"
      },
      {
        "leftRange": "include",
        "isAdvancedParameter": true,
        "displayName": "序列长度",
        "name": "sequence_length",
        "recommendValue": 8192,
        "description": "训练数据的序列长度，单个训练数据样本的最大长度，超出配置长度将自动截断",
        "range": [
          500,
          8192
        ],
        "step": 0,
        "rightRange": "include",
        "type": "intInputNumber"
      },
      {
        "leftRange": "exclude",
        "isAdvancedParameter": true,
        "displayName": "学习率预热比例",
        "name": "lr_warmup_ratio",
        "recommendValue": 0.05,
        "description": "warmup占用总的训练steps的比例",
        "range": [
          0,
          1
        ],
        "step": 0,
        "rightRange": "exclude",
        "type": "floatInputNumber"
      },
      {
        "leftRange": "exclude",
        "isAdvancedParameter": true,
        "displayName": "权重衰减",
        "name": "weight_decay",
        "recommendValue": 0.01,
        "description": "L2正则化，让权重衰减到更小的值，在一定程度上减少模型过拟合的问题",
        "range": [
          0,
          0.2
        ],
        "step": 0,
        "rightRange": "exclude",
        "type": "floatInputNumber"
      }
    ],
    "sft_lora": [{
        "name": "useQwenMixedStrategy",
        "recommendValue": false,
        "step": 0
      },
      {
        "leftRange": "include",
        "isAdvancedParameter": false,
        "displayName": "循环次数",
        "name": "n_epochs",
        "recommendValue": 3,
        "description": "循环次数代表模型训练过程中模型学习数据集的次数，可理解为看几遍数据，一般建议的范围是1-3遍即可，可依据需求进行调整",
        "range": [
          1,
          200
        ],
        "step": 1,
        "rightRange": "include",
        "type": "number"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "学习率",
        "name": "learning_rate",
        "recommendValue": "3e-4",
        "description": "学习率代表每次更新数据的增量参数权重，学习率数值越大参数变化越大，对模型影响越大",
        "step": 0,
        "type": "input"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "批次大小",
        "name": "batch_size",
        "recommendValue": 16,
        "description": "批次大小代表模型训练过程中，模型更新模型参数的数据步长，可理解为模型每看多少数据即更新一次模型参数，一般建议的批次大小为16/32，表示模型每看16或32条数据即更新一次参数",
        "supportValues": [
          8,
          16,
          32
        ],
        "step": 0,
        "type": "select"
      },
      {
        "isAdvancedParameter": true,
        "displayName": "学习率调整策略",
        "name": "lr_scheduler_type",
        "recommendValue": "linear",
        "description": "选择不同的学习率策略，动态地改变模型在训练过程中更新权重时所采用的学习率大小",
        "supportValues": [
          "linear",
          "cosine",
          "cosine_with_restarts",
          "polynomial",
          "constant",
          "constant_with_warmup",
          "inverse_sqrt",
          "reduce_lr_on_plateau"
        ],
        "step": 0,
        "type": "select"
      },
      {
        "leftRange": "include",
        "isAdvancedParameter": true,
        "displayName": "验证步数",
        "name": "eval_steps",
        "recommendValue": 50,
        "description": "训练阶段针模型的验证间隔步长，用于阶段性评估模型训练准确率、训练损失",
        "range": [
          1,
          2147483647
        ],
        "step": 0,
        "rightRange": "include",
        "type": "intInputNumber"
      },
      {
        "leftRange": "include",
        "isAdvancedParameter": true,
        "displayName": "序列长度",
        "name": "sequence_length",
        "recommendValue": 8192,
        "description": "训练数据的序列长度，单个训练数据样本的最大长度，超出配置长度将自动截断",
        "range": [
          500,
          8192
        ],
        "step": 0,
        "rightRange": "include",
        "type": "intInputNumber"
      },
      {
        "leftRange": "exclude",
        "isAdvancedParameter": true,
        "displayName": "学习率预热比例",
        "name": "lr_warmup_ratio",
        "recommendValue": 0.05,
        "description": "warmup占用总的训练steps的比例",
        "range": [
          0,
          1
        ],
        "step": 0,
        "rightRange": "exclude",
        "type": "floatInputNumber"
      },
      {
        "leftRange": "exclude",
        "isAdvancedParameter": true,
        "displayName": "权重衰减",
        "name": "weight_decay",
        "recommendValue": 0.01,
        "description": "L2正则化，让权重衰减到更小的值，在一定程度上减少模型过拟合的问题",
        "range": [
          0,
          0.2
        ],
        "step": 0,
        "rightRange": "exclude",
        "type": "floatInputNumber"
      },
      {
        "isAdvancedParameter": true,
        "displayName": "LoRa秩值",
        "name": "lora_rank",
        "recommendValue": 8,
        "description": "LoRa训练中的秩大小，影响LoRa训练中自身数据对模型作用程度，秩越大作用越大，需要依据数据量选择合适的秩",
        "supportValues": [
          8,
          16,
          32,
          64
        ],
        "step": 0,
        "type": "select"
      },
      {
        "isAdvancedParameter": true,
        "displayName": "LoRa阿尔法",
        "name": "lora_alpha",
        "recommendValue": 32,
        "description": "LoRa训练中的缩放系数，用于调整初始化训练权重，使其与预训练权重接近或保持一致",
        "supportValues": [
          8,
          16,
          32,
          64
        ],
        "step": 0,
        "type": "select"
      },
      {
        "leftRange": "exclude",
        "isAdvancedParameter": true,
        "displayName": "LoRa丢弃率",
        "name": "lora_dropout",
        "recommendValue": 0.1,
        "description": "配置训练过程中随机丢弃或忽略神经元的比率，防止过拟合，提高模型泛化能力",
        "range": [
          0,
          0.2
        ],
        "step": 0,
        "rightRange": "exclude",
        "type": "floatInputNumber"
      },
      {
        "isAdvancedParameter": true,
        "displayName": "LoRa目标模块",
        "name": "lora_target_modules",
        "recommendValue": "ALL",
        "description": "选择模型的全部或特定模块层进行微调优化，设定ALL是对所有的linear(qkvo, mlp)加上lora，设定AUTO表示查找MODEL_MAPPING中的lora_target_modules(默认指定为qkv)",
        "supportValues": [
          "ALL",
          "AUTO"
        ],
        "step": 0,
        "type": "select"
      }
    ],
    "dpo_lora": [{
        "leftRange": "include",
        "isAdvancedParameter": false,
        "displayName": "循环次数",
        "name": "n_epochs",
        "recommendValue": 3,
        "description": "循环次数代表模型训练过程中模型学习数据集的次数，可理解为看几遍数据，一般建议的范围是1-3遍即可，可依据需求进行调整",
        "range": [
          1,
          200
        ],
        "step": 1,
        "rightRange": "include",
        "type": "number"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "学习率",
        "name": "learning_rate",
        "recommendValue": "3e-4",
        "description": "学习率代表每次更新数据的增量参数权重，学习率数值越大参数变化越大，对模型影响越大",
        "step": 0,
        "type": "input"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "批次大小",
        "name": "batch_size",
        "recommendValue": 16,
        "description": "批次大小代表模型训练过程中，模型更新模型参数的数据步长，可理解为模型每看多少数据即更新一次模型参数，一般建议的批次大小为16/32，表示模型每看16或32条数据即更新一次参数",
        "supportValues": [
          8,
          16,
          32
        ],
        "step": 0,
        "type": "select"
      }
    ],
    "dpo_full": [{
        "leftRange": "include",
        "isAdvancedParameter": false,
        "displayName": "循环次数",
        "name": "n_epochs",
        "recommendValue": 3,
        "description": "循环次数代表模型训练过程中模型学习数据集的次数，可理解为看几遍数据，一般建议的范围是1-3遍即可，可依据需求进行调整",
        "range": [
          1,
          200
        ],
        "step": 1,
        "rightRange": "include",
        "type": "number"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "学习率",
        "name": "learning_rate",
        "recommendValue": "1e-5",
        "description": "学习率代表每次更新数据的增量参数权重，学习率数值越大参数变化越大，对模型影响越大",
        "step": 0,
        "type": "input"
      },
      {
        "isAdvancedParameter": false,
        "displayName": "批次大小",
        "name": "batch_size",
        "recommendValue": 16,
        "description": "批次大小代表模型训练过程中，模型更新模型参数的数据步长，可理解为模型每看多少数据即更新一次模型参数，一般建议的批次大小为16/32，表示模型每看16或32条数据即更新一次参数",
        "supportValues": [
          8,
          16,
          32
        ],
        "step": 0,
        "type": "select"
      }
    ]
  },
  "modelCodeForQueryPrice": "qwen2-7b-instruct",
  "modelTypeEnum": "BM",
  "supportEval": false,
  "modelKey": "qwen",
  "ancestorModelId": "qwen2-7b-instruct",
  "modelName": "通义千问2-开源版-7B",
  "trainingTypes": [
    "sft_lora",
    "sft_full",
    "dpo_lora",
    "dpo_full"
  ],
  "deleted": false,
  "modelKeyDesc": "qwen1.5",
  "modelCode": "qwen2-7b-instruct",
  "supportApp": false,
  "lackDeployQuota": false,
  "supportExperience": false,
  "applyStatus": 0
}]