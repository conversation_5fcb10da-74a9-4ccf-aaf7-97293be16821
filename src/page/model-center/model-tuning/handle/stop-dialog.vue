<template>
  <el-dialog title="终止任务" :visible.sync="state" :before-close="handleClose" :close-on-click-modal="false" width="400px"
    center append-to-body>
    <AlarmPrompt pre_message="确认终止任务" :resource_name="row_data ? row_data.model_id : ''" after_message="吗？">
    </AlarmPrompt>
    <div slot="footer" class="dialog-footer">
      <cl-button type="primary" @click="confirm()" :loading="loading">
        <span v-if="!loading">确定</span>
        <span v-else>终止中...</span>
      </cl-button>
      <cl-button @click="handleClose">取消</cl-button>
    </div>
  </el-dialog>
</template>

<script>
  import {
    terminate_training_job
  } from "@/http/model-http/model-http";
  export default {
    data() {
      return {
        state: false,
        row_data: null,
        loading: false,
        rules: {
          name: [{
              required: true,
              message: '请输入名称',
              trigger: 'blur'
            },
            {
              validator: nameTestRule
            }
          ]
        }
      }
    },
    methods: {
      open(data) {
        if (data) {
          this.row_data = data
        }
        this.state = true
      },
      confirm() {
        this.loading = true
        terminate_training_job(this.row_data.id).then((res) => {
          this.$handle_http_back(res, false, false)
          this.loading = false
          this.$emit('ok')
          this.state = false
        }).catch((err) => {
          console.error(err)
          this.loading = false
        })
      },
      handleClose() {
        this.state = false
      }
    }
  }

</script>

<style>

</style>
