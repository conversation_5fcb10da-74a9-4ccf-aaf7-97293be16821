export default function (vueApp) {
  let $store = vueApp.$store;
  let $route = vueApp.$route;
  let $router = vueApp.$router;
  let $moment = vueApp.$moment;

  let {operateStrategy, typeFilter,taskTypeFilter, canSelectable } = vueApp;
  let {dateformat } = vueApp.$options.filters;
  return [
    {
      type: "selection",
      selectable: canSelectable,
      width: "30",
    },
    {
      "label": "序号",
      "type": "index",
    },
    {
      "label": "模型名称",
      "render": (h, scope) => { return <span>{scope.row.model_id}</span> }
    },
    {
      "label": "任务类型",
      "render": (h, scope) => { return <span>{taskTypeFilter(scope.row.training_type)}</span> }
    },
    {
      "label": "调优任务code",
      "render": (h, scope) => { return <span>
        <contentCopy value={scope.row.optimization_code} style={{'margin-right': '5px'}} tip="复制调优任务code"/>
        {scope.row.optimization_code}
        </span>
      }
    },
    {
      "label": "基础模型",
      "render": (h, scope) => { return <span>{scope.row.foundation_model_id}</span> }
    },
    {
      "label": "训练状态",
      "render": (h, scope) => { return <span>
        <stateClassification
            type={typeFilter(scope.row.training_status)}
            value={scope.row.training_status}
          ></stateClassification></span> }
    },
    {
      "label": "训练Token量",
      "render": (h, scope) => { return <span>{scope.row.token_count}</span> }
    },
    {
      "label": "创建时间",
      "render": (h, scope) => { return <span>{dateformat(scope.row.created_at)}</span> }
    },
    {
      "label": "创建人",
      "render": (h, scope) => { return <span>{scope.row.user_name?scope.row.user_name:'--'}</span> }
    },
    {
      "label": "操作",
      "width": "60",
      "align": "center",
      "render": (h, scope) => {
        return <el-dropdown size="small" placement="bottom" trigger="click" >
          <cl-button icon="el-icon-setting" title="操作" ></cl-button>
          <el-dropdown-menu slot="dropdown" >
            <el-dropdown-item onClick={() => operateStrategy(scope.row, 'delDialog')} disabled={scope.row.training_status!=='训练成功'&&scope.row.training_status!=='训练失败'&&scope.row.training_status!=='已终止'}>
              删除
            </el-dropdown-item>
            <el-dropdown-item onClick={() => operateStrategy(scope.row, 'stopTask')} disabled={scope.row.training_status!=='训练中'}>
              终止训练
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      },
      "resizable": false
    }
  ]
}
