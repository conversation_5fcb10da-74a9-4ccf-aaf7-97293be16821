export default function (vueApp) {
  let $store = vueApp.$store;
  let $route = vueApp.$route;
  let $router = vueApp.$router;
  let $moment = vueApp.$moment;

  let { goto, operateStrategy } = vueApp;
  let { dateformat } = vueApp.$options.filters;
  return [
    {
      type: "selection",
      width: "30",
    },
    {
      "label": "模型名称",
      prop: 'model_id',
      "render": (h, scope) => {
        return <span>
        <contentCopy value={scope.row.model_id} tip="复制模型名称" style={{'margin-right': '5px'}}/>
        <span>{scope.row.model_id}</span>
      </span>
      }
    },
    {
      "label": "基础模型",
      prop: 'foundation_model_id',
      "render": (h, scope) => { return <span>{scope.row.foundation_model_id}</span> }
    },
    // {
    //   "label": "模型参数",
    //   prop: 'model_args',
    //   "render": (h, scope) => { return <span>{scope.row.model_args}</span> }
    // },
    {
      "label": "模型来源",
      prop: 'original_model',
      "render": (h, scope) => { return <span>{scope.row.original_model}</span> }
    },
    {
      "label": "创建时间",
      prop: 'create_time',
      "render": (h, scope) => { return <span>{dateformat(scope.row.create_time)}</span> }
    },
    {
      "label": "创建人",
      "render": (h, scope) => { return <span>{scope.row.user_name?scope.row.user_name:'--'}</span> }
    },
    {
      "label": "操作",
      "width": "60",
      "align": "center",
      "render": (h, scope) => {
        return <el-dropdown size="small" placement="bottom" trigger="click" >
          <cl-button icon="el-icon-setting" title="操作" ></cl-button>
          <el-dropdown-menu slot="dropdown" >
            <el-dropdown-item onClick={() => goto(`/modelDeployment/deployment?model_id=${scope.row.model_id}`)} >
              部署
            </el-dropdown-item>
            <el-dropdown-item onClick={() => goto('TuningNewModel', scope.row)} >
              增量训练
            </el-dropdown-item>
            <el-dropdown-item onClick={() => operateStrategy(scope.row, 'delDialog')} >
              删除
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      },
      "resizable": false
    }
  ]
}
