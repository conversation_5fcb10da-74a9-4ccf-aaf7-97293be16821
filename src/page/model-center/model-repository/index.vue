<template>
	<cl-page-container title="模型仓库" explain="模型仓库" :hideHead="true">
		<bread name="模型仓库" description="展示所有调优产出的模型，用户可以管理调优产出的模型，支持模型部署及增量训练。"></bread>
		<cl-table-container name="compare-data-grop" model="flex">
			<cl-table-header
				ref="my_header"
				inputWidth="208px"
				placeholder="搜索（模型名称）"
				@search="table_search"
				:table_loading="table_loading"
				:page_num="page_num"
				:page_size="page_size"
				:rightHandles="rightHandles"
			>
				<template slot="left">
					<div class="table-tool-bar-left">
						<cl-button
							type="danger"
							icon="el-icon-delete"
							:disabled="table_select.length === 0"
							@click="operateStrategy('', 'delBatchDialog')"
						>
							批量删除
						</cl-button>
					</div>
				</template>
			</cl-table-header>
			<cl-table-body>
				<cl-table
					element-loading-text="加载中..."
					:data="table_list"
					:ref="`all_model_repository`"
					@selection-change="selectionChange"
					:columns="tableColunms"
					@no-data="noData"
				></cl-table>
			</cl-table-body>
			<cl-table-footer>
				<cl-pagination
					background
					v-if="table_list.length != 0"
					@size-change="handleListSizeChange"
					@current-change="handleListCurrentChange"
					:current-page="page_num"
					:page-sizes="[10, 20, 50, 100]"
					:page-size="page_size"
					layout="sizes, total, jumper, next, pager, prev"
					:total="page_total"
				>
				</cl-pagination>
			</cl-table-footer>
		</cl-table-container>
		<sugon-delete-dialog
			:list="table_select"
			:isShow.sync="dialogActionDelete"
			@confirm="delTableChoose"
			:title="`${table_select && table_select.length > 1 ? '删除' : '删除'}`"
			nameKey="model_id"
			idKey="model_id"
		>
		</sugon-delete-dialog>
	</cl-page-container>
</template>
<script>
import * as modelHttp from "@/http/model-http/model-http";
import bread from '@/components/bread/index.vue';
import coljs from './table_col.js';
export default {
	name: 'modelRepository',
	components: {bread},
	data () {
		return {
			search_value: "",
			tabActiveName: "all",
			table_list: [],
			page_size: 10,
			page_num: 1,
			page_total: 0,
			table_loading: false,
			table_select: [],
			tableColunms: [],
			project_id: localStorage.getItem("ProjectId") || '',
			dialogActionDelete: false,
			rightHandles: [
        'setBtn',
        () => {
          return <el-select class="header-right-selset-sps"
					 	value={this.tabActiveName} placeholder="请选择模型类型"
						style="width: 140px;margin-right: 8px" 
						onChange={this.handleClick}>
						<el-option label='全部' value='all'></el-option>
						<el-option label='我创建的' value='mine'></el-option>
          </el-select>
        },
        'input',
				() => <cl-button onClick={this.reset}>重置</cl-button>,
				'search'],
		}
	},
	mounted () {
		this.getList();
		this.tableColunms = coljs(this)
	},
	methods: {
		reset() {
      this.tabActiveName = 'all'
      this.$refs.my_header.input_value('')
      this.search_value = ''
      this.getList()
    },
		typeFilter: function (val) {
			return val === '训练成功' ? 'success' : val === '训练失败' ? 'error' : 'warning'
		},
		table_search (res) {
			this.search_value = res.value
			this.page_num = res.page_num
			this.page_size = res.page_size
			this.getList()
		},
		noData () {
			if (this.page_num > 1) {
				this.page_num = this.page_num - 1
				this.getList()
			}
		},
		getList () {
			let params = {
        model_id: this.search_value,
				page_num: this.page_num,
				page_size: this.page_size,
				project_id: this.project_id
			};
			if (this.tabActiveName == 'mine') {
				params.user_id = GetUserInfo('userId')
			}
			this.table_loading = true;
			modelHttp
				.get_model_repository(params)
				.then((res) => {
					this.$handle_http_back(res, true, false)
					if (res.content) {
						this.page_total = res.content.total;
						this.table_list = res.content.list;
						this.table_loading = false;
					}
				})
				.catch((_) => {
					this.table_loading = false;
				});
		},
		handleClick (val) {
			this.tabActiveName = val
			this.page_num = 1;
			this.page_total = 0;
			this.page_size = 10;
			this.table_list = [];
			this.table_select = []
			this.getList();
		},
		handleListSizeChange (page_size) {
			this.page_size = page_size;
			/**每页条数发生变化 */
			this.getList();
		},
		handleListCurrentChange (pageIndex) {
			/**当前页发生变化 */
			this.page_num = pageIndex;
			this.getList();
		},
		selectionChange (row) {
			this.table_select = row;
		},
		operateStrategy (item, data) {
			switch (data) {
				case 'delDialog':
					this.table_loading = false
					this.table_select = []
					this.table_select.push(item)
					this.$table_select_repeat(this.table_select, this.table_list, 'all_model_repository')
					this.dialogActionDelete = true
					break
				case 'delBatchDialog':
					this.dialogActionDelete = true
					this.table_loading = false
					break
				default:
					break
			}
		},
		delTableChoose (response) {
			let value = response.data.map((el) => {
				return {
					name: el.name,
					params: el.id,
					request: modelHttp.del_model_repository_by_id
				};
			});
			const that = this;
			const batch_delete = new this.$batch_delete({
				data: value,
				title: "删除提示",
				success: function () {
					that.dialogActionDelete = false;
					that.getList();
				},
				error: function (err) {
					that.dialogActionDelete = false;
					//that.getList();
				},
			});
			/**发送请求 */
			batch_delete.del();
		},
		goto (link, row) {
      if (row) {
        this.$router.push({
          name: link,
          params: row
        })
      } else {
        this.$router.push(link)
      }
			this.$nextTick(_ => {
				AppMenu.setActive(link)
			})
		}
	},
	destroyed () {
	},
};
</script>
<style lang="scss" scoped>
>>> .header-right-selset-sps  .el-input {
	width: 140px !important;
}
</style>
