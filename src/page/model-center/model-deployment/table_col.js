export default function (vueApp) {
  let $store = vueApp.$store;
  let $route = vueApp.$route;
  let $router = vueApp.$router;
  let $moment = vueApp.$moment;

  let {goto, operateStrategy, typeFilter } = vueApp;
  let {dateformat } = vueApp.$options.filters;
  return [
    {
      "label": "模型名称",
      "render": (h, scope) => { return <span>{scope.row.model_id}</span> }
    },
    {
      "label": "部署id",
      "render": (h, scope) => { return <span>{scope.row.id}</span> }
    },
    {
      "label": "所属集群",
      "render": (h, scope) => { return <span>{scope.row.cluster_name ? scope.row.cluster_name : '--'}</span> }
    },
    {
      "label": "部署详情",
      "render": (h, scope) => {
        return <span>
          实例数量: {scope.row.instance_num}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          规格: {scope.row.compute_info ? scope.row.compute_info: '--'}
          </span>
          }
    },
    {
      "label": "状态",
      "render": (h, scope) => { return scope.row.status ? <span>
        <stateClassification
            type={typeFilter(scope.row.status)}
            value={scope.row.status}
          ></stateClassification></span>
        : null }
    },
    {
      "label": "创建时间",
      "render": (h, scope) => { return <span>{dateformat(scope.row.created_at)}</span> }
    },
    {
      "label": "创建人",
      "render": (h, scope) => { return <span>{scope.row.user_name?scope.row.user_name:'--'}</span> }
    },
    {
      "label": "操作",
      "width": "60",
      "align": "center",
      "render": (h, scope) => {
        return <el-dropdown size="small" placement="bottom" trigger="click" >
          <cl-button icon="el-icon-setting" title="操作" ></cl-button>
          <el-dropdown-menu slot="dropdown" >
            {
              scope.row.model_type == 'TG' || scope.row.model_type == 'IU' ? <el-dropdown-item onClick={() => goto(scope.row)} disabled={scope.row.status!=='运行中'} >
                去体验
              </el-dropdown-item>
              : null
            }
            <el-dropdown-item onClick={() => operateStrategy(scope.row, 'offline')} disabled={scope.row.status=='下线中'}>
              下线
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      },
      "resizable": false
    }
  ]
}
