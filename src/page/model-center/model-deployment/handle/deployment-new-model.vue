<template>
  <div class="model-deployment">
    <sugon-back-button name="部署新模型" v-if="!model"></sugon-back-button>
    <div style="display: flex;flex-direction: column;height: calc(100% - 50px);">
      <div class="model-deployment-container">
        <el-form ref="form" :model="form" :rules="rules" label-position="left" label-width="130px">
          <div class="form-title" v-if="!model" style="margin-top: 0;">
            选择模型
          </div>
          <el-form-item label="选择集群" prop="cluster_id" v-if="!model">
            <el-select style="width: 400px;" placeholder="请选择集群" v-model="form.cluster_id"
              @change="handelChangeCluster">
              <el-option :label="item.name" :value="item.id" v-for="(item, index) of cluster_list"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="" v-if="!model" :rules="{ required: true }" style="margin-bottom: 0;">
            <span slot="label">
              选择模型
              <el-tooltip placement="top-start">
                <div slot="content">
                  选择模型进行部署，平台支持部署系统预置模型或训练完成待部署的模型，选择模型类型后可从下拉列表中选择合适的模型进行部署
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </span>
            <el-radio-group v-model="radio" @change="handelChangeType">
              <el-radio label="custom">自定义模型</el-radio>
              <el-radio label="preset" :disabled="form.cluster_id == 'default-cluster'">预置模型</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" prop="model" v-if="!model && radio == 'custom'">
            <div style="margin-top: 8px;">
              <el-select style="width: 400px;" placeholder="请选择模型" v-model="form.model" @change="handelChangeModel">
                <el-option :label="item.model_id" :value="item.model_id" v-for="(item, index) of model_list"
                  :key="index"></el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="" prop="preset_model" v-if="!model && radio == 'preset'">
            <div style="margin-top: 8px;">
              <el-select style="width: 400px;" placeholder="请选择模型" v-model="form.preset_model"
                @change="handelChangePresetModel">
                <el-option :label="item.model_id" :value="item.model_id" v-for="(item, index) of preset_model_list"
                  :key="index"></el-option>
              </el-select>
            </div>
          </el-form-item>
          <div class="form-title" v-if="!model">资源配置</div>

          <el-form-item label="算力类型" prop="supportGpu"
            v-if='(model && (model.model_type == "EMB" || model.model_type == "RK")) || 
            (choose_model&&(choose_model.model_type == "EMB" || choose_model.model_type == "RK")) || 
            (preset_choose_model&&(preset_choose_model.model_type == "EMB" || preset_choose_model.model_type == "RK"))'>
            <el-radio-group v-model="form.supportGpu" class="input-lenght">
              <el-radio :label="false">CPU</el-radio>
              <el-radio :label="true">加速卡</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="规格" prop="flavor"
            v-if='(!model && form.supportGpu) || (model && (model.model_type == "EMB" || model.model_type == "RK") && form.supportGpu) || (model && (model.model_type == "TG" || model.model_type == "OMNI" || model.model_type == "IU"))'>
            <el-select style="width: 400px;" placeholder="请选择规格" v-model="form.flavor" @change="handelChangeFlavor">
              <el-option :label="item.code.split('_')[0] +
                '-' +
                item.code.split('_')[1] +
                '-' +
                item.code.split('_')[2]
                " :value="item.code" v-for="(item, index) of flavor_list" :key="index">
                {{ item.code.split("_")[0] }}-{{ item.code.split("_")[1] }}-{{
                  item.code.split("_")[2]
                }}（最大可部署实例数：{{ item.number }}）
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="实例数" prop="instance_num">
            <el-input-number style="width: 200px;" :min="1" :max="max_available" placeholder="请输入实例数"
              v-model="form.instance_num"></el-input-number>
          </el-form-item>
        </el-form>
      </div>
      <div class="deployment-footer-btn" v-if="!model">
        <cl-button type="primary" @click="submitForm('form')">开始部署</cl-button>
        <cl-button @click="
          () => {
            $router.push('/modelDeployment');
          }
        ">取消</cl-button>
      </div>
    </div>
  </div>
</template>
<script>
import {
  get_model_repository,
  get_cluster_list,
  get_available_replicas,
  post_deployment,
  get_model
} from "@/http/model-http/model-http";
export default {
  props: ["model"],
  data() {
    return {
      radio: 'custom',
      form: {
        model: "",
        preset_model: '',
        instance_num: 1,
        flavor: "",
        cluster_id: "",
        supportGpu: true
      },
      choose_flavor: {},
      choose_model: {},
      preset_choose_model: {},
      max_available: 1,
      project_id: "",
      model_list: [],
      flavor_list: [],
      cluster_list: [],
      preset_model_list: [],
      rules: {
        model: [
          {
            required: true,
            message: "请选择模型",
            trigger: "blur"
          }
        ],
        supportGpu:[
          {
            required: true,
            message: "请选择算力类型",
            trigger: "blur"
          }
        ],
        flavor: [
          {
            required: true,
            message: "请选择规格",
            trigger: "blur"
          }
        ],
        cluster_id: [
          {
            required: true,
            message: "请选择集群",
            trigger: "blur"
          }
        ],
        instance_num: [
          {
            required: true,
            message: "请输入实例数",
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    if (this.model) {
      console.log(this.model, 'model');
      this.radio = 'preset'
      this.form.model = this.model.model_id;
      this.preset_choose_model = this.model;
      this.form.cluster_id = "default-cluster";
      this.getAvailableRreplicas();
    }
    this.project_id = this.$get_projectId();
    this.getModelList();
    this.getClusterList();
    // this.getPresetModel()
  },
  methods: {
    handelChangeType(val) {
      this.form.model = ''
      this.form.preset_model = ''
      this.form.flavor = ''
      if (val == 'custom') {
        this.getModelList()
      } else {
        this.getPresetModel()
      }
    },
    go_back() {
      this.$back_list(this);
    },
    handelChangeModel(val) {
      this.flavor_list = [];
      this.form.flavor = "";
      this.choose_model = this.model_list.find(item => item.model_id === val);
      this.getAvailableRreplicas();
    },
    handelChangePresetModel(val) {
      this.flavor_list = [];
      this.form.flavor = "";
      this.preset_choose_model = this.preset_model_list.find(item => item.model_id === val);
      this.getAvailableRreplicas();
    },
    handelChangeCluster(val) {
      console.log(val);

      if (val == 'default-cluster') {
        console.log('in');

        this.radio = 'custom'
        this.form.preset_model = ''
        if (this.$route.query.model_id) {
          this.form.model = this.$route.query.model_id
        }

      }
      this.getAvailableRreplicas();
    },
    handelChangeFlavor(val) {
      this.choose_flavor = this.flavor_list.find(item => item.code === val);
      this.max_available = this.choose_flavor.number;
      if (this.model) {
        this.max_available < 1
          ? this.$emit("max_available", false)
          : this.$emit("max_available", true);
      }
      console.log(this.choose_flavor);
    },
    getAvailableRreplicas() {
      if (!this.form.cluster_id || (!this.form.model && !this.form.preset_model)) {
        return;
      } else {
        let params = {
          foundation_model: this.model
            ? this.model.model_id
            : this.radio == 'custom' ? this.choose_model.foundation_model_id : this.preset_choose_model.model_id,
          cluster_id: this.form.cluster_id
        };
        get_available_replicas(params)
          .then(res => {
            this.$handle_http_back(res, true, false);
            if (res.content) {
              this.flavor_list = res.content;
            }
          })
          .catch(_ => { });
      }
    },
    submitForm(handle) {
      if (this.max_available < 1) {
        return this.$message.error("最大可部署实例数为0，请重新选择");
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          let data = {};
          data.compute_info = this.form.supportGpu ? this.form.flavor : 'CPU';
          data.model_id = this.radio == 'preset' ? this.preset_choose_model.model_id : this.choose_model.model_id;
          data.instance_num = this.form.instance_num;
          data.user_id = GetUserInfo("userId");
          data.project_id = this.project_id;
          data.cluster_id = this.form.cluster_id;
          data.model_source = this.model ? "source_share" : "source_private";
          data.support_gpu = this.form.supportGpu
          post_deployment(data)
            .then(data => {
              this.$handle_http_back(data, false, false)
                .then(res => {
                  if (handle && handle.ok) {
                    handle.ok();
                  }
                  this.$router.push("/modelDeployment");
                })
                .catch(() => {
                  if (handle && handle.err) {
                    handle.err();
                  }
                });
            })
            .catch(() => {
              if (handle && handle.err) {
                handle.err();
              }
            });
        }
      });
    },
    getClusterList() {
      let project_id = localStorage.getItem("ProjectId") || "";
      get_cluster_list(project_id)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.cluster_list = res.content;
          }
        })
        .catch(_ => { });
    },
    getModelList() {
      let params = {
        page_num: 1,
        page_size: 9999
      };
      get_model_repository(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.model_list = res.content.list;
            if (this.$route.query && this.$route.query.model_id) {
              this.form.model = this.$route.query.model_id;
              this.choose_model = this.model_list.find(
                item => item.model_id === this.$route.query.model_id
              );
              this.getAvailableRreplicas();
            }
          }
        })
        .catch(_ => { });
    },
    getPresetModel() {
      get_model({
        page_num: 1,
        page_size: 10000,
      }).then(data => {
        this.$handle_http_back(data, true, false).then(res => {
          this.preset_model_list = res.content.list || []
        }).catch(_ => {
          this.preset_model_list = []
        })
      }).catch(_ => {
      })
    }
  }
};
</script>
<style scoped lang="scss">
.model-deployment {
  width: 100%;
  height: 100%;
}

.model-deployment-container {
  height: calc(100% - 50px);
  padding: 20px;
  background: #fff;
}

.form-title {
  font-size: 15px;
  font-weight: 700;
  margin: 15px 0;
}

.deployment-footer-btn {
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 80px;
  border-top: 1px solid #ebeef5;
  padding-left: 24px;
}
</style>
