<template>
  <cl-page-container title="模型部署" explain="模型部署" :hideHead="true">
    <bread name="模型部署" description="支持用户对自定义模型的部署，部署成功后提供可调用的服务。"></bread>
    <cl-table-container name="compare-data-grop" model="flex">
      <cl-table-header placeholder="搜索（模型名称）" @search="table_search" :rightHandles="rightHandles" :table_loading="table_loading" :page_num="page_num" :page_size="page_size">
        <template slot="left">
          <div class="table-tool-bar-left">
            <cl-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="operateStrategy('', 'deployment')"
                >
                  部署新模型
                </cl-button>
          </div>
        </template>
      </cl-table-header>
      <cl-table-body>
        <cl-table element-loading-text="加载中..." :data="table_list" :ref="`all_model_repository`" @selection-change="selectionChange" :columns="tableColunms" @no-data="noData" ></cl-table>
      </cl-table-body>
      <cl-table-footer>
        <cl-pagination background v-if="table_list.length != 0" @size-change="handleListSizeChange"
            @current-change="handleListCurrentChange" :current-page="page_num"
            :page-sizes="[10, 20, 50, 100]" :page-size="page_size"
            layout="sizes, total, jumper, next, pager, prev" :total="page_total">
          </cl-pagination>
      </cl-table-footer>
    </cl-table-container>
    <stopDialog ref="stopDialog" @ok="getList"/>
    <sugon-delete-dialog
      :list="table_select"
      :isShow.sync="dialogDel"
      @confirm="delTableChoose"
      :title="`${table_select && table_select.length > 1 ? '删除' : '删除'}`"
      nameKey="model_id"
      idKey="id"
    >
    </sugon-delete-dialog>
  </cl-page-container>
</template>
<script>
import * as modelHttp from "@/http/model-http/model-http";
import stopDialog from './handle/stop-dialog';
import coljs from './table_col.js';
import bread from '@/components/bread/index.vue';
import {del_deployment} from "../../../http/model-http/model-http";
export default {
  name:'modelDeployment',
  components: {stopDialog, bread},
  data() {
    return {
      search_value: "",
      tabActiveName: "all",
      table_list: [],
      page_size: 10,
      page_num: 1,
      page_total: 0,
      table_loading: false,
      table_select: [],
      tableColunms: [],
      dialogDel: false,
      project_id: localStorage.getItem("ProjectId") || '',
      searchItems: [
        {
          label: '模型名称',
          prop: 'model_name'
        },
        {
          label: '状态',
          prop: 'status'
        },
      ],
      status_list: [{
        key: '启动中',
        value: 0
      },{
        key: '运行中',
        value: 1
      },{
        key: '部署失败',
        value: 2
      },{
        key: '训练失败',
        value: 3
      },{
        key: '回滚中',
        value: 4
      }],
      searchKey: 'model_name',
      status: '',
      rightHandles: [
        'setBtn',
        () => {
          return <el-select class="header-right-selset-sps"
					 	value={this.tabActiveName} placeholder="请选择"
						style="width: 140px;margin-right: 8px" 
						onChange={this.handleClick}>
						<el-option label='全部' value='all'></el-option>
						<el-option label='我创建的' value='mine'></el-option>
          </el-select>
        },
        () => {
          return <el-select clearable class="header-right-selset-sps" value={this.status} placeholder="请选择状态" onChange={this.training_status_change}
                            style="width: 140px;margin-right: 10px;" >
            {this.status_list.map(item => <el-option label={item.name} value={item.code}></el-option>)}
          </el-select >
        },
        () => {
          return <el-input value={this.search_value} placeholder="搜索（模型名称）" onChange={this.getList} onInput={this.name_change} style="width: 208px;" />
        },
        () => <cl-button onClick={this.getList}>搜索</cl-button>,
        () => <cl-button onClick={this.reset}>重置</cl-button>, 'search'],
    }
  },
  mounted() {
    this.status_list = this.$getCode({type:'deploy_status'})
    this.getList();
    this.tableColunms = coljs(this)
  },
  methods: {
    reset() {
      this.tabActiveName = 'all'
      this.search_value = ''
      this.status = ''
      this.getList()
    },
    typeFilter: function (val) {
      return val === '运行中' ? 'success' : val === '部署中' ? 'warning' : 'error'
    },
    table_search(res){
      this.search_value = res.value
      this.page_num = res.page_num
      this.page_size = res.page_size
      this.getList()
    },
    noData(){
      if(this.page_num>1){
      this.page_num = this.page_num - 1
      this.getList()
      }
    },
    training_status_change(val) {
      this.status = val
      this.getList()
    },
    name_change(val) {
      this.search_value = val
    },
    getList() {
      let params = {
        page_num: this.page_num,
        page_size: this.page_size,
        project_id: this.project_id,
        status: this.status,
        model_name: this.search_value,
        user_id: this.tabActiveName === 'all' ? '' : GetUserInfo('userId'),
      };
      this.table_loading = true;
      modelHttp
        .get_deployment_list(params)
        .then((res) => {
          this.$handle_http_back(res,true,false)
          if (res.content) {
            this.page_total = res.content.total;
            this.table_list = res.content.list;
          }
          this.table_loading = false;
        })
        .catch((_) => {
          this.table_loading = false;
        });
    },
    handleClick(val) {
      this.tabActiveName = val
      this.page_num = 1;
      this.searchKey = 'model_name'
      this.search_value = ''
      this.page_total = 0;
      this.page_size = 10;
      this.table_list = [];
      this.table_select = []
      this.getList();
    },
    handleListSizeChange(page_size) {
      this.page_size = page_size;
      /**每页条数发生变化 */
      this.getList();
    },
    handleListCurrentChange(pageIndex) {
      /**当前页发生变化 */
      this.page_num = pageIndex;
      this.getList();
    },
    selectionChange(row) {
      this.table_select = row;
    },
    operateStrategy(item, data) {
      switch (data) {
        case 'delDialog':
          this.table_loading = false
          this.table_select = []
          this.table_select.push(item)
          this.$table_select_repeat(this.table_select, this.table_list, 'all_model_repository')
          this.dialogDel = true
          break
        case 'delBatchDialog':
          this.dialogDel = true
          this.table_loading = false
          break
        case 'deployment':
          this.$go_detail(this, `/modelDeployment/deployment`,
            {
              page_size: this.page_size,
              page_num: this.page_num,
            })
          break
        case 'offline':
          this.$refs.stopDialog.open(item)
          break
        default:
          break
      }
    },
    goto(row) {
      if (row.model_type == 'TG') {
        this.$router.push(`/modelExperience?name=${row.model_id}&model_id=${row.id}&mode_type=2`)
      }else if (row.model_type == 'IU') {
        this.$router.push(`/viewModelExperience?name=${row.model_id}&model_id=${row.id}&mode_type=2`)
      }
    },
    delTableChoose(response) {
      let value = response.data.map((el) => {
        return {
          name: el.name,
          params: el.id,
          request: modelHttp.del_deployment,
        };
      });
      const that = this;
      const batch_delete = new this.$batch_delete({
        data: value,
        title: "删除提示",
        success: function () {
          that.dialogDel = false;
          that.getList();
        },
        error: function (err) {
          that.dialogDel = false;
          that.getList();
        },
      });
      /**发送请求 */
      batch_delete.del();
    },
  },
  destroyed() {
  },
};
</script>
<style lang="scss" scoped>
>>> .el-tabs,.el-tabs--top {
  height: 100% !important;
}
>>> .el-tabs__content {
  height: calc(100% - 56px);
}
>>> .el-tab-pane {
  height: calc(100%);
}
>>>  .header-right-selset-sps .el-input{
  width: 140px !important;
}
</style>
