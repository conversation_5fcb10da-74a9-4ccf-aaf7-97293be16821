<template>
  <div class="monitor-data-picker">
    <div class="mdp-radio-group-area">
      <el-radio-group :value="radioValue" type="button">
        <el-radio-button v-for="radio in radioOpt" :key="radio.label" :label="radio.value" :value="radio.value"
          @click.native="_handleRadioValClick($event, radio)">{{ radio.label }}</el-radio-button>
      </el-radio-group>
    </div>
    <div class="mdp-date-picker-area">
      <el-select
        style="width: 195px;margin-bottom: 10px;"
        placeholder="请选择集群"
        v-model="clusterId"
        @change="handelChangeCluster"
      >
        <el-option
          :label="item.name"
          :value="item.id"
          v-for="(item, index) of cluster_list"
          :key="index"
        ></el-option>
      </el-select>
      <span>自定义时间段
        <el-tooltip class="item" effect="dark" content="自定义时间段最少选择最近10分钟内" placement="top">
        <i class="el-icon-question"></i>
      </el-tooltip>
        ：</span>
      <el-date-picker
        :name="pickerRefName"
        :id="pickerRefName"
        :ref="pickerRefName"
        split-panels
        :type="datePickerType"
        align="right"
        :value-format="datePicekerFormat"
        style="width: 370px"
        v-model="pickerValueClone"
        :clearable="false"
        :picker-options="defaultPickerOpt"
        @change="_handleDatePickerChange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
export default {
  props: {
    radioValue: {
      type: Number,
      required: true,
      default: () => {
        return -1
      }
    },
    radioOpt: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    },
    radioChange: {
      type: Function,
      default: () => { }
    },
    pickerValue: {
      type: [Array, String, Object],
      default: () => {
        return []
      }
    },
    datePickerType: {
      type: String,
      default: () => {
        return 'daterange'
      }
    },
    datePicekerFormat: {
      type: String,
      default: () => {
        return 'yyyy-MM-dd HH:mm:ss'
      }
    },
    pickerRefName: {
      type: String,
      default: () => {
        return 'monitorDatePicker'
      }
    },
    pickerChange: {
      type: Function,
      default: () => { }
    },
    cluster_list: {
      type: [Array],
      default: () => {
        return []
      }
    },
    clusterId: {
      type: String,
      default: () => {
        return 'all'
      }
    }
  },
  data() {
    return {
      defaultPickerOpt: {
        disabledDate: (data) => {
          const nowTime = new Date()
          return data < nowTime - 1000 * 60 * 60 * 24 * 30 || data > nowTime
        }
      }
    }
  },
  computed: {
    pickerValueClone: {
      get() {
        return this.pickerValue
      },
      set(val) {
        this.$emit('update:pickerValue', val)
      }
    }
  },
  methods: {
    _handleRadioValClick(e, val) {
      if (e.target.tagName === 'INPUT') return
      this.$emit('update:radioValue', val.value)
    },
    _handleDatePickerChange(val) {
      let oneMinTime = 10
      if (val) {
        oneMinTime = moment(val[1]).diff(moment(val[0]), 'minutes')
        if (oneMinTime < 10) {
          this.$message({
            message: '自定义时间段最少选择最近10分钟内',
            type: 'error'
          })
          const nowDate = new Date()
          const afterDate = moment(nowDate).format('YYYY-MM-DD HH:mm:ss')
          const beforeDate = moment(nowDate).subtract(10, 'minute').format('YYYY-MM-DD HH:mm:ss')
          val = [beforeDate, afterDate]
        }
      }
      this.$emit('update:pickerValue', val)
      typeof this.pickerChange === 'function' && this.pickerChange(oneMinTime >= 1 ? -999 : -1)
    },
    handelChangeCluster(val) {
      this.$emit('update:clusterId', val)
    }
  },
  created() {
  }
}
</script>

<style lang="scss" scoped>
.monitor-data-picker {
  position: relative;

  .mdp-radio-group-area {
    display: inline-block;
    position: relative;
    z-index: 99;
  }

  .mdp-date-picker-area {
    position: absolute;
    right: 0;
    top: 0;
  }

  .el-range__close-icon {
    display: none;
  }
}
</style>