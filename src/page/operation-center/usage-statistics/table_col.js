import { getSchemaType } from '@/utils'
export default function (vueApp) {
  const { gotoDetail, typeFilter } = vueApp;
  let { dateformat } = vueApp.$options.filters;
  return [
    {
      label: "模型名称",
      prop: "model_name",
      render: (h, scope) => {
        return <span>{scope.row.model_name}</span>;
      },
    },
    {
      label: "业务空间",
      prop: "version",
      render: (h, scope) => {
        return <span>默认业务空间</span>;
      },
    },
    {
      label: "调用次数",
      prop: "total_count ",
      render: (h, scope) => {
        return <span>{scope.row.total_count}</span>
      },
    },
    {
      label: "调用失败次数",
      prop: "fail",
      render: (h, scope) => {
        return <span>{scope.row.fail}</span>
      },
    },
    {
      label: "失败率",
      prop: "fail_rate ",
      render: (h, scope) => {
        return <span>{scope.row.total_count ? (scope.row.fail/scope.row.total_count*100).toFixed(2) : 0}%</span>
      },
    },
    {
      label: "调用量tokens",
      prop: "total_tokens ",
      render: (h, scope) => {
        return <span>{scope.row.total_tokens ? scope.row.total_tokens.toFixed(2) : 0} </span>
      },
    },
    {
      label: "输入量tokens",
      prop: "input_tokens ",
      render: (h, scope) => {
        return <span>{scope.row.input_tokens ? scope.row.input_tokens.toFixed(2) : 0} </span>
      },
    },
    {
      label: "输出量tokens",
      prop: "output_tokens ",
      render: (h, scope) => {
        return <span>{scope.row.output_tokens ? scope.row.output_tokens.toFixed(2) : 0} </span>
      },
    },
    // {
    //   label: "操作",
    //   prop: "user_name ",
    //   render: (h, scope) => {
    //     return <span>{scope.row.user_name}</span>
    //   },
    // },
  ];
}