<template>
  <div class="usage-container" style="height: 100%;">
    <div class="usage-container-header">
      <datePicker v-if="radioType" :radioValue.sync="radioType" :radioOpt="radioOpt" :clusterId.sync="clusterId"
        :pickerValue.sync="tmp_datePickerTime" pickerRefName="pageDatePicker" :pickerOptClickFn="_clickPickerLeft"
        :pickerChange="_radioPickerChange" :cluster_list="cluster_list"></datePicker>
    </div>
    <div v-loading="chart_loading"
      style="flex: 1;overflow-y: auto;display: flex;flex-direction: column;scrollbar-width: none;">
      <div class="usage-overview-container">
        <div class="usage-overview">
          <div class="usage-overview-item">
            <div class="usage-overview-item-title">模型总量</div>
            <div class="usage-overview-item-value">
              {{ (token_info && token_info.total) || (token_info && token_info.total == 0) ? token_info.total : '-' }}
              <span class="usage-overview-item-unit" v-if="(token_info && token_info.total) || (token_info && token_info.total == 0)">个</span>
            </div>
          </div>
          <el-divider direction="vertical"></el-divider>
          <div class="usage-overview-item">
            <div class="usage-overview-item-title">总调用次数</div>
            <div class="usage-overview-item-value">
              {{ (token_info && token_info.total_count) || (token_info && token_info.total_count == 0) ? token_info.total_count : '-' }}
              <span class="usage-overview-item-unit" v-if="(token_info && token_info.total_count) || (token_info && token_info.total_count == 0)">次</span>
            </div>
          </div>
          <el-divider direction="vertical"></el-divider>
          <div class="usage-overview-item">
            <div class="usage-overview-item-title">调用总量tokens</div>
            <div class="usage-overview-item-value">
              {{ (token_info && token_info.total_tokens) || (token_info && token_info.total_tokens == 0) ? token_info.total_tokens : '-' }}
            </div>
          </div>
          <el-divider direction="vertical"></el-divider>
          <div class="usage-overview-item">
            <div class="usage-overview-item-title">输入总量tokens</div>
            <div class="usage-overview-item-value">
              {{ (token_info && token_info.input_tokens) || (token_info && token_info.input_tokens == 0) ? token_info.input_tokens : '-' }}
            </div>
          </div>
          <el-divider direction="vertical"></el-divider>
          <div class="usage-overview-item">
            <div class="usage-overview-item-title">输出总量tokens</div>
            <div class="usage-overview-item-value">
              {{ (token_info && token_info.output_tokens) || (token_info && token_info.output_tokens == 0) ? token_info.output_tokens : '-' }}
            </div>
          </div>
        </div>
        <div>
        </div>
      </div>
      <div style="width:100%;height: 300px;" v-show="model_tokens.length > 0">
        <div class="pieCharts" style="width:100%;height: 300px;" :id="id"></div>
      </div>
      <div style="height: calc(100% - 130px);">
        <cl-table-container name="compare-data-grop" model="flex">
          <cl-table-header inputWidth="208px" placeholder="搜索（模型名称）" @search="table_search"
            :table_loading="table_loading" :page_num="page_num" :page_size="page_size">
          </cl-table-header>
          <cl-table-body>
            <cl-table element-loading-text="加载中..." :data="table_list" :ref="`all_model_repository`"
             :columns="tableColunms" @no-data="noData">
            </cl-table>
            <cl-button v-show="table_list.length == 0" id="custom_go_to_button" type="primary" @click="$router.push('/modelSquence')">去模型广场</cl-button>
          </cl-table-body>
        </cl-table-container>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
import { get_token_metrics_list, get_model_statistics, get_model_tokens, get_cluster_list } from '@/http/operation-http/operation-http';
import coljs from "./table_col";
import datePicker from './component/date-picker.vue';
export default {
  components: { datePicker },
  data() {
    return {
      radioType: 4,
      clusterId: '',
      cluster_list: [],
      nodataUrl: require('@/assets/images/EmptyState.svg'),
      radioOpt: [
        { label: '今天', value: 1 },
        { label: '近2天', value: 2 },
        { label: '近3天', value: 3 },
        { label: '近7天', value: 4 },
        { label: '近15天', value: 5 },
      ],
      datePickerTime: [],
      tmp_datePickerTime: [],
      pieChartsData: [],
      pieChart: null,
      id: `usageEchartsCard`,
      table_list: [],
      search_value: "",
      page_size: 10,
      page_num: 1,
      page_total: 0,
      project_id: localStorage.getItem('ProjectId') || '',
      table_loading: false,
      table_select: [],
      tableColunms: [],
      colorDefault: {
        serious: '#E3372D',
        high: '#e06513',
        mid: '#e09513',
        low: '#1387e0',
      },
      token_info: null,
      model_tokens: [],
      chart_loading: false
    }

  },
  watch: {
    datePickerInterval(newVal) {
      //console.log(newVal,'datePickerInterval');
    },
    datePickerTime(newVal, oldVal) {
      this.table_loading = true
      this.get_token_metrics_list()
      this.get_model_statistics()
      this.get_model_tokens()
    },
    radioType(newVal, oldVal) {
      this._setDatePickerValue()
    },
    clusterId(newVal, oldVal) {
      console.log(newVal, 'newVal');
      this.table_loading = true
      this.get_token_metrics_list()
      this.get_model_statistics()
      this.get_model_tokens()
    },
    tmp_datePickerTime(newV, oldV) {

      let data = moment(newV[1]).format('YYYY-MM-DD 23:59:59')
      this.datePickerTime = [newV[0], data];
      this.get_token_metrics_list()
    }
  },
  mounted() {
    this.tableColunms = coljs(this)
    this._setDatePickerValue()
    this.get_token_metrics_list()
    this.get_model_statistics()
    this.get_model_tokens();
    this.getClusterList()
    // this.get_model_tokens()
    this.$nextTick(() => {
      document.getElementsByClassName('bread-container')[0].style.marginBottom = '10px'
    })
  },
  methods: {
    /**设置请求时间 */
    _setDatePickerValue() {
      const subTractType = {
        1: [0, 'day'],
        2: [1, 'day'],
        3: [2, 'day'],
        4: [6, "day"],
        5: [14, "day"]
      }

      const subTractVal = subTractType[this.radioType]
      const nowDate = new Date()
      const afterDate = moment(nowDate).format('YYYY-MM-DD 23:59:59')
      const beforeDate = moment(nowDate)
        .subtract(subTractVal[0], subTractVal[1])
        .format('YYYY-MM-DD 00:00:00')

      this.datePickerTime = [beforeDate, afterDate]

    },
    get_token_metrics_list() {
      this.table_loading = true
      let start_time = this.datePickerTime[0]
      let end_time = this.datePickerTime[1]
      let params = {
        start_time: start_time,
        end_time: end_time,
        project_id: this.project_id,
      }
      if (this.clusterId !== '') {
        params.cluster_id = this.clusterId
      }
      get_token_metrics_list(params).then((res) => {
        if (res.success) {
          this.token_info = res.content
          this.table_loading = false
        }
      }).catch((_) => {
        this.table_loading = false
      })
    },
    get_model_statistics() {
      let start_time = this.datePickerTime[0]
      let end_time = this.datePickerTime[1]
      let params = {
        start_time: start_time,
        end_time: end_time,
        project_id: this.project_id,
      }
      if (this.search_value !== '') params.model_id = this.search_value
      if (this.clusterId !== '') {
        params.cluster_id = this.clusterId
      }
      get_model_statistics(params).then((res) => {
        if (res.success) {
          this.table_list = res.content
          if (res.content.length == 0) {
            this.$nextTick(() => {
              let ele = document.getElementsByClassName('cloud-table-container-box')[0].clientHeight
              let button = document.getElementById('custom_go_to_button')
              if (ele) button.style.top = ele / 2 + 120 + 'px'
              console.log(ele);
              
            })
          }
          this.table_loading = false
        }
      }).catch((_) => {
        this.$nextTick(() => {
              let ele = document.getElementsByClassName('cloud-table-container-box')[0].high
              let button = document.getElementById('custom_go_to_button')
              if (ele) button.style.top = ele / 2 + 120 + 'px'
              
            })
        this.table_loading = false
      })
    },
    get_model_tokens() {
      let start_time = this.datePickerTime[0]
      let end_time = this.datePickerTime[1]
      let params = {
        start_time: start_time,
        end_time: end_time,
        project_id: this.project_id,
      }
      if (this.clusterId !== '') {
        params.cluster_id = this.clusterId
      }
      this.chart_loading = true
      get_model_tokens(params).then((res) => {
        if (res.success) {
          this.model_tokens = res.content
          if (res.content.length > 0) {
            let xAxis = res.content.map((item) => {
              return item.create_date
            })
            let data = res.content.map((item) => {
              return item.tokens
            })

            if (!this.pieChart) {
              this.initCharts()
            }
            const maxLabel = Math.max(...data.map(value => String(value).length)); 
            
            const labelWidth = maxLabel == 1 ? maxLabel * 8 + 17 : maxLabel * 8 + 10; // 每个字符宽度约为 8px
            this.pieChart.setOption({
              title: {
                show: false,
                text: "",
              },
              // legend: {
              //   data: ['调用总量tokens'],
              // },
              grid: {
                right: '0',     // 右边距 
                left: labelWidth 
              },
              xAxis: {
                type: 'category',
                data: xAxis
              },
              yAxis: {
                type: 'value',
                name: '调用总量tokens',
                nameTextStyle: {
                  align: "left"
                },
              },
              tooltip: {
                trigger: 'axis',
                position: 'right',
                formatter: function (parms) {
                  let tooltipContent = '';  
                  parms.forEach((item) => {  
                      tooltipContent += item.marker+ '调用总量tokens'+':&nbsp;&nbsp;' + item.value + '<br/>' +'&nbsp;&nbsp;&nbsp;&nbsp;'+item.axisValue + '<br/>';  
                  });  
                  return tooltipContent;
                  // var str = parms.marker + "" +
                  //   "调用总量tokens：" + parms.data + "</br>"
                  // return str;
                }
              },
              series: [
                {
                  data: data,
                  type: 'bar',
                  barWidth: 40,
                  showBackground: true,
                  itemStyle: {
                    color: '#1387e0',
                  },
                }
              ]
            })
            setTimeout(() => {
              this.pieChart.resize()
            }, 200);
          }
          setTimeout(() => {
            this.chart_loading = false
          }, 500);
        }
      })
        .catch((err) => {
          this.chart_loading = false
        })
    },
    initCharts() {
      this.pieChartsData = [{ name: 'test', value: 100 }, { name: 'test1', value: 1200 }]
      let domContainer = document.getElementById(this.id);
      if (domContainer) {
        this.pieChart = this.$echarts.init(domContainer);
        this.get_model_tokens();
        let option = this.creatSeries()
        this.pieChart.setOption(option);
      }
    },
    creatSeries() {
      let option = {
        legend: {
          data: ['调用总量'],
        },
        xAxis: {
          type: 'category',
          data: ['调用总量']
        },
        yAxis: {
          type: 'value',
          name: '调用总量',
          nameTextStyle: {
            align: "right"
          }
        },
        series: [
          {
            data: [0, 0, 0],
            type: 'bar',
            barWidth: 40,
            showBackground: true,
          }
        ]
      };
      return option;
    },
    updateCharts() {
      if (!this.pieChart) {
        this.initCharts();
      }
      let option = this.creatSeries();
      this.pieChart.setOption(option);
    },
    table_search(res) {
      this.search_value = res.value
      this.page_num = res.page_num
      this.page_size = res.page_size
      this.get_model_statistics()
    },
    noData() {
      if (this.page_num > 1) {
        this.page_num = this.page_num - 1
        this.get_model_statistics()
      }
    },
    getClusterList() {
      let params = {
        page_num: 1,
        page_size: 999,
        project_id: this.project_id
      };
      this.table_loading = true;
      get_cluster_list(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.cluster_list = []
            let arr = [{
              id: '',
              name: '全部'
            }]
            this.cluster_list = res.content ? arr.concat(res.content.list) : [];
          }
          
        })
        .catch(_ => {
          this.table_loading = false;
        });
    },
  }
}
</script>
<style lang="scss" scoped>
#usageEchartsCard {
  width: 100%;
  /* 容器宽度 */
  height: 100%;
  /* 容器高度 */
  transition: width 0.2s, height 0.2s;
  /* 添加过渡动画 */
}

>>> .cloud-table-container-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

#custom_go_to_button {
  position: absolute;
}

.cloud-table-header {
  padding: 20px 0 0;
}

.cloud-table-body {
  padding: 20px 0;
}

.table-main {
  width: 100%;
}

.usage-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.usage-overview-container {
  margin-top: 20px;
  padding: 20px;
  border-radius: 4px;
  background-color: #32353C0A;
  border: 1px solid #292C331A;
}

.usage-container-header {
  padding-bottom: 10px;
  background-color: #fff;
}

.usage-overview {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .usage-overview-item {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: 400;

    .usage-overview-item-title {
      font-weight: 400;
    }

    .usage-overview-item-value {
      font-weight: 500;
      font-size: 32px;
      color: #282A2E;
    }

    .usage-overview-item-unit {
      font-size: 14px;
    }

  }
}

>>>.el-divider--vertical {
  background-color: #292C331A !important;
  height: 40px !important;
}
</style>