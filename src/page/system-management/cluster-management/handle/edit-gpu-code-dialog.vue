<template>
  <sugon-dialog :isShow.sync="state" @confirm="confirm" title="编辑加速卡型号" @close="handleClose">
    <div class="sugon-dialog-out-padding" style="padding: 30px 20px;">
        <el-form
            ref="form"
            :model="form"
            label-width="100px"
            :rules="rules"
            class="demo-ruleForm"
            label-position="right"
            >
            <el-form-item label="加速卡型号" prop="gpu_code">
              <el-select
                placeholder="请选择加速卡型号"
                v-model="form.gpu_code"
              >
                <el-option
                  :label="ele.code"
                  :value="ele.code"
                  v-for="(ele, index) of gpu_info"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
        </el-form>
    </div>
  </sugon-dialog>
</template>

<script>
import {modify_node_info} from "@/http/system-http/system-http";
import { get_gpu_info } from "@/http/squrare-http";
export default {
    data() {
        return {
            state: false,
            form: {
              gpu_code: '',
            },
            project_id: '',
            gpu_info: [],
            rules: {
              gpu_code: [
                { required: true, message: '请请选择加速卡型号', trigger: 'blur' }
              ]
            }
        }
    },
    methods:{
        open(data){
            if(data){
                Object.assign(this.form,data)
            }
            this.project_id = this.$get_projectId()
            this.get_gpu_info()
            this.state = true
        },
        get_gpu_info() {
          get_gpu_info({ page_size: 9999, page_num: 1 }).then((data) => {
            this.$handle_http_back(data, true, false).then((res) => {
              this.gpu_info = res.content ? res.content.list : [];
            });
          });
        },
        handleClose () {
          this.form = {
            gpu_code: ''
          }
          this.state = false;
        },
        confirm(response){
          modify_node_info(this.form.id, this.form.gpu_code).then((res)=>{
            this.$handle_http_back(res,false,false)
            this.$emit('ok')
            this.state = false
            response.close()
          }).catch((err)=>{
            console.error(err)
            response.close()
          })
        }
    }
}
</script>

<style>

</style>
