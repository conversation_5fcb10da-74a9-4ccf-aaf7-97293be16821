<template>
  <sugon-dialog
    :isShow.sync="state"
    @confirm="confirm"
    width="750"
    :title="is_edit ? '编辑' : '新增'"
    :isDisabled="user_type !== 'admin'"
    @close="handleClose"
  >
    <div class="sugon-dialog-out-padding">
      <div class="apikey-tips">
        <el-alert show-icon type="info">
          需先完成算力集群所需服务部署后，再配置新增该集群。
        </el-alert>
      </div>
      <el-form
        ref="form"
        :model="form"
        label-width="170px"
        :rules="rules"
        class="demo-ruleForm"
        label-position="right"
      >
        <el-form-item label="集群类型" prop="cluster_type">
          <el-radio-group v-model="form.cluster_type">
            <el-radio :label="'中央集群'" :disabled="user_type !== 'admin'"
              >中央集群</el-radio
            >
            <el-radio :label="'边缘集群'">边缘集群</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="集群ID" v-if="form.cluster_type == '边缘集群'" prop="id">
          <el-input v-model="form.id" :disabled="is_edit"></el-input>
        </el-form-item>
        <el-form-item label="集群名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input type="textarea" v-model="form.description"></el-input>
        </el-form-item>
        <el-form-item label="集群地址" prop="k8s_server">
          <span slot="label">
            集群地址
            <el-tooltip content="集群 k8s API 服务地址，用于与客户端通信，端口：6443">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input v-model="form.k8s_server"></el-input>
        </el-form-item>
        <el-form-item label="集群认证 Token" prop="token">
          <span slot="label">
            集群认证 Token
            <el-tooltip content="集群 k8s 认证 Token">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input type="textarea" :rows="5" v-model="form.token"></el-input>
        </el-form-item>
        <el-form-item label="OpenAPI 服务地址" prop="endpoint_server">
          <span slot="label">
            OpenAPI 服务地址
            <el-tooltip content="集群内 OpenAPI 网关服务 IP:Port">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
          <el-input v-model="form.endpoint_server"></el-input>
        </el-form-item>
      </el-form>
    </div>
  </sugon-dialog>
</template>

<script>
import { modify_cluster, create_cluster } from "@/http/system-http/system-http";
export default {
  data() {
    const validateId = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入集群ID'));
      } else {
        if (value.length > 64 || value.length < 8) {
          return callback(new Error('集群ID长度为8-64位')); 
        } else {
          const regex = /^[a-zA-Z0-9-]+$/; 
          if (!regex.test(value)) {
           return callback(new Error("ID只能输入英文、数字和中划线")); 
          } else {
            return callback();
          }
        }
      }
    }
    return {
      state: false,
      is_edit: false,
      form: {
        name: "",
        description: "",
        k8s_server: "",
        endpoint_server: "",
        token: "",
        cluster_type: "中央集群",
        id: ''
      },
      project_id: "",
      user_type: "",
      rules: {
        cluster_type: [
          { required: true, message: "请选择集群类型", trigger: "blur" },
        ],
        name: [
          { required: true, message: "请输入名称", trigger: "blur" },
          { validator: nameTestRule },
        ],
        k8s_server: [
          { required: true, message: "请输入集群地址", trigger: "blur" },
        ],
        endpoint_server: [
          { required: true, message: "请输入映射地址", trigger: "blur" },
        ],
        token: [{ required: true, message: "请输入认证信息", trigger: "blur" }],
        id: [{
          required: true, message: "请输入集群ID", trigger: "blur"
        }, {
          validator: validateId, trigger: ['blur']
        }]
      },
    };
  },
  methods: {
    open(data) {
      this.user_type = GetUserInfo("userType");
      this.form.cluster_type =
        this.user_type !== "admin" ? "边缘集群" : "中央集群";
      if (data) {
        this.is_edit = true;
        Object.assign(this.form, data);
      } else {
        this.is_edit = false;
      }

      this.project_id = this.$get_projectId();
      this.state = true;
    },
    handleClose() {
      this.is_edit = false;
      this.form = {
        name: "",
        description: "",
        k8s_server: "",
        endpoint_server: "",
        token: "",
        cluster_type: "",
      };
      this.state = false;
    },
    confirm(response) {
      //如果表单有校验，会自动校验。不需另外写element的校验函数。response返回有按钮loading关闭函数
      let data = {
        ...this.form,
        project_id: this.project_id,
        user_id: GetUserInfo("userId"),
      };
      if (this.is_edit) {
        modify_cluster(data)
          .then((res) => {
            this.$handle_http_back(res, false, false);
            response.close();
            if (res.success) {
              this.$emit("ok");
              this.handleClose();
            }
          })
          .catch((err) => {
            console.error(err);
            response.close();
          });
      } else {
        create_cluster(data)
          .then((res) => {
            this.$handle_http_back(res, false, false);
            response.close();
            if (res.success) {
              this.$emit("ok");
              this.handleClose();
            }
          })
          .catch((err) => {
            console.error(err);
            response.close();
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
>>> .apikey-tips {
  padding: 10px 0px 20px 0;
  i {
    color: var(--color-blue);
  }
  .el-alert {
    background-color: var(--color-blue-lighten);
  }
  .el-alert .el-alert__description {
    display: flex;
    align-items: center;
    margin: 0;
    color: var(--color-theme-text);
  }
  .el-alert__icon {
    font-size: 16px;
    width: 16px;
  }
}
</style>
