<template>
  <cl-page-container title="集群管理" explain="集群管理" :hideHead="true">
    <bread name="集群管理" description="灵活的算力集群管理，支持用户配置边缘算力集群，并提供私有的模型服务接入点，满足用户个性化需求。"></bread>
    <cl-table-container name="compare-data-grop" model="flex" v-if="init_table_list.length > 0">
      <cl-table-header
        inputWidth="208px"
        placeholder="搜索（集群名称）"
        @search="table_search"
        :table_loading="table_loading"
        :page_num="page_num"
        :page_size="page_size"
      >
        <template slot="left">
          <div class="table-tool-bar-left">
            <cl-button type="primary" icon="el-icon-plus" @click="operateStrategy(null, 'add')">
              新增集群
            </cl-button>
          </div>
        </template>
      </cl-table-header>
      <cl-table-body>
        <cl-table
          element-loading-text="加载中..."
          :data="table_list"
          :ref="`all_model_repository`"
          @selection-change="selectionChange"
          :columns="tableColunms"
          @no-data="noData"
        ></cl-table>
      </cl-table-body>
      <cl-table-footer>
        <cl-pagination
          background
          v-if="table_list.length != 0"
          @size-change="handleListSizeChange"
          @current-change="handleListCurrentChange"
          :current-page="page_num"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page_size"
          layout="sizes, total, jumper, next, pager, prev"
          :total="page_total"
        >
        </cl-pagination>
      </cl-table-footer>
    </cl-table-container>
    <div v-else class="empty-card-box">
      <img :src="noresult_url" />
      <div style="margin: 0px 0 20px 0;font-weight: 900;font-size: 14px;">暂无数据</div>
      <p style="margin: 0px 0 30px 0">
        当前项目下暂无集群
      </p>
      <cl-button type="primary" @click="operateStrategy(null, 'add')"
        >创建集群</cl-button
      >
    </div>
    <stopDialog ref="stopDialog" @ok="initList" />
    <sugon-delete-dialog
      :list="table_select"
      :isShow.sync="dialogDel"
      @confirm="delTableChoose"
      :title="`${table_select && table_select.length > 1 ? '删除' : '删除'}`"
      nameKey="name"
      idKey="id"
    >
    </sugon-delete-dialog>
  </cl-page-container>
</template>
<script>
import * as modelHttp from "@/http/system-http/system-http";
import stopDialog from "./handle/create-edit-dialog.vue";
import coljs from "./table_col.js";
import bread from "@/components/bread/index.vue";
export default {
  name: "modelDeployment",
  components: { stopDialog, bread },
  data() {
    return {
      noresult_url: require("@/assets/images/EmptyState.svg"),
      search_value: "",
      tabActiveName: "all",
      table_list: [],
      init_table_list: [],
      page_size: 10,
      page_num: 1,
      page_total: 0,
      table_loading: false,
      table_select: [],
      tableColunms: [],
      dialogDel: false,
      project_id: localStorage.getItem("ProjectId") || ""
    };
  },
  mounted() {
    this.initList();
    this.tableColunms = coljs(this);
    if (this.$route.query.create) {
        setTimeout(() => {
          this.$refs.stopDialog.open();
        }, 700);
      }
  },
  methods: {
    initList() {
      this.table_loading = true;
      let params = {
        page_num: this.page_num,
        page_size: this.page_size,
        project_id: this.project_id,
      };
      modelHttp
        .get_cluster_list(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.init_table_list = res.content.list;
          }
          this.getList()
        })
        .catch(_ => {
          this.table_loading = false;
        });
    },
    typeFilter: function(val) {
      return val === "集群正常" ? "success" : "error";
    },
    table_search(res) {
      this.search_value = res.value;
      this.page_num = res.page_num;
      this.page_size = res.page_size;
      this.initList();
    },
    noData() {
      if (this.page_num > 1) {
        this.page_num = this.page_num - 1;
        this.initList();
      }
    },
    training_status_change(val) {
      this.status = val;
      this.initList();
    },
    name_change(val) {
      this.search_value = val;
    },
    getList() {
      let params = {
        page_num: this.page_num,
        page_size: this.page_size,
        project_id: this.project_id,
        name: this.search_value
      };
      this.table_loading = true;
      modelHttp
        .get_cluster_list(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.page_total = res.content.total;
            this.table_list = res.content.list;
          }
          this.table_loading = false;
        })
        .catch(_ => {
          this.table_loading = false;
        });
    },
    handleListSizeChange(page_size) {
      this.page_size = page_size;
      /**每页条数发生变化 */
      this.initList();
    },
    handleListCurrentChange(pageIndex) {
      /**当前页发生变化 */
      this.page_num = pageIndex;
      this.initList();
    },
    selectionChange(row) {
      this.table_select = row;
    },
    operateStrategy(item, data) {
      switch (data) {
        case "delDialog":
          this.table_loading = false;
          this.table_select = [];
          this.table_select.push(item);
          this.$table_select_repeat(
            this.table_select,
            this.table_list,
            "all_model_repository"
          );
          this.dialogDel = true;
          break;
        case "edit":
          this.$refs.stopDialog.open(item);
          break;
        case "add":
          this.$refs.stopDialog.open();
          break;
        default:
          break;
      }
    },
    gotoDetail(row) {
      this.$go_detail(
        this,
        `/AiClusterManagement/detail?id=${row.id}&name=${row.name}`,
        {
          page_size: this.page_size,
          page_num: this.page_num
        }
      );
    },
    goto(link, row) {
      this.$router.push({
        path: link,
        query: {
          name: row.model_id,
          model_id: row.model_id,
          mode_type: 2
        }
      });
      this.$nextTick(_ => {
        AppMenu.setActive(link);
      });
    },
    delTableChoose(response) {
      let value = response.data.map(el => {
        return {
          name: el.name,
          params: el.id,
          request: modelHttp.del_cluster
        };
      });
      const that = this;
      const batch_delete = new this.$batch_delete({
        data: value,
        title: "删除提示",
        success: function() {
          that.dialogDel = false;
          that.initList();
        },
        error: function(err) {
          that.dialogDel = false;
          that.initList();
        }
      });
      /**发送请求 */
      batch_delete.del();
    }
  },
  destroyed() {}
};
</script>
<style lang="scss" scoped>
.cloud-table-container-flex {
  height: calc(100% - 70px);
}
.empty-card-box {
  height: calc(100% - 70px);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #fff;
}
</style>
