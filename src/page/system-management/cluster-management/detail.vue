<template>
  <div style="height: 100%">
    <sugon-back-button :name="name"></sugon-back-button>
    <div class="cluster-management">
      <div class="cluster-management-header">
        <div
          class="cluster-management-card"
          :style="{ width: cardWidth, marginRight: '16px' }"
        >
          <div class="card-content">
            <p class="jdsl">节点数量</p>
            <p class="jdsl-num">
              <span style="font-size: 32px;">{{ node_info.node_count }}</span
              >个
            </p>
          </div>
          <div class="card-icon">
            <i class="iconfont icon-GPUjiedian"></i>
          </div>
        </div>
        <div
          class="cluster-management-card"
          :style="{ width: cardWidth, marginRight: '16px' }"
        >
          <div class="card-content">
            <p class="jdsl">加速卡</p>
            <p class="jdsl-num">
              <span style="font-size: 32px;">{{ node_info.gpu_count }}</span
              >个
            </p>
          </div>
          <div class="card-icon">
            <i class="iconfont icon-xianka"></i>
          </div>
        </div>
        <div
          class="cluster-management-card"
          :style="{ width: cardWidth, marginRight: '16px' }"
        >
          <div
            class="card-content"
            v-if="node_info.gpu_memory_mb || node_info.gpu_memory_mb == 0"
          >
            <p class="jdsl">加速卡显存使用率</p>
            <div
              class="jdsl-num"
              style="display: flex;justify-content: space-between;"
            >
              <div>
                <span style="font-size: 32px;">{{
                  node_info.gpu_memory_mb == 0
                    ? 0
                    : (
                        (node_info.used_gpu_memory_mb /
                          node_info.gpu_memory_mb) *
                        100
                      ).toFixed(2)
                }}</span
                >%
              </div>
              <div class="card-icon">
                <i class="iconfont icon-xianka"></i>
              </div>
            </div>
            <p class="jdsl-des">
              <span
                >使用量：{{ node_info.used_gpu_memory_mb.toFixed(2) }}GiB</span
              >
              <span>总量：{{ node_info.gpu_memory_mb.toFixed(2) }}GiB</span>
            </p>
          </div>
        </div>
        <div class="cluster-management-card" :style="{ width: cardWidth }">
          <div
            class="card-content"
            v-if="node_info.memory_mb || node_info.memory_mb == 0"
          >
            <p class="jdsl">内存使用率</p>
            <div
              class="jdsl-num"
              style="display: flex;justify-content: space-between;"
            >
              <div>
                <span style="font-size: 32px;">{{
                  node_info.memory_mb == 0
                    ? 0
                    : (
                        (node_info.used_memory_mb / node_info.memory_mb) *
                        100
                      ).toFixed(2)
                }}</span
                >%
              </div>
              <div class="card-icon">
                <i class="iconfont icon-neicun1"></i>
              </div>
            </div>
            <p class="jdsl-des">
              <span>使用量：{{ node_info.used_memory_mb.toFixed(2) }}GiB</span>
              <span>总量：{{ node_info.memory_mb.toFixed(2) }}GiB</span>
            </p>
          </div>
        </div>
      </div>
      <div class="cluster-management-body">
        <div class="title">节点列表</div>
        <div class="table-content">
          <cl-table
            height="100%"
            :data="table_list"
            v-loading="table_loading"
            element-loading-text="加载中..."
            :columns="table_colunms"
          ></cl-table>
        </div>
        <el-pagination
          class="is-background"
          @size-change="pageSizeChange"
          @current-change="pageChange"
          :current-page="page_num"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page_size"
          layout="sizes, total, jumper, next, pager, prev"
          :total="page_total"
        ></el-pagination>
      </div>
    </div>
    <editGpuCode ref="editGpuCode" @ok="get_node_info"/>
  </div>
</template>

<script>
import * as systemHttp from "@/http/system-http/system-http";
import editGpuCode from './handle/edit-gpu-code-dialog.vue';
export default {
  name: "ClusterManagement",
  components: {editGpuCode},
  computed: {
    cardWidth() {
      let str = `calc((100% - ${(this.speed - 1) * 16}px) / ${this.speed})`;
      return str;
    }
  },
  data() {
    const statusFilter = val => {
      let value = {
        type: "",
        value: ""
      };
      switch (val) {
        case "1":
          value.type = "success";
          value.value = "运行中";
          break;
        default:
          break;
      }
      return value;
    };
    return {
      name: "",
      node_info: {},
      page_num: 1,
      page_size: 10,
      page_total: 12,
      speed: 4,
      table_list: [],
      table_loading: false,
      table_colunms: [
        {
          prop: "name",
          label: "节点名称"
        },
        {
          prop: "ip",
          label: "ip地址",
          render(h, scope) {
            return <span>{scope.row.ip ? scope.row.ip : "--"}</span>;
          }
        },
        {
          prop: "status",
          label: "状态",
          render: (h, scope) => {
            return (
              <span>
                <stateClassification
                  type={statusFilter(scope.row.status).type}
                  value={statusFilter(scope.row.status).value}
                ></stateClassification>
              </span>
            );
          }
        },
        {
          prop: "name",
          label: "加速卡型号",
          render(h, scope) {
            return <span>{scope.row.gpu_code ? scope.row.gpu_code : '--'}</span>;
          }
        },
        {
          prop: "gpu_used",
          label: "加速卡数量",
          render(h, scope) {
            return (
              <span>
                {scope.row.gpu_used} / {scope.row.gpu_count}
              </span>
            );
          }
        },
        {
          prop: "speed_card_memory",
          label: "加速卡显存",
          "min-width": "100px",
          render(h, scope) {
            return (
              <span>
                {scope.row.speed_card_memory_used.toFixed(2)}GiB /{" "}
                {scope.row.speed_card_memory.toFixed(2)}GiB
              </span>
            );
          }
        },
        {
          prop: "cpu_rate",
          label: "CPU使用率",
          render(h, scope) {
            return <span>{scope.row.cpu_rate.toFixed(2)}%</span>;
          }
        },
        {
          prop: "memory_rate",
          label: "内存使用率",
          render(h, scope) {
            return <span>{scope.row.memory_rate.toFixed(2)}%</span>;
          }
        },
        {
          prop: "disk_rate",
          label: "磁盘使用率",
          render(h, scope) {
            return <span>{scope.row.disk_rate.toFixed(2)}%</span>;
          }
        },
        {
        label: "操作",
        width: "60",
        align: "center",
        render: (h, scope) => {
          return (
            <el-dropdown size="small" placement="bottom" trigger="click">
              <cl-button icon="el-icon-setting" title="操作"></cl-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  onClick={() => this.operateStrategy(scope.row, "edit")}
                >
                  编辑加速卡型号
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          );
        },
        resizable: false
      }
      ]
    };
  },
  created() {
    if (this.$route.query.name) {
      this.name = this.$route.query.name;
    }
    this.get_node_info();
  },
  methods: {
    get_node_info() {
      let params = {
        page_num: this.page_num,
        page_size: this.page_size,
        cluster_id: this.$route.query.id
      };
      this.table_loading = true;
      systemHttp
        .get_node_info(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.node_info = res.content;
            this.table_list = res.content.node_info_list.list;
            this.page_total = res.content.node_info_list.total;
          }
          this.table_loading = false;
        })
        .catch(_ => {
          this.table_loading = false;
        });
    },
    pageSizeChange(val) {
      this.page_size = val;
      this.get_node_info();
    },
    pageChange(val) {
      this.page_num = val;
      this.get_node_info();
    },
    operateStrategy(data, type) {
      switch (type) {
        case 'edit':
          this.$refs.editGpuCode.open(data)
          break;
        default:
          break;
      }
    }
  }
};
</script>

<style lang="scss">
.cluster-management {
  height: calc(100% - 50px);
  width: 100%;
  background: #f5f6f9;
  display: flex;
  flex-direction: column;
  .table-main {
    padding: 0;
  }
  .cluster-management-header {
    overflow: hidden;
    .cluster-management-card {
      float: left;
      display: flex;
      height: 116px;
      background: white;
      box-sizing: border-box;
      padding: 0 24px;
      align-items: center;
      .card-icon {
        width: 46px;
        color: var(--color-theme);
        text-align: right;
        .iconfont {
          font-size: 32px;
        }
      }
      .card-content {
        flex: 1;
        .jdsl {
          height: 22px;
          font-size: 16px;
          line-height: 22px;
        }
        .jdsl-num {
          font-size: 14px;
        }
        .jdsl-des {
          font-size: 12px;
          color: #494a4d;
          span {
            margin-right: 6px;
          }
        }
      }
    }
  }
  .cluster-management-body {
    margin-top: 16px;
    background: white;
    padding: 16px 16px 0;
    box-sizing: border-box;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .title {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 600;
    }
    .table-content {
      flex: 1;
      overflow: hidden;
      .el-table {
        margin-bottom: 0;
      }
    }
  }
}
</style>
