export default function(vueApp) {
  let $store = vueApp.$store;
  let $route = vueApp.$route;
  let $router = vueApp.$router;
  let $moment = vueApp.$moment;

  let { gotoDetail, operateStrategy, typeFilter } = vueApp;
  let { dateformat } = vueApp.$options.filters;
  return [
    {
      label: "集群名称",
      render: (h, scope) => {
        return h(
          "a",
          {
            on: {
              click: () => {
                gotoDetail(scope.row);
              }
            }
          },
          scope.row.name
        );
      }
    },
    {
      label: "集群状态",
      render: (h, scope) => {
        return scope.row.cluster_status ? (
          <span>
            <stateClassification
              type={typeFilter(scope.row.cluster_status)}
              value={scope.row.cluster_status}
            ></stateClassification>
          </span>
        ) : null;
      }
    },
    {
      label: "集群类型",
      render: (h, scope) => {
        return <span>{scope.row.cluster_type}</span>;
      }
    },
    {
      label: "节点数量",
      render: (h, scope) => {
        return <span>{scope.row.node_count}</span>;
      }
    },
    {
      label: "加速卡数量",
      render: (h, scope) => {
        return <span>{scope.row.gpu_count}</span>;
      }
    },
    {
      label: "描述",
      render: (h, scope) => {
        return <span>{scope.row.description}</span>;
      }
    },

    {
      label: "创建时间",
      render: (h, scope) => {
        return <span>{dateformat(scope.row.create_time)}</span>;
      }
    },
    {
      label: "更新时间",
      render: (h, scope) => {
        return <span>{dateformat(scope.row.update_time)}</span>;
      }
    },
    {
      label: "操作",
      width: "60",
      align: "center",
      render: (h, scope) => {
        return (
          <el-dropdown size="small" placement="bottom" trigger="click">
            <cl-button icon="el-icon-setting" title="操作"></cl-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                onClick={() => operateStrategy(scope.row, "edit")}
              >
                编辑
              </el-dropdown-item>
              <el-dropdown-item
                onClick={() => operateStrategy(scope.row, "delDialog")}
              >
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        );
      },
      resizable: false
    }
  ];
}
