<template>
  <sugon-dialog :isShow.sync="state" @confirm="confirm" width="600" title="新建" @close="handleClose">
    <div class="sugon-dialog-out-padding">
        <el-form
            ref="form"
            :model="form"
            label-width="100px"
            :rules="rules"
            class="demo-ruleForm"
            label-position="right"
            >
            <el-form-item label="名称" prop="name">
                <el-input v-model="form.name"></el-input>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input type="textarea" v-model="form.description"></el-input>
          </el-form-item>
        </el-form>
    </div>
  </sugon-dialog>
</template>

<script>
import {create_api_key} from "@/http/system-http/system-http";
export default {
    data() {
        return {
            state: false,
            form: {
                name: '',
                description: ''
            },
            project_id: '',
            rules: {
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' },
                    { validator: nameTestRule }
                ]
            }
        }
    },
    methods:{
        open(data){
            if(data){
                Object.assign(this.form,data)
            }
            this.project_id = this.$get_projectId()
            this.state = true
        },
        handleClose () {
          this.form = {
            name: '',
            description: ''
          }
          this.state = false;
        },
        confirm(response){//如果表单有校验，会自动校验。不需另外写element的校验函数。response返回有按钮loading关闭函数
          let data = {
            name: this.form.name,
            description: this.form.description,
            project_id: this.project_id,
             user_id: GetUserInfo("userId"),
          }
          create_api_key(data).then((res)=>{
            this.$handle_http_back(res,false,false)
            this.$emit('ok')
            this.state = false
            response.close()
          }).catch((err)=>{
            console.error(err)
            response.close()
          })
        }
    }
}
</script>

<style>

</style>
