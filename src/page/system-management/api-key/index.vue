<template>
  <cl-page-container title="API Key" explain="API Key" :hideHead="true" v-loading="table_loading">
    <div class="apikey-tips">
        <el-alert show-icon type="info">
        API Key 是您调用霄练 AI 大模型服务的关键凭证，具有长期有效性。请务必避免在公开场合分享该密钥信息，妥善做好保管工作，并定期更换，以防未经授权的访问引发安全隐患或资金损失。
      </el-alert>
    </div>
    <cl-table-container name="compare-data-grop" model="flex" v-if="init_table_list.length > 0">
      <cl-table-header inputWidth="208px" placeholder="搜索（名称）" @search="table_search" :table_loading="table_loading" :page_num="page_num" :page_size="page_size">
        <template slot="left">
          <div class="table-tool-bar-left">
            <cl-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="operateStrategy('', 'add')"
                >
                  新建
                </cl-button>
            <cl-button
              type="danger"
              icon="el-icon-delete"
              :disabled="table_select.length === 0"
              @click="operateStrategy('', 'delBatchDialog')"
            >
              批量删除
            </cl-button>
          </div>
        </template>
      </cl-table-header>
      <cl-table-body>
        <cl-table element-loading-text="加载中..." :data="table_list" :ref="`all_model_repository`" @selection-change="selectionChange" :columns="tableColunms" @no-data="noData" ></cl-table>
      </cl-table-body>
      <cl-table-footer>
        <cl-pagination background v-if="table_list.length != 0" @size-change="handleListSizeChange"
            @current-change="handleListCurrentChange" :current-page="page_num"
            :page-sizes="[10, 20, 50, 100]" :page-size="page_size"
            layout="sizes, total, jumper, next, pager, prev" :total="page_total">
          </cl-pagination>
      </cl-table-footer>
    </cl-table-container>
    <div v-else class="empty-card-box">
      <img :src="noresult_url" />
      <div style="margin: 0px 0 20px 0;font-weight: 900;font-size: 14px;">暂无数据</div>
      <p style="margin: 0px 0 30px 0">
        可创建 API Key 以访问模型服务和应用
      </p>
      <cl-button type="primary" @click="operateStrategy('', 'add')"
        >创建API Key</cl-button
      >
    </div>
    <createDialog ref="createDialog" @ok="handelCreate"/>
    <sugon-delete-dialog
    :list="table_select"
    :isShow.sync="dialogActionDelete"
    @confirm="delTableChoose"
    :title="`${table_select && table_select.length > 1 ? '删除' : '删除'}`"
    nameKey="name"
    idKey="id"
>
</sugon-delete-dialog>
  </cl-page-container>
</template>
<script>
import * as systemHttp from "@/http/system-http/system-http";
import createDialog from './handle/create-dialog';
import coljs from './table_col.js';
export default {
  name:'ApiKey',
  components: {createDialog},
  data() {
    return {
      noresult_url: require("@/assets/images/EmptyState.svg"),
      search_value: "",
      table_list: [],
      init_table_list: [],
      page_size: 10,
      page_num: 1,
      page_total: 0,
      table_loading: false,
      table_select: [],
      tableColunms: [],
      dialogActionDelete: false,
      dialogActionAdd: false,
      project_id: localStorage.getItem("ProjectId") || '',
    }
  },
  created() {
    this.initList();
    this.tableColunms = coljs(this)
  },
  mounted() {
    this.$nextTick(() => {
      document.getElementsByClassName('bread-container')[0].style.marginBottom = '10px'
    })
  },
  methods: {
    handelCreate() {
      this.initList()
    },
    table_search(res){
      this.search_value = res.value
      this.page_num = res.page_num
      this.page_size = res.page_size
      this.initList()
    },
    noData(){
      if(this.page_num>1){
      this.page_num = this.page_num - 1
      this.initList()
      }
    },
    initList() {
      this.table_loading = true;
      let params = {
        page_num: this.page_num,
        page_size: this.page_size,
        project_id: this.project_id
      };
      systemHttp
        .get_api_key(params)
        .then((res) => {
          this.$handle_http_back(res,true,false)
          if (res.content) {
            this.init_table_list = res.content.list;
          }
          this.getList()
        }).catch((_) => {
          this.table_loading = false;
        });
    },
    getList() {
      let params = {
        name: this.search_value,
        page_num: this.page_num,
        page_size: this.page_size,
        project_id: this.project_id
      };
      systemHttp
        .get_api_key(params)
        .then((res) => {
          this.$handle_http_back(res,true,false)
          if (res.content) {
            this.page_total = res.content.total;
            this.table_list = res.content.list;
          }
          this.table_loading = false;
        })
        .catch((_) => {
          this.table_loading = false;
        });
    },
    handleListSizeChange(page_size) {
      this.page_size = page_size;
      /**每页条数发生变化 */
      this.initList()
    },
    handleListCurrentChange(pageIndex) {
      /**当前页发生变化 */
      this.page_num = pageIndex;
      this.initList()
    },
    selectionChange(row) {
      this.table_select = row;
    },
    operateStrategy(item, data) {
      switch (data) {
        case 'delDialog':
          console.log('in');
          this.table_loading = false
          this.table_select = []
          this.table_select.push(item)
          this.$table_select_repeat(this.table_select, this.table_list, 'all_model_repository')
          this.dialogActionDelete = true
          break
        case 'delBatchDialog':
          this.dialogActionDelete = true
          this.table_loading = false
          break
        case 'add':
          this.$refs.createDialog.open(item)
          break
        default:
          break
      }
    },
    delTableChoose(response) {
      let value = response.data.map((el) => {
        return {
          name: el.name,
          params: el.id,
          request: systemHttp.del_api_key,
        };
      });
      const that = this;
      const batch_delete = new this.$batch_delete({
        data: value,
        title: "删除提示",
        success: function () {
          that.dialogActionDelete = false;
          response.close()
          that.initList()
        },
        error: function (err) {
          that.dialogActionDelete = false;
          response.close()
          that.initList()
        },
      });
      /**发送请求 */
      batch_delete.del();
    },
    goto(link) {
      this.$router.push(link)
      this.$nextTick(_=>{
        AppMenu.setActive(link)
      })
    }
  },
  destroyed() {
  },
};
</script>
<style lang="scss" scoped>
>>> .apikey-tips {
  padding: 20px 20px 0 20px;
  i {
    color: var(--color-blue);
  }
  .el-alert {
    background-color: var(--color-blue-lighten);
  }
  .el-alert .el-alert__description {
    display: flex;
    align-items: center;
    margin: 0;
    color: var(--color-theme-text);
  }
  .el-alert__icon {
    font-size: 16px;
    width: 16px;
  }
}
.cloud-table-container-flex {
  height: calc(100% - 60px);
}
.empty-card-box {
  height: calc(100% - 60px);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #fff;
}
</style>
