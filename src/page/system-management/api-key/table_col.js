export default function (vueApp) {
  let $store = vueApp.$store;
  let $route = vueApp.$route;
  let $router = vueApp.$router;
  let $moment = vueApp.$moment;

  let { operateStrategy } = vueApp;
  let {dateformat } = vueApp.$options.filters;
  return [
    {
      type: "selection",
      width: "30",
    },
    {
      "label": "名称",
      "render": (h, scope) => { return <span>{scope.row.name}</span> }
    },
    {
      "label": "API-Key",
      "render": (h, scope) => { return <span>{scope.row.api_key}</span> }
    },
    {
      "label": "描述",
      "render": (h, scope) => { return <span>{scope.row.description}</span> }
    },
    {
      "label": "创建时间",
      "render": (h, scope) => { return <span>{dateformat(scope.row.create_time)}</span> }
    },
    {
      "label": "操作",
      "width": "60",
      "align": "center",
      "render": (h, scope) => {
        return <el-dropdown size="small" placement="bottom" trigger="click" >
          <cl-button icon="el-icon-setting" title="操作" ></cl-button>
          <el-dropdown-menu slot="dropdown" >
            <el-dropdown-item onClick={() => operateStrategy(scope.row, 'delDialog')} >
              删除
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      },
      "resizable": false
    }
  ]
}