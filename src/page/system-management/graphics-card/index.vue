<template>
  <cl-page-container
    title="显卡管理"
    explain="显卡管理"
    :hideHead="true"
    class="graphics-card-container"
  >
  <div v-loading="table_loading" style="height: 100%;">
    <div class="graphics-card-content" v-if="cluster_list.length > 0">
      <el-select
        style="width: 195px;margin-bottom: 16px;"
        placeholder="请选择集群"
        v-model="cluster"
        @change="handelChangeCluster"
      >
        <el-option
          :label="item.name"
          :value="item.id"
          v-for="(item, index) of cluster_list"
          :key="index"
        ></el-option>
      </el-select>
      <div class="graphics-card-content-header">
        <acceleratorCard :gpu_info="gpu_info" v-if="gpu_info"></acceleratorCard>
        <memoryUsageRate :gpu_info="gpu_info" v-if="gpu_info"></memoryUsageRate>
        <computingPowerRate
          :gpu_info="gpu_info"
          v-if="gpu_info"
        ></computingPowerRate>
      </div>
      <cl-table-container
        name="compare-data-grop"
        model="flex"
        class="graphics-card-content-list"
      >
        <div class="graphics-card-content-list-name">
          <span class="list-name">加速卡列表</span>
          <span class="list-name-click" @click="handleClickOpenDialog"
            >查看支持全部加速卡型号</span
          >
        </div>
        <cl-table-body>
          <cl-table
            element-loading-text="加载中..."
            :data="table_list"
            :ref="`all_model_repository`"
            @selection-change="selectionChange"
            :columns="tableColunms"
            @no-data="noData"
          ></cl-table>
        </cl-table-body>
        <cl-table-footer>
          <!-- <cl-pagination
            background
            v-if="table_list.length != 0"
            @size-change="handleListSizeChange"
            @current-change="handleListCurrentChange"
            :current-page="page_num"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page_size"
            layout="sizes, total, jumper, next, pager, prev"
            :total="page_total"
          >
          </cl-pagination> -->
        </cl-table-footer>
      </cl-table-container>
    </div>
    <div v-else class="empty-card-box">
      <img :src="noresult_url" />
      <div style="margin: 0px 0 20px 0;font-weight: 900;font-size: 14px;">暂无数据</div>
      <p style="margin: 0px 0 30px 0">
        当前项目下暂无集群，可创建集群后使用加速卡管理功能
      </p>
      <cl-button type="primary" @click="goto_create_cluster"
        >立即创建集群</cl-button
      >
    </div>
  </div>
    <gpuListDialog ref="gpuListDialog"></gpuListDialog>
  </cl-page-container>
</template>
<script>
import * as systemHttp from "@/http/system-http/system-http";
import coljs from "./table_col.js";
import acceleratorCard from "./handle/accelerator-card.vue";
import memoryUsageRate from "./handle/memory-usage-rate.vue";
import computingPowerRate from "./handle/computing-power-rate.vue";
import gpuListDialog from "./handle/gpu-list-dialog.vue";
export default {
  name: "GraphicsCard",
  components: {
    acceleratorCard,
    memoryUsageRate,
    computingPowerRate,
    gpuListDialog
  },
  data() {
    return {
      noresult_url: require("@/assets/images/EmptyState.svg"),
      table_list: [],
      page_size: 10,
      page_num: 1,
      page_total: 0,
      table_loading: false,
      tableColunms: [],
      gpu_info: null,
      cluster_list: [],
      project_id: localStorage.getItem("ProjectId") || "",
      cluster: null
    };
  },
  created() {
    this.getClusterList();
  },
  mounted() {
    this.$nextTick(() => {
      document.getElementsByClassName('bread-container')[0].style.marginBottom = '10px'
    })
  },
  methods: {
    noData() {
      if (this.page_num > 1) {
        this.page_num = this.page_num - 1;
        this.getList();
      }
    },
    goto_create_cluster() {
      this.$router.push("/AiClusterManagement?create=true");
    },
    handelChangeCluster() {
      this.table_loading = true
      this.getList();
    },
    getClusterList() {
      let params = {
        page_num: 1,
        page_size: 999,
        project_id: this.project_id
      };
      this.table_loading = true;
      systemHttp
        .get_cluster_list(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.cluster_list = res.content ? res.content.list : [];
            if (this.cluster_list.length > 0) {
              this.cluster = this.cluster_list[0].id;
              this.getList();
              this.tableColunms = coljs(this);
            } else {
              this.table_loading = false;
            }
          }
          
        })
        .catch(_ => {
          this.table_loading = false;
        });
    },
    getList() {
      let params = {
        cluster_id: this.cluster ? this.cluster : ""
      };
      systemHttp
        .get_gpu_info(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.gpu_info = res.content;
            this.page_total = res.content.gpu_group_list.length;
            this.table_list = res.content.gpu_group_list;
          }
          this.table_loading = false;
        })
        .catch(_ => {
          this.table_loading = false;
        });
    },
    handleListSizeChange(page_size) {
      this.page_size = page_size;
      /**每页条数发生变化 */
      this.getList();
    },
    handleListCurrentChange(pageIndex) {
      /**当前页发生变化 */
      this.page_num = pageIndex;
      this.getList();
    },
    handleClickOpenDialog() {
      this.$refs.gpuListDialog.open(this.gpu_info);
    }
  }
};
</script>
<style lang="scss" scoped>
.empty-card-box {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #fff;
}
.graphics-card-container {
  background-color: #fff;
  padding: 20px;
}
.graphics-card-content {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.graphics-card-content-header {
  width: 100%;
  height: 187px;
  display: flex;
  justify-content: space-between;
  /* background-color: #fff; */
  margin-bottom: 10px;
  overflow: hidden;
}
.graphics-card-content-list {
  height: calc(100% - 240px);
  
}
.graphics-card-content-list-name {
  // padding-left: 20px;
  padding-top: 16px;
  .list-name {
    margin-right: 10px;
    font-size: 14px;
    font-weight: 500;
  }
  .list-name-click {
    font-size: 12px;
    cursor: pointer;
    color: #1487fd;
  }
}
>>> .cloud-table-body {
  padding: 20px 0;
}

</style>
