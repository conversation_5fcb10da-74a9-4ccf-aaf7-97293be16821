<template>
  <div class="memory-usage-rate">
    <div class="memory-usage-rate-name">加速卡显存使用率</div>
    <div class="memory-usage-rate-content" v-if="gpu_info">
      <div class="memory-usage-rate-chart">
        <div style="width: 130px;height: 130px;">
          <el-progress
            type="circle"
            :percentage="percentage"
            color="#FE814E"
            :stroke-width="10"
          ></el-progress>
        </div>
        <ul class="custom-legend" style="width:130px;">
          <li
            v-for="item in pieChartsData"
            :key="item.name"
            class="custom-legend-li"
          >
            <span
              class="custom-legend-li-icon"
              :style="{ background: color_list_dict[item.name] }"
            ></span>
            <span class="custom-legend-li-name">{{ item.name }}</span>
            <span class="custom-legend-li-value">{{ item.value }}GiB</span>
          </li>
        </ul>
      </div>
      <div class="memory-usage-rate-progress">
        <div
          v-for="(item, index) in use_list"
          :key="index"
          class="memory-usage-rate-progress-item"
        >
          <div class="memory-usage-rate-progress-title">
            <div class="memory-usage-rate-progress-name">{{ item.name }}</div>
            <div
              class="memory-usage-rate-progress-value"
              style="font-size:12px"
            >
              <span
                >{{ item.used_gpu_memory.toFixed(2) }}GiB/{{
                  item.gpu_memory.toFixed(2)
                }}GiB</span
              >
            </div>
          </div>
          <div>
            <el-progress
              :stroke-width="4"
              :percentage="20"
              :show-text="false"
              :text-inside="false"
            ></el-progress>
          </div>
        </div>
      </div>
    </div>
    <div v-else style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
      <img src="../../../../assets/images/noresult.png" style="width: 100px;"/>
      <div style="font-size: 14px;margin: 8px 8px 0 0;">暂无数据</div>
    </div>
  </div>
</template>
<script>
export default {
  props: ["gpu_info"],
  data() {
    return {
      percentage: 0,
      id: `SharedStorageEchartsCard_${+new Date()}`,
      noresult_url: require("@/assets/images/EmptyState.svg"),
      pieChartsData: [
        {
          name: "总量",
          value: 0
        },
        {
          name: "已用",
          value: 0
        }
      ],
      dict: {
        image_used: "总量",
        container_used: "已用"
      },
      color_list_dict: {
        已用: "#FE814E",
        总量: "#E8E8E8"
      },
      use_list: []
    };
  },
  watch: {
    gpu_info: {
      handler(newVal, oldVal) {
        this.initData();
      },
      deep: true
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      if (this.gpu_info) {
        this.percentage = this.gpu_info.gpu_memory
          ? (
              (this.gpu_info.used_gpu_memory / this.gpu_info.gpu_memory) *
              100
            ).toFixed(2)
          : 0;
        this.pieChartsData[0].value = this.gpu_info.gpu_memory
          ? this.gpu_info.gpu_memory.toFixed(2)
          : 0;
        this.pieChartsData[1].value = this.gpu_info.used_gpu_memory
          ? this.gpu_info.used_gpu_memory.toFixed(2)
          : 0;
        this.use_list = this.gpu_info.gpu_group_list.slice(0, 3);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.memory-usage-rate {
  margin-right: 10px;
  width: 47%;
  min-width: 520px;
  background-color: #fff;
  padding: 10px ;
  border: 1px solid var(--color-gray-200);
}

.memory-usage-rate-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}

.memory-usage-rate-content {
  display: flex;
}

.memory-usage-rate-chart {
  width: 55%;
  min-width: 250px;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  .custom-legend {
    margin-left: 30px;
    display: flex;
    flex-direction: column;
    justify-content: end;
    width: 80px;

    .custom-legend-li {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 5px;

      .custom-legend-li-icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 12px;
      }

      .custom-legend-li-name {
        margin-left: 5px;
        margin-right: 5px;
      }
    }
  }
}

.memory-usage-rate-progress {
  margin-top: 20px;
  width: 44%;
  flex: 1;
}

.memory-usage-rate-progress-item {
  margin-bottom: 16px;
}

.memory-usage-rate-progress-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
</style>
