<template>
  <div class="accelerator-card">
    <div class="accelerator-card-name">加速卡</div>
    <div class="accelerator-card-chart" v-if="gpu_info">
      <div class="pieCharts" style="width:140px;height: 140px;" :id="id"></div>
      <ul class="custom-legend" style="width:130px;">
        <li
          v-for="item in pieChartsData"
          :key="item.name"
          class="custom-legend-li"
        >
          <span
            class="custom-legend-li-icon"
            :style="{ background: color_list_dict[item.name] }"
          ></span>
          <span class="custom-legend-li-name">{{ item.name }}</span>
          <span class="custom-legend-li-name">{{ item.value }}</span>
        </li>
      </ul>
    </div>
    <div v-else style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
      <img src="../../../../assets/images/noresult.png" style="width: 100px;"/>
      <div style="font-size: 14px;margin: 8px 8px 0 0;">暂无数据</div>
    </div>
  </div>
</template>
<script>
export default {
  props: ["gpu_info"],
  data() {
    return {
      id: `AcceleratorCardEchartsCard_${+new Date()}`,
      pieChartsData: [],
      dict: {
        avail_mb: "NIVIDIA A200",
        image_used: "NIVIDIA A300",
        container_used: "NIVIDIA A100",
        other_used: "NIVIDIA A500"
      },
      id: `SharedStorageEchartsCard_${+new Date()}`,
      pieChart: null,
      color_list: ["#0474FF", "#FE814E", "#15C886", "#FFAE4C"],
      color_list_dict: {}
    };
  },
  watch: {
    gpu_info: {
      handler(newVal, oldVal) {
        this.initData();
        this.updateCharts();
        console.log(newVal, "gpu_info");
      },
      deep: true
    }
  },
  mounted() {
    console.log(this.gpu_info);
    this.initData();
    this.initCharts();
  },
  methods: {
    initData() {
      if (this.gpu_info && this.gpu_info.gpu_type_count) {
        this.pieChartsData = Object.keys(this.gpu_info.gpu_type_count).map(
          item => {
            return {
              name: item,
              value: this.gpu_info.gpu_type_count[item]
            };
          }
        );
        Object.keys(this.gpu_info.gpu_type_count).map((item, index) => {
          this.color_list_dict[item] = this.color_list[index];
        });
      }
    },
    initCharts() {
      console.log(this.$echarts);
      let domContainer = document.getElementById(this.id);
      if (domContainer) {
        this.pieChart = this.$echarts.init(domContainer);
        let option = this.creatSeries();
        this.pieChart.setOption(option, true);
      }
    },
    creatSeries() {
      let _this = this;
      let scale = 1;
      var rich = {
          yellow: {
            color: "#ffc72b",
            fontSize: 15 * scale,
            align: "center"
          },
          total: {
            color: "#ffc72b",
            fontSize: 40 * scale,
            align: "center"
          },

          blue: {
            color: "#49dff0",
            fontSize: 16 * scale,
            align: "center"
          },
          hr: {
            borderColor: "#0b5263",
            width: "100%",
            borderWidth: 1,
            height: 0
          }
        },
        option = {
          backgroundColor: "#fff",
          title: {
            show: true,
            text: this.gpu_info.gpu_count,
            subtext: "总数",
            left: "center",
            top: "35%",
            textStyle: {
              color: "#000",
              fontSize: 24
            },
            subtextStyle: {
              fontSize: 12,
              color: ["#26272A"]
            }
          },
          legend: {
            show: false,
            icon: "rect"
          },
          tooltip: {
            trigger: "item",
            position: "right",
            formatter: function(parms) {
              var str =
                parms.marker +
                "" +
                parms.data.name +
                "</br>" +
                "数量：" +
                parms.data.value +
                "</br>";
              return str;
            }
          },
          series: [
            {
              name: "缓存盘容量",
              stillShowZeroSum: true,
              type: "pie",
              itemStyle: {
                normal: {
                  borderWidth: 0,
                  borderColor: "#fff"
                }
              },
              radius: ["70%", "90%"],
              hoverAnimation: true,
              label: {
                normal: {
                  show: false,
                  formatter: function(params, ticket, callback) {
                    var total = 0;
                    var percent = 0;
                    _this.pieChartsData.forEach(function(value, index, array) {
                      total += value.value;
                    });
                    percent = ((params.value / total) * 100).toFixed(2);
                    return (
                      "{yellow|" + params.value + "}\n{blue|" + percent + "%}"
                    );
                  },
                  rich: rich
                }
              },
              labelLine: {
                normal: {
                  length: 55 * scale,
                  length2: 10,
                  lineStyle: {
                    color: "#0b5263"
                  },
                  show: false
                }
              },
              data: this.pieChartsData.map(item => {
                return {
                  ...item,
                  itemStyle: {
                    color: _this.color_list_dict[item.name]
                  }
                };
              })
            }
          ]
        };
      return option;
    },
    updateCharts() {
      if (!this.pieChart) {
        this.initCharts();
      }
      let option = this.creatSeries();
      this.pieChart.setOption(option);
    }
  }
};
</script>
<style lang="scss" scoped>
.accelerator-card {
  margin-right: 10px;
  width: 28%;
  background-color: #fff;
  padding: 10px ;
  border: 1px solid var(--color-gray-200);
}
.accelerator-card-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}
.accelerator-card-chart {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .custom-legend {
    display: flex;
    flex-direction: column;
    justify-content: end;
    width: 130px;
    .custom-legend-li {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-top: 5px;

      .custom-legend-li-icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 12px;
      }
      .custom-legend-li-name {
        margin-left: 5px;
      }
    }
  }
}
</style>
