<template>
  <sugon-dialog
    :isShow.sync="state"
    @confirm="confirm"
    width="1000"
    height="700"
    title="支持的加速卡型号"
    type="no-form"
    ref="dialog"
  >
  <cl-table-container
        name="compare-data-grop"
        model="flex"
        class="graphics-card-content-list"
      >
        <cl-table-body>
          <cl-table
            element-loading-text="加载中..."
            :data="table_list"
            :ref="`all_model_repository`"
            :columns="tableColunms"
          ></cl-table>
        </cl-table-body>
      </cl-table-container>
  </sugon-dialog>
</template>
  
  <script>
import * as modelHttp from "@/http/model-http/model-http";
export default {
  data() {
    return {
        state: false,
        key:+new Date(),
        table_list: [],
        gpu_info: null,
        tableColunms:[
        {
          prop: "type",
          label: "加速卡型号",
          render(h, scope) {
            return <span>{scope.row.type}</span>
          },
        },
        {
          prop: "id",
          label: "厂商",
          render(h, scope) {
            return <span>{scope.row.manufacturer}</span>
          },
        },
        {
          prop: "speed_card_memory",
          label: "显存",
          render(h, scope) {
            return <span>{scope.row.memory_mb}</span>
          },
        },
        {
          prop: "cpu_rate",
          label: "描述",
          'min-width': '400px',
          render(h, scope) {
            return <span>{scope.row.description}</span>
          },
        },
      ]
    };
  },
  methods: {
    open(row_data) {
      this.state = true;
      this.key = +new Date();
      this.gpu_info = row_data
      this.getList()
    },
    getList() {
      let params = {
        page_num: 1,
        page_size: 9999
      };
      this.table_loading = true;
      modelHttp
        .get_gpu_info(params)
        .then(res => {
          this.$handle_http_back(res, true, false);
          if (res.content) {
            this.table_list =  res.content.list;
          }
          this.table_loading = false;
        })
        .catch(e => {
          console.log(e);
          this.table_loading = false;
        });
    },
    confirm(response) {
      this.state = false;
      response.close();
    },
  },
};
</script>
  
  <style scoped lang="scss">
    >>> .sugon-dialog-box {
      height: 600px;
    }
    >>> .sugon-dialog-content {
      height: calc(100% - 150px);
    }
</style>