<template>
  <div class="computing-power-rate">
    <div class="computing-power-rate-name">算力使用率TOP3</div>
    <div class="computing-power-rate-content" v-if="gpu_info">
      <div class="computing-power-rate-progress">
        <div
          v-for="(item, index) in use_list"
          :key="index"
          class="computing-power-rate-progress-item"
        >
          <div class="computing-power-rate-progress-title">
            <div class="computing-power-rate-progress-name">
              {{ item.key }}
            </div>
            <div
              class="computing-power-rate-progress-value"
              style="font-size:12px"
            >
              <span>{{ item.value }}%</span>
            </div>
          </div>
          <div>
            <el-progress
              :stroke-width="4"
              :percentage="item.value"
              :show-text="false"
              :text-inside="false"
            ></el-progress>
          </div>
        </div>
      </div>
    </div>
    <div v-else style="display: flex;flex-direction: column;justify-content: center;align-items: center;">
      <img src="../../../../assets/images/noresult.png" style="width: 100px;"/>
      <div style="font-size: 14px;margin: 8px 8px 0 0;">暂无数据</div>
    </div>
  </div>
</template>
<script>
export default {
  props: ["gpu_info"],
  data() {
    return {
      use_list: {},
      noresult_url: require("@/assets/images/EmptyState.svg")
    };
  },
  watch: {
    gpu_info: {
      handler(newVal, oldVal) {
        console.log(newVal, "gpu_info");
        this.initData();
      },
      deep: true
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      if (this.gpu_info && this.gpu_info.top5_map) {
        let list = Object.keys(this.gpu_info.top5_map).map(item => {
          return {
            key: item,
            value: this.gpu_info.top5_map[item]
          };
        });
        list.sort((a, b) => {
          return b.value - a.value;
        });
        this.use_list = list.slice(0, 3);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.computing-power-rate {
  width: 30%;
  background-color: #fff;
  padding: 10px ;
  border: 1px solid var(--color-gray-200);
}
.computing-power-rate-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
}
.computing-power-rate-progress-item {
  margin-bottom: 16px;
}
.computing-power-rate-progress-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}
</style>
