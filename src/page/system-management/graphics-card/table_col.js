export default function(vueApp) {
  let $store = vueApp.$store;
  let $route = vueApp.$route;
  let $router = vueApp.$router;
  let $moment = vueApp.$moment;

  let { operateStrategy } = vueApp;
  let { dateformat } = vueApp.$options.filters;
  return [
    {
      // "label": "绑定",
      type: "expand",
      render: (h, scope) => {
        return (
          <div class="bindListContainer">
            <el-table data={scope.row.gpu_info_vos}>
              <el-table-column label="设备id" prop="id">
                {({ row }) => <span>{row.id}</span>}
              </el-table-column>
              <el-table-column label="加速卡型号" prop="name">
                {({ row }) => <span>{row.name}</span>}
              </el-table-column>
              <el-table-column label="所属节点" prop="node_name">
                {({ row }) => <span>{row.node_name}</span>}
              </el-table-column>
              <el-table-column label="显存使用率" prop="memory_rate">
                {({ row }) => (
                  <span>
                    {row.memory_rate == null ? "--" : row.memory_rate.toFixed(2) + "%"}
                  </span>
                )}
              </el-table-column>
              <el-table-column label="算力使用率" prop="gpu_rate">
                {({ row }) => (
                  <span>
                    {row.gpu_rate == null ? "--" : row.gpu_rate.toFixed(2) + "%"}
                  </span>
                )}
              </el-table-column>
              <el-table-column label="芯片频率" prop="chip_freq">
                {({ row }) => (
                  <span>{row.chip_freq == null ? "--" : row.chip_freq + ' MHz'}</span>
                )}
              </el-table-column>
            </el-table>
          </div>
        );
      }
    },
    {
      label: "加速卡型号",
      render: (h, scope) => {
        return <span>{scope.row.name}</span>;
      }
    },
    {
      label: "加速卡总数",
      render: (h, scope) => {
        return <span>{scope.row.count == null ? "--" : scope.row.count}</span>;
      }
    },
    {
      label: "加速卡使用数",
      render: (h, scope) => {
        return <span>{scope.row.used == null ? "--" : scope.row.used}</span>;
      }
    },
    {
      label: "所属节点",
      render: (h, scope) => {
        return (
          <span>
            {scope.row.node_name == null ? "--" : scope.row.node_name}
          </span>
        );
      }
    },
    {
      label: "显存平均使用率",
      render: (h, scope) => {
        return (
          <span>
            {scope.row.avg_memory_rate == null
              ? "--"
              : scope.row.avg_memory_rate.toFixed(2) + "%"}
          </span>
        );
      }
    },
    {
      label: "算力使用率（最高）",
      render: (h, scope) => {
        return (
          <span>
            {scope.row.top1_rate == null
              ? "--"
              : scope.row.top1_rate.toFixed(2) + "%"}
          </span>
        );
      }
    }
    // {
    //   label: "芯片频率",
    //   render: (h, scope) => {
    //     return <span>{scope.row.name}</span>;
    //   }
    // }
  ];
}
