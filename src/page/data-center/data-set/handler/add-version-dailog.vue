<template>
    <sugon-dialog :isShow.sync="state" @confirm="confirm" width="1000" :title="'新建数据集'" @close="handleClose">
        <div class="sugon-dialog-out-padding">
            <el-form ref="form" :model="form" label-width="100px" :rules="rules" label-position="right">
                <!-- <el-form-item label="版本号" prop="versionName">
                    <div>{{form.versionName}}</div>
                </el-form-item> -->
                <el-form-item label="数据集类型" prop="schema_type">
                      <div>{{form.schema_type?schema_type_dict[form.schema_type]:'--'}}</div>
                </el-form-item>
                <el-form-item label="存储位置" prop="store_way">
                    <span>{{ form.store_way?$getCode({type:'store_way',code:form.store_way})? $getCode({type:'store_way',code:form.store_way}).name:'':''}}</span>
                </el-form-item>
                <el-form-item label="导入方式">
                    本地上传
                </el-form-item>
                <el-form-item label="立即上传" prop="file_list">
                    <customUpload ref="customUpload" @fileChange="fileChange" @uploadFileSuccessChange="fileChange" url="sugoncloud-xiaolian-api/files/dataset/files/upload"></customUpload>
                </el-form-item>
                <el-form-item prop="description" label="描述">
                    <el-input type="textarea" :row="2" style="width:100%" v-model="form.description"></el-input>
                </el-form-item>
            </el-form>
        </div>
    </sugon-dialog>
</template>

<script>
import { get_codes_by_type } from '@/http/dict-http'
import customUpload from '@/page/data-center/data-set/component/upload.vue'
import * as dataCenterHttp from '@/http/data-center'
export default {
    name: 'data-set-add-edit-dialog',
    components:{
        customUpload
    },
    data() {
       const check_file_list =(rule, value, callback) =>{
        if(value.length==0){
            return callback(new Error('请先导入文件'));
        };
       
        let ready = value.filter(item =>item.status=='ready');//全部未上传
        if(ready.length==value.length){
            return callback(new Error('请点击上传文件'));
        };
        let error = value.filter(item =>item.status=='error');//全部上传失败
        if(error.length==value.length){
            return callback(new Error('请重新上传文件'));
        };
        let success = value.filter(item =>item.status=='success');//有上传成功的文件
        if(success.length==0){
            return callback(new Error('至少有一个上传成功的文件'));
        };
        return callback();
       }
        return {
            state: false,
            form: {
                versionName: '',
                schema_type: 'SFT',//数据格式
                position: 'platform',
                importType: 'localhost',
                description: '',
                scene: '',
                file_list:[],
                store_way:''

            },
            rules: {
                name: [
                    { required: true, message: '请输入名称', trigger: 'blur' },
                    { min: 2, max: 64, message: '长度在 2 到 64 个字符', trigger: 'blur' }
                ],
                schema_type: [
                    {
                        required: true,
                        trigger: 'blur'
                    }
                ],
                file_list:[
                    {
                        required:true,
                        message:'请先导入文件',
                        trigger:'blur'
                    },
                    {
                        validator:check_file_list
                    }
                   
                ]
            },
            model_scene_list: [],
            store_way_list:[],
            schema_type_dict:{
                "SFT":'SFT-文本生成',
                "DPO":'DPO-文本生成',
                "CPT":'CPT-文本生成'
            }
        }
    },
    methods: {
        open(data) {
            if (data) {
               let v_number = 1;
               if(data.ai_data_set_version_vo_list){
                let length = data.ai_data_set_version_vo_list.length;
                v_number = v_number+length;
               }
               this.form.versionName = `V${v_number}`;
               this.form.store_way = data.store_way;
               this.form.id =data.id;
               this.form.schema_type =data.schema_type;
               this.form.project_id =data.project_id;
            }
            this.state = true;
            this.getCodesByType('model_scene');
        },
        confirm(response) {//如果表单有校验，会自动校验。不需另外写element的校验函数。response返回有按钮loading关闭函数
           
            let file_list = this.$refs.customUpload.getFileParams();
            let params ={
                files:file_list,
                //version_name:this.form.versionName,
                data_set_id:this.form.id,
                project_id:this.form.project_id

            };
            
            dataCenterHttp.add_data_set_version(params).then((res) => {
                this.$handle_http_back(res, true, false)
                response.close();
                if(res.success){
                    this.$emit('success')
                    this.handleClose();
                }
              
            }).catch((err) => {
                console.error(err)
                response.close()
            })
        },
        getCodesByType(type) {
              let name =`${type}_list`
            get_codes_by_type({
                type: type
            }).then(res => {
              
                this[name] = [];
                if (res.content) {
                    this[name] = res.content ? res.content : [];
                    let dict ={
                        'model_scene':'scene',
                        
                    }                    
                    let form_key_name =dict[type]?dict[type]:type;
                    if ( this[name].length !== 0) {
                        if ([undefined, null, ''].includes( this.form[form_key_name])) {
                            this.form[form_key_name] =this[name][0].code;
                        }
                    }

                }
            }).catch(_ => {
                this[name] = [];
            })
        },
        fileChange(fileList){
            //成功的文件
            this.form.file_list =fileList;
            this.$refs.form.validate('file_list')
        },
        handleClose(){
            Object.assign(this.$data, this.$options.data());
            this.state =false;
        }

    }
}
</script>

<style></style>