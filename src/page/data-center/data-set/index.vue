<template>
  <cl-page-container
    title=""
    explain=""
    :hideHead="true"
    class="data-set-cloud-page-container"
  >
    <bread name="数据集" description="统一管理数据集，支持多版本管理，可在模型调优中使用数据集。"></bread>
    <cl-table-container name="tableName" model="flex">
      <cl-table-header
        @search="tableSearch"
        :rightHandles="rightHandles"
        :table_loading="table_loading"
        :page_num="page_num"
        :page_size="page_size"
        @reset="reset"
      >
        <template slot="left">
          <div class="table-tool-bar-left">
            <cl-button
              type="primary"
              icon="el-icon-plus"
              @click="openDialog('', 'add')"
            >
              新建
            </cl-button>
            <cl-button
              type="danger"
              icon="el-icon-delete"
              :disabled="select_list.length === 0"
              @click="openDialog('', 'delBatchDialog')"
            >
              批量删除
            </cl-button>
          </div>
        </template>
      </cl-table-header>
      <cl-table-body>
        <cl-table
          height="100%"
          ref="table_dom"
          :data="table_list"
          v-loading="table_loading"
          element-loading-text="加载中..."
          @selection-change="tableSelect"
          @filter-change="filterChange"
          :columns="table_colunms"
        ></cl-table>
      </cl-table-body>
      <cl-table-footer>
        <cl-pagination
          background
          v-if="table_list.length !== 0"
          @size-change="pageSizeChange"
          @current-change="pageChange"
          :current-page="page_num"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page_size"
          layout="sizes, total, jumper, next, pager, prev"
          :total="page_total"
        ></cl-pagination>
      </cl-table-footer>
    </cl-table-container>
    <!--新增数据集弹窗-->
    <addEditDialog ref="addEditDialog" @success="getList"></addEditDialog>
    <!-- 新增数据集版本 -->
    <addVersionDailog
      ref="addVersionDailog"
      @success="getList"
    ></addVersionDailog>
    <!-- 删除数据集弹窗 -->
    <sugon-delete-dialog
      :list="select_list"
      :isShow.sync="dialog_del"
      @confirm="handelClickDelete"
      :title="`${select_list.length > 1 ? '删除' : '删除'}`"
      nameKey="name"
      idKey="id"
    >
    </sugon-delete-dialog>
  </cl-page-container>
</template>
<script>
import columns from "./table-config";
import * as dataCenterHttp from "@/http/data-center";
import addEditDialog from "./handler/add-edit-dialog.vue";
import addVersionDailog from "./handler/add-version-dailog.vue";
import bread from '@/components/bread/index.vue';
export default {
  name: "dataSet",
  components: {
    addEditDialog,
    addVersionDailog,
    bread
  },
  data() {
    return {
      active_name: "all",
      table_loading: false,
      page_num: 1,
      page_size: 10,
      select_list: [],
      table_list: [],
      table_colunms: [],
      page_total: 0,
      dialog_del: false,
      search_value: "",
      schema_type_list: [
        {
          label: "SFT-文本生成",
          prop: "SFT"
        },
        {
          label: "DPO-文本生成",
          prop: "DPO"
        },
        {
          label: "CPT-文本生成",
          prop: "CPT"
        }
      ],
      schema_type: "",
      rightHandles: [
        "setBtn",
        () =>(
            <el-select
            class="header-right-selset"
            value={this.active_name}
            placeholder="请选择"
            onChange={this.handleClick}        
            style={{ "margin-right": "10px","width":"140px" }}
          >
            {[{label:'全部',prop:'all'},{label:'我的',prop:'mine'}].map(item => (
              <el-option label={item.label} value={item.prop}></el-option>
            ))}
          </el-select> 
        ),
        () => (
          <el-select
            class="header-right-selset"
            value={this.schema_type}
            placeholder="请选择数据集类型"
            onChange={this.schemaTypeChange}
            clearable
            style={{ "margin-right": "10px","width":"140px" }}
          >
            {this.schema_type_list.map(item => (
              <el-option label={item.label} value={item.prop}></el-option>
            ))}
          </el-select>
        ),
        () => (
          <el-input
            value={this.search_value}
            placeholder="搜索（数据集）"
            onChange={this.getList}
            onInput={this.searchValueChange}
            style="width: 208px;"
          />
        ),
        () => <cl-button onClick={this.getList}>搜索</cl-button>,

        () => <cl-button onClick={this.reset}> 重置</cl-button>,
        "search"
      ],
      project_id: localStorage.getItem("ProjectId") || '',
    };
  },
  mounted() {
    this.setColumns();
    this.getList();
  },
  methods: {
    tableSearch(res) {
      this.search_value = res.value;
      this.page_num = res.page_num;
      this.page_size = res.page_size;
      if (this.search_value == "") {
        this.schema_type = "";
      }
      this.getList();
    },
    getList() {
      let params = {
        pageNum: this.page_num,
        pageSize: this.page_size,
        name: this.search_value,
        projectId:this.project_id
      };
      if (this.active_name == "mine") {
        params.create_by = window.GetUserInfo().username;
      }
      if (this.schema_type) {
        params.schemaType = this.schema_type;
      }
      this.table_loading = true;
      dataCenterHttp
        .get_data_set_list(params)
        .then(res => {
          if (res.content) {
            this.page_total = res.content.total ? res.content.total : 0;
            this.table_list = res.content.list ? res.content.list : [];
          } else {
            this.page_total = 0;
            this.table_list = [];
          }
          this.table_loading = false;
        })
        .catch(_ => {
          this.page_total = 0;
          this.table_list = [];
          this.table_loading = false;
        });
    },
    openDialog(item, type) {
      switch (type) {
        case "delDialog":
          this.select_list = [item];
          this.$table_select_repeat(
            this.select_list,
            this.table_list,
            "table_dom"
          );
          this.dialog_del = true;
          break;
        case "add":
          this.$refs.addEditDialog.open();
          break;
        case "addVersionDialog":
          this.$refs.addVersionDailog.open(item);
          break;
        case "delBatchDialog":
          this.dialog_del = true;
          break;
      }
    },
    tableSelect(res) {
      this.select_list = res;
    },
    pageSizeChange(size) {
      this.page_size = size;
    },
    pageChange(page) {
      this.page_num = page;
    },
    handelClickDelete(response) {
      let value = response.data.map(el => {
        return {
          name: el.name,
          request: dataCenterHttp.del_data_set_by_id,
          params: el.id
        };
      });
      const that = this;
      const batch_delete = new this.$batch_delete({
        data: value,
        title: "删除提示",
        success: function() {
          that.dialog_del = false;
          that.getList();
        },
        error: function(err) {
          that.dialog_del = false;
          //that.getList();
        }
      });
      /**发送请求 */
      batch_delete.del();
    },
    setColumns() {
      let _this = this;
      this.table_colunms = [
        ...columns(this),
        {
          label: "操作",
          width: "60",
          resizable: false,
          render: (h, scope) => {
            return (
              <el-dropdown size="small" placement="bottom" trigger="click">
                <cl-button icon="el-icon-setting" title="操作"></cl-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    onClick={() => {
                      _this.openDialog(scope.row, "addVersionDialog");
                    }}
                  >
                    新建版本
                  </el-dropdown-item>
                  {/* <el-dropdown-item onClick={() => { _this.openDialog(scope.row, 'delDialog') }}>导出</el-dropdown-item> */}
                  <el-dropdown-item
                    onClick={() => {
                      _this.openDialog(scope.row, "delDialog");
                    }}
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            );
          }
        }
      ];
    },
    gotoDetail(row) {
      this.$go_detail(this, `/dataSet/detail?id=${row.id}&name=${row.name}`, {
        page_size: this.page_size,
        page_num: this.page_num
      });
    },
    handleClick(val) {
      this.active_name=val;
      this.getList();
    },
    searchValueChange(val) {
      this.search_value = val;
    },
    schemaTypeChange(val) {
      this.schema_type = val;
      this.getList();
    },
    typeFilter: function(val) {
      return val === "导入完成" ? "success" : "error";
    },
    reset() {
      this.search_value = "";
      this.schema_type = "";
      this.page_num = 1;
      this.page_size = 10;
      this.active_name='all'
      this.getList();
    }
  }
};
</script>
<style lang="scss">
.data-set-cloud-page-container {
  width: 100%;
  height: 100%;
  .cloud-table-header .cloud-table-header-right .el-select .el-input {
    width: auto;
  }
}
</style>
