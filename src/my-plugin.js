
export default {
    install(Vue){        
        Vue.prototype.$getCode = (params = {type:'',code:''})=>{
            if(params.type && params.code){
                let arr = AppStore.state.aiCodes.filter((item)=>item.type == params.type)
                return arr.find((item)=>item.code == params.code)
            }else if(params.type){
              return  AppStore.state.aiCodes.filter((item)=>item.type == params.type).sort((a, b) => a.sort_num - b.sort_num);
            }else if(params.code){
                return AppStore.state.aiCodes.find((item)=>item.code == params.code)
            }else{
                return AppStore.state.aiCodes
            }
        }
    }
}