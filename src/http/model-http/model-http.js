import { get, post, put, del, delData } from "@/http/base-http/base-http";
import { static_name } from "@/utils";

export const get_models = data =>
  get(`/${static_name.api_path}/sugoncloud-xiaolian-api/model`, data);

// 查询所有训练任务返回分页
export const get_training_job = data =>
  get(`/${static_name.api_path}/sugoncloud-xiaolian-api/training-job`, data);

// 提交训练任务
export const post_training_job = data =>
  post(`/${static_name.api_path}/sugoncloud-xiaolian-api/training-job`, data);

// 根据ID查询训练任务
export const get_training_job_detail = id =>
  post(`/${static_name.api_path}/sugoncloud-xiaolian-api/training-job/${id}`);

// 更新训练任务
export const update_training_job = id =>
  put(`/${static_name.api_path}/sugoncloud-xiaolian-api/training-job/${id}`);

// 删除任务
export const del_training_job = id =>
  del(`/${static_name.api_path}/sugoncloud-xiaolian-api/training-job/${id}`);

//终止训练任务
export const terminate_training_job = id =>
  put(
    `/${static_name.api_path}/sugoncloud-xiaolian-api/training-job/${id}/terminate`
  );

// 查询所有部署模型返回分页
export const get_deployment_list = data =>
  get(`/${static_name.api_path}/sugoncloud-xiaolian-api/deployment/list`, data);

// 提交模型部署
export const post_deployment = data =>
  post(`/${static_name.api_path}/sugoncloud-xiaolian-api/deployment`, data);

// 根据ID查询部署模型
export const get_deployment_detail = id =>
  post(`/${static_name.api_path}/sugoncloud-xiaolian-api/deployment/${id}`);

// 删除推理服务
export const del_deployment = id =>
  del(`/${static_name.api_path}/sugoncloud-xiaolian-api/deployment/${id}`);

//下线推理服务
export const offline_deployment = id =>
  put(
    `/${static_name.api_path}/sugoncloud-xiaolian-api/deployment/${id}/offline`
  );

//获取策略列表

export const get_codes = data =>
  get(`/${static_name.api_path}/sugoncloud-xiaolian-api/api/codes`, data);

// 获取gpu信息 列表
export const get_gpu_info = data =>
  get(`/${static_name.api_path}/sugoncloud-xiaolian-api/gpu/info`, data);

//模型仓库列表
export const get_model_repository = data =>
  get(
    `/${static_name.api_path}/sugoncloud-xiaolian-api/model-repository`,
    data
  );

//根据规格以及和模型，计算该模型最多能部署几个副本
export const get_available_replicas = data =>
  get(
    `/${static_name.api_path}/sugoncloud-xiaolian-api/node/info/available/replicas`,
    data
  );

//超参列表
export const get_hyper_parameters = data =>
  get(
    `/${static_name.api_path}/sugoncloud-xiaolian-api/hyper-parameters`,
    data
  );

//删除模型仓库
export const del_model_repository_by_id = model_id =>
  del(
    `/${static_name.api_path}/sugoncloud-xiaolian-api/model-repository/${model_id}`
  );

// 集群管理列表
export const get_cluster_list = projectId =>
  get(
    `/${static_name.api_path}/sugoncloud-xiaolian-api/cluster/project?project_id=${projectId}`
  );

//获取预置模型列表
export const get_model =
(data)=>get(`/${static_name.api_path}/sugoncloud-xiaolian-api/model`,data)