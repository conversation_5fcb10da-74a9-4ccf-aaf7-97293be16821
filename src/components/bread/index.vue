<template>
  <div class="custom-bread">
    <div class="custom-bread-name">{{name}}</div>
    <div class="custom-bread-description">{{description}}</div>
    <div class="custom-bread-gap"></div>
  </div>
</template>
<script>
export default {
  props: ['name', 'description'],
  data() {
    return {
      
    }
  },
  mounted() {
    document.getElementsByClassName('cloud-table-container-flex')[0].style.height = 'calc(100% - 74px)'
  },
}
</script>
<style lang="scss" scoped>
  .custom-bread {
	.custom-bread-gap {
		height: 10px;
		width: 100%;
		background: #f5f6f9
	}
	.custom-bread-name {
		padding: 8px 0 0px 20px;
		font-size: 14px;
		font-weight: bold;
	}
	.custom-bread-description {
		padding: 3px 0 8px 20px;
		font-size: 12px;
		font-weight: 400;
	}
}
</style>