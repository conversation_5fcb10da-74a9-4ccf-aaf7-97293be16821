<template>
    <div v-html="md"></div>
</template>

<script>
import MarkdownIt from "markdown-it";
import MarkdownItAbbr from "markdown-it-abbr";
import MarkdownItAnchor from "markdown-it-anchor";
import MarkdownItDeflist from "markdown-it-deflist";
import MarkdownItEmoji from "markdown-it-emoji";
import MarkdownItFootnote from "markdown-it-footnote";
import MarkdownItHighlightjs from "markdown-it-highlightjs";
import MarkdownItIns from "markdown-it-ins";
import MarkdownItMark from "markdown-it-mark";
import MarkdownItSub from "markdown-it-sub";
import MarkdownItSup from "markdown-it-sup";
import MarkdownItTasklists from "markdown-it-task-lists";
import MarkdownItTOC from "markdown-it-toc-done-right";
import mk from "markdown-it-katex";
export default {
  props: {
    anchor: {
      type: Object,
      default: () => ({}),
    }, // 添加逗号
    breaks: {
      type: Boolean,
      default: false,
    },
    emoji: {
      type: Object,
      default: () => ({}),
    }, // 添加逗号
    highlight: {
      type: Object,
      default: () => ({}),
    }, // 添加逗号
    html: {
      type: Boolean,
      default: false,
    },
    langPrefix: {
      type: String,
      default: "language-",
    },
    linkify: {
      type: Boolean,
      default: false,
    },
    plugins: {
      type: Array,
      default: () => [],
    },
    quotes: {
      type: String,
      default: "“”‘’",
    },
    source: {
      type: String,
      default: "",
    },
    tasklists: {
      type: Object,
      default: () => ({}),
    }, // 添加逗号
    toc: {
      type: Object,
      default: () => ({}),
    }, // 添加逗号
    typographer: {
      type: Boolean,
      default: false,
    },
    xhtmlOut: {
      type: Boolean,
      default: false,
    },
  },
  data(){
    return {
        md:''
    }
  },
  mounted() {
    this.renderMarkdown();
  },
  watch:{
    source(newVal,oldVal){
      this.renderMarkdown();
    }
  },
  methods:{
    renderMarkdown(){
      let markdown = new MarkdownIt()
        .use(mk)
        .use(MarkdownItAbbr)
        .use(MarkdownItAnchor, this.anchor)
        .use(MarkdownItDeflist)
        .use(MarkdownItEmoji, this.emoji)
        .use(MarkdownItFootnote)
        .use(MarkdownItHighlightjs, this.highlight)
        .use(MarkdownItIns)
        .use(MarkdownItMark)
        .use(MarkdownItSub)
        .use(MarkdownItSup)
        .use(MarkdownItTasklists, this.tasklists)
        .use(MarkdownItTOC, this.toc)
        .set({
          breaks: this.breaks,
          html: this.html,
          langPrefix: this.langPrefix,
          linkify: this.linkify,
          quotes: this.quotes,
          typographer: this.typographer,
          xhtmlOut: this.xhtmlOut,
        });

        this.plugins.forEach(({ plugin, options = {} }) => {
        markdown.use(plugin, options);
      });

      let text = this.source.replace(/\\\[/g, '$').replace(/\\\]/g, '$');
      text = text.replace(/\\\(\s/g, '$').replace(/\s\\\)/g, '$');
      this.md = markdown.render(text);
    }
  }
};
</script>

<style>
</style>