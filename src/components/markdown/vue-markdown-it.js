import { h, onMounted, onUpdated, ref } from 'vue';

import MarkdownIt from 'markdown-it';
import MarkdownItAbbr from 'markdown-it-abbr';
import MarkdownItAnchor from 'markdown-it-anchor';
import MarkdownItDeflist from 'markdown-it-deflist';
import MarkdownItEmoji from 'markdown-it-emoji';
import MarkdownItFootnote from 'markdown-it-footnote';
import MarkdownItHighlightjs from 'markdown-it-highlightjs';
import MarkdownItIns from 'markdown-it-ins';
import MarkdownItMark from 'markdown-it-mark';
import MarkdownItSub from 'markdown-it-sub';
import MarkdownItSup from 'markdown-it-sup';
import MarkdownItTasklists from 'markdown-it-task-lists';
import MarkdownItTOC from 'markdown-it-toc-done-right';
import mk from 'markdown-it-katex';

const props = {
  anchor: {
    type: Object,
    default: () => ({}),
  }, // 添加逗号
  breaks: {
    type: Boolean,
    default: false,
  },
  emoji: {
    type: Object,
    default: () => ({}),
  }, // 添加逗号
  highlight: {
    type: Object,
    default: () => ({}),
  }, // 添加逗号
  html: {
    type: Boolean,
    default: false,
  },
  langPrefix: {
    type: String,
    default: 'language-',
  },
  linkify: {
    type: Boolean,
    default: false,
  },
  plugins: {
    type: Array,
    default: () => [],
  },
  quotes: {
    type: String,
    default: '“”‘’',
  },
  source: {
    type: String,
    default: '',
  },
  tasklists: {
    type: Object,
    default: () => ({}),
  }, // 添加逗号
  toc: {
    type: Object,
    default: () => ({}),
  }, // 添加逗号
  typographer: {
    type: Boolean,
    default: false,
  },
  xhtmlOut: {
    type: Boolean,
    default: false,
  },
};

export default {
  name: 'vue3-markdown-it',
  props,
  setup(props) {
    const md = ref();
    const renderMarkdown = () => {
      let markdown = new MarkdownIt()
        .use(mk)
        .use(MarkdownItAbbr)
        .use(MarkdownItAnchor, props.anchor)
        .use(MarkdownItDeflist)
        .use(MarkdownItEmoji, props.emoji)
        .use(MarkdownItFootnote)
        .use(MarkdownItHighlightjs, props.highlight)
        .use(MarkdownItIns)
        .use(MarkdownItMark)
        .use(MarkdownItSub)
        .use(MarkdownItSup)
        .use(MarkdownItTasklists, props.tasklists)
        .use(MarkdownItTOC, props.toc)
        .set({
          breaks: props.breaks,
          html: props.html,
          langPrefix: props.langPrefix,
          linkify: props.linkify,
          quotes: props.quotes,
          typographer: props.typographer,
          xhtmlOut: props.xhtmlOut,
        });

      props.plugins.forEach(({ plugin, options = {} }) => {
        markdown.use(plugin, options);
      });
      let text = props.source.replace(/\\\[/g, '$').replace(/\\\]/g, '$');
      text = text.replace(/\\\(\s/g, '$').replace(/\s\\\)/g, '$');
      md.value = markdown.render(text);
    };

    onMounted(() => renderMarkdown());
    onUpdated(() => renderMarkdown());

    return () => h('div', { innerHTML: md.value });
  },
};
