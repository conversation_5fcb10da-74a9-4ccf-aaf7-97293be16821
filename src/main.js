
import {createRouter} from '@/router'
import {routerInterceptor} from '@/router'
import {interceptor} from './interceptor'
import store from '@/store'
// import VueHighlightJS from 'vue-highlightjs'
// import ModelExperience from '@/page/model-center/model-experience'
// import './assets/css/layout.css'

// import './assets/main.scss';
import './assets/css/layout.css'
import myPlugin from './my-plugin'


initVue.check(['CloudApplicationMainCliGlobal','cloudBaseHttp'],()=>{
  // CloudApplicationMainCliGlobal.setVue('prototype',{name:'$selfPlugin',value:selfPlugin})
  CloudApplicationMainCliGlobal.setVue('use',myPlugin)
  CloudApplicationMainCliGlobal.setVue('use',window.vueQuill)
  CloudApplicationMainCliGlobal.setVue('prototype',{name:'$echarts',value:echarts})
  const cloudStore = CloudApplicationMainCliGlobal.createStore(store)
  const cloudRouter = CloudApplicationMainCliGlobal.createRouter({
    router: createRouter(),
    // blank:['/testChat'],
    // fullScreen:[
    //   {path:'/testChat',name:'testChat',component:ModelExperience}
    // ],
    interceptor: function(to, from, next, router) {
      routerInterceptor(to, from, next, router)
    }
  })
  CloudApplicationMainCliGlobal.createInterceptor(interceptor)
  // AppServerBlank.push('sugoncloud-xiaolian-api')
  var defaultConfig = {
    store: cloudStore,
    router: cloudRouter,
    AppName: '霄练AI平台',
    AppServer:'sugoncloud-xiaolian-api'
  }
  CloudApplicationMainCliGlobal.create(defaultConfig)
})
