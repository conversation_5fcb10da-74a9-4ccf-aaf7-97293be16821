'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.
const ip = require('ip').address();
const path = require('path')
module.exports = {
  dev: {

    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/api_server': {
        target: 'http://***********:30502/', // 双流
        // target: 'http://*************:30502/',
        changeOrigin: true,
        pathRewrite: {
          '^/api_server': ''
        }
      },
      '/mock': {
        target: 'http://localhost:9500/',  // 双流测试环境
        changeOrigin: true,
      },
      '/global':{
        // target: 'http://***********:30512/', // target host
        target: 'https://***********:30000/global',
        secure:false,
        // target: 'http://**********:30512',
        // target: 'http://**********:30512',
        //target: 'http://localhost:8500', // target host
        changeOrigin: true, // needed for virtual hosted sites
        ws: true, // proxy websockets
        pathRewrite: {
          '^/global': '', // rewrite path
        },
      },
      '/testChat': {
        target: 'http://***********:30502/sugoncloud-wxdl-api',
        // target: 'http://************:8077', // 双流
        // target: 'http://*************:30502/',
        changeOrigin: true,
        pathRewrite: {
          '^/testChat': ''
        }
      },
      '/api': {
        target: 'https://***********:30000/api',
        // target: 'https://**********:30000/api',
        // target: 'http://**********:30502',
        //  target: 'http://**********:30502',
        // target: 'http://**********:30502',
        // target: 'http://***********:30502',
        // target: 'http://**********:30502',
        // target: 'http://***********:30502/', // target host
        changeOrigin: true,
        secure:false,
        pathRewrite: {
          '^/api': '', // rewrite path
        },
      },
      timeout:1000*60*30
    },

    // Various Dev Server settingsr
    host: 'localhost', // can be overwritten by process.env.HOST
    port: 8085, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-


    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-module-eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    assetsPublicPath: './',

    /**
     * Source Maps
     */

    productionSourceMap: true,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: '#source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  }
}
