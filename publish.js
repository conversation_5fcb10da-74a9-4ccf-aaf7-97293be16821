const fs = require('fs');
const tar = require('tar');
const http = require('http');
const axios = require('axios');
const { exec } = require('child_process');

const distName = 'dist'
const outputName = 'xiaolian'
const http_url = `http://************:3000`


const folderPath = `./${distName}`;
const outputPath = `./${outputName}.tar.gz`;

exec('git log -1 --pretty=format:"%H %an %ae %s"', async (error, stdout, stderr) => {
    if (error) {
      console.error(`Error executing git command: ${error}`);
      return;
    }
    // 解析输出
    const [commitSha, authorName, authorEmail, message] = stdout.trim().split(' ');

    console.log(`Latest commit: ${commitSha}`);
    console.log(`Author: ${authorName} <${authorEmail}>`);
    console.log(`Message: ${message}`);
    console.log('文件压缩完成.');
    let commitText = `${commitSha} ${authorName} ${authorEmail} ${message}`;
    fs.writeFile(`./${distName}/commit.txt`, commitText, async (err) => {
        if (err) {
          console.error(`Error writing to file: ${err}`);
          return;
        }

        console.log(`commit写入成功`);
        fs.readdir(folderPath, (err, files) => {
            if (err) {
              console.error('Error reading directory:', err);
              return;
            }

            // 创建压缩流
            const compressStream = tar.c({
              gzip: true,
              file: outputPath,
              cwd: folderPath,
            }, files,async (err) => {
              if (err) {
                console.error('Error compressing folder:', err);
              } else {
                let branchRes = await httpGet(`${http_url}/branch`);
                let branchList = branchRes.state?branchRes.data:[]
                exec('git rev-parse --abbrev-ref HEAD', async (error, stdout, stderr) => {
                    const branchName = stdout.trim();
                    if(branchList.indexOf(branchName) == -1){
                        console.log(`当前分支${branchName}不在发布分支列表中`);
                    }else{
                        upload({user:authorName,branch:branchName,commit:message.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, ''),commitSha})
                    }
                })
              }
            });
        });
    });

});




function upload(config){
    const outputPath = `./${outputName}.tar.gz`;

    // 创建读取文件的流
    const readStream = fs.createReadStream(outputPath);

    // 获取文件大小
    const fileSize = fs.statSync(outputPath).size;
    console.log(`File size: ${fileSize} bytes`)

    // 创建 HTTP POST 请求
    let id = new Date().getTime();
    const req = http.request({
    hostname: '************',
    port: 3000,
    path: `/upload?name=${outputName}&id=${id}&user=${encodeURIComponent(config.user)}&branch=${encodeURIComponent(config.branch)}&commit=${encodeURIComponent(config.commit)}&commitSha=${encodeURIComponent(config.commitSha)}`,
    method: 'POST',
    headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Length': fileSize
    }
    }, (res) => {
        let data = '';
        res.on('data', chunk => {
            data += chunk;
        });
        res.on('end', () => {
            console.log(`Server response: ${data}`);
            getMessage(id)
            req.end();
        });

    });

    // 完成上传
    readStream.pipe(req);

    // 确保请求结束
    // req.end();

}
const clearLine = '\x1B[2K';
// 移动光标到第 5 行
const moveToLine = '\x1B[5G';

// 打印固定位置的信息
function printFixed(message) {
  process.stdout.write(clearLine + moveToLine + message);
}

function getMessage(id){
    let time = ''
    let lastText = ''
    let isLog = []
    time = setInterval(()=>{
        axios.get(`${http_url}/message?id=${id}`).then(res=>{
            let logArr = res.data
            for(var item of logArr){
                if(isLog.indexOf(item) == -1){
                    console.log(item)
                    lastText = item
                    isLog.push(item)
                }
            }
            if(lastText == '发布成功' || lastText == '未查询到该流程信息' || lastText.indexOf('Error') !== -1){
                fs.unlinkSync(`./${outputName}.tar.gz`);
                clearInterval(time)
            }
        })
    },1500)
}
function httpGet(url){
    return new Promise((resolve,reject)=>{
        axios.get(url).then(res=>{
            resolve(res.data)
        }).catch(err=>{
            reject(err)
        })
    })
}

